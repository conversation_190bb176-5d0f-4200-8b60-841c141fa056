<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/content_background">

    <!-- Camera Preview -->
    <androidx.camera.view.PreviewView
        android:id="@+id/camera_preview"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerInParent="true" />

    <!-- Detection Overlay -->
    <ImageView
        android:id="@+id/detection_overlay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerInParent="true"
        android:scaleType="matrix"
        android:visibility="gone" />

    <!-- Top Control Panel -->
    <LinearLayout
        android:id="@+id/top_panel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:background="@color/control_button_background"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical">

        <!-- Back Button -->
        <ImageButton
            android:id="@+id/back_button"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@android:drawable/ic_menu_revert"
            android:contentDescription="Back"
            android:onClick="onBackPressed" />

        <!-- Title -->
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Object Detection"
            android:textColor="@color/text_primary"
            android:textSize="20sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginHorizontal="16dp" />

        <!-- Detection Status -->
        <TextView
            android:id="@+id/detection_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Detection: ON"
            android:textColor="@android:color/holo_green_light"
            android:textSize="14sp"
            android:textStyle="bold" />

    </LinearLayout>

    <!-- Bottom Control Panel -->
    <LinearLayout
        android:id="@+id/bottom_panel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@color/control_button_background"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Detection Results -->
        <TextView
            android:id="@+id/detection_results"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="No objects detected"
            android:textColor="@color/text_secondary"
            android:textSize="16sp"
            android:gravity="center"
            android:minHeight="48dp"
            android:background="@drawable/status_background"
            android:padding="12dp"
            android:layout_marginBottom="16dp" />

        <!-- Control Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center">

            <!-- Switch Camera Button -->
            <Button
                android:id="@+id/switch_camera_button"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:text="Front Camera"
                android:textColor="@color/text_primary"
                android:background="@color/robot_accent"
                android:layout_marginEnd="8dp"
                style="@style/Widget.AppCompat.Button.Borderless" />

            <!-- Toggle Detection Button -->
            <Button
                android:id="@+id/toggle_detection_button"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:text="Stop Detection"
                android:textColor="@color/text_primary"
                android:background="@color/robot_primary"
                android:layout_marginStart="8dp"
                style="@style/Widget.AppCompat.Button.Borderless" />

        </LinearLayout>

    </LinearLayout>

    <!-- Loading Indicator -->
    <ProgressBar
        android:id="@+id/loading_indicator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:visibility="gone" />

</RelativeLayout>
