package com.stemrobo.humanoid.adapters;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.stemrobo.humanoid.R;
import com.stemrobo.humanoid.models.ChatMessage;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * Adapter for chat messages RecyclerView
 */
public class ChatAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {
    
    private List<ChatMessage> chatMessages;
    private SimpleDateFormat timeFormat;
    
    public ChatAdapter(List<ChatMessage> chatMessages) {
        this.chatMessages = chatMessages;
        this.timeFormat = new SimpleDateFormat("HH:mm", Locale.getDefault());
    }
    
    @Override
    public int getItemViewType(int position) {
        return chatMessages.get(position).getType();
    }
    
    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        LayoutInflater inflater = LayoutInflater.from(parent.getContext());
        
        switch (viewType) {
            case ChatMessage.TYPE_USER:
                View userView = inflater.inflate(R.layout.item_chat_user, parent, false);
                return new UserMessageViewHolder(userView);
                
            case ChatMessage.TYPE_AI:
                View aiView = inflater.inflate(R.layout.item_chat_robot, parent, false);
                return new AIMessageViewHolder(aiView);
                
            case ChatMessage.TYPE_SYSTEM:
                View systemView = inflater.inflate(R.layout.item_chat_system, parent, false);
                return new SystemMessageViewHolder(systemView);
                
            default:
                View defaultView = inflater.inflate(R.layout.item_chat_system, parent, false);
                return new SystemMessageViewHolder(defaultView);
        }
    }
    
    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        ChatMessage message = chatMessages.get(position);
        
        if (holder instanceof UserMessageViewHolder) {
            ((UserMessageViewHolder) holder).bind(message);
        } else if (holder instanceof AIMessageViewHolder) {
            ((AIMessageViewHolder) holder).bind(message);
        } else if (holder instanceof SystemMessageViewHolder) {
            ((SystemMessageViewHolder) holder).bind(message);
        }
    }
    
    @Override
    public int getItemCount() {
        return chatMessages.size();
    }
    
    // ViewHolder for user messages
    class UserMessageViewHolder extends RecyclerView.ViewHolder {
        TextView messageText;
        TextView timeText;
        
        UserMessageViewHolder(@NonNull View itemView) {
            super(itemView);
            messageText = itemView.findViewById(R.id.message_text);
            timeText = itemView.findViewById(R.id.time_text);
        }
        
        void bind(ChatMessage message) {
            messageText.setText(message.getMessage());
            timeText.setText(timeFormat.format(new Date(message.getTimestamp())));
        }
    }
    
    // ViewHolder for AI/Robot messages
    class AIMessageViewHolder extends RecyclerView.ViewHolder {
        TextView messageText;
        TextView timeText;
        
        AIMessageViewHolder(@NonNull View itemView) {
            super(itemView);
            messageText = itemView.findViewById(R.id.message_text);
            timeText = itemView.findViewById(R.id.time_text);
        }
        
        void bind(ChatMessage message) {
            messageText.setText(message.getMessage());
            timeText.setText(timeFormat.format(new Date(message.getTimestamp())));
        }
    }
    
    // ViewHolder for system messages
    class SystemMessageViewHolder extends RecyclerView.ViewHolder {
        TextView messageText;
        
        SystemMessageViewHolder(@NonNull View itemView) {
            super(itemView);
            messageText = itemView.findViewById(R.id.message_text);
        }
        
        void bind(ChatMessage message) {
            messageText.setText(message.getMessage());
        }
    }
}
