package com.stemrobo.humanoid.database;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;

/**
 * SQLite database helper for storing preset action sequences.
 * Manages database creation, upgrades, and provides access to DAOs.
 */
public class PresetDatabase extends SQLiteOpenHelper {
    
    private static final String DATABASE_NAME = "preset_database.db";
    private static final int DATABASE_VERSION = 1;
    
    // Table names
    public static final String TABLE_PRESETS = "presets";
    public static final String TABLE_PRESET_STEPS = "preset_steps";
    public static final String TABLE_PRESET_ACTIONS = "preset_actions";
    
    // Presets table columns
    public static final String PRESET_ID = "id";
    public static final String PRESET_NAME = "name";
    public static final String PRESET_DESCRIPTION = "description";
    public static final String PRESET_CATEGORY = "category";
    public static final String PRESET_CREATED_AT = "created_at";
    public static final String PRESET_MODIFIED_AT = "modified_at";
    public static final String PRESET_IS_ACTIVE = "is_active";
    public static final String PRESET_TOTAL_DURATION = "total_duration_ms";
    
    // Preset steps table columns
    public static final String STEP_ID = "id";
    public static final String STEP_PRESET_ID = "preset_id";
    public static final String STEP_START_TIME = "start_time_ms";
    public static final String STEP_DURATION = "duration_ms";
    public static final String STEP_NAME = "name";
    public static final String STEP_ORDER = "step_order";
    
    // Preset actions table columns
    public static final String ACTION_ID = "id";
    public static final String ACTION_STEP_ID = "step_id";
    public static final String ACTION_TYPE = "action_type";
    public static final String ACTION_DESCRIPTION = "description";
    public static final String ACTION_PARAMETERS = "parameters"; // JSON string
    public static final String ACTION_ORDER = "action_order";
    
    // Create table statements
    private static final String CREATE_PRESETS_TABLE = 
        "CREATE TABLE " + TABLE_PRESETS + " (" +
        PRESET_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
        PRESET_NAME + " TEXT NOT NULL, " +
        PRESET_DESCRIPTION + " TEXT, " +
        PRESET_CATEGORY + " TEXT DEFAULT 'CUSTOM', " +
        PRESET_CREATED_AT + " INTEGER NOT NULL, " +
        PRESET_MODIFIED_AT + " INTEGER NOT NULL, " +
        PRESET_IS_ACTIVE + " INTEGER DEFAULT 1, " +
        PRESET_TOTAL_DURATION + " INTEGER DEFAULT 0" +
        ")";
    
    private static final String CREATE_PRESET_STEPS_TABLE = 
        "CREATE TABLE " + TABLE_PRESET_STEPS + " (" +
        STEP_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
        STEP_PRESET_ID + " INTEGER NOT NULL, " +
        STEP_START_TIME + " INTEGER NOT NULL, " +
        STEP_DURATION + " INTEGER NOT NULL, " +
        STEP_NAME + " TEXT, " +
        STEP_ORDER + " INTEGER NOT NULL, " +
        "FOREIGN KEY(" + STEP_PRESET_ID + ") REFERENCES " + TABLE_PRESETS + "(" + PRESET_ID + ") ON DELETE CASCADE" +
        ")";
    
    private static final String CREATE_PRESET_ACTIONS_TABLE = 
        "CREATE TABLE " + TABLE_PRESET_ACTIONS + " (" +
        ACTION_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
        ACTION_STEP_ID + " INTEGER NOT NULL, " +
        ACTION_TYPE + " TEXT NOT NULL, " +
        ACTION_DESCRIPTION + " TEXT, " +
        ACTION_PARAMETERS + " TEXT, " +
        ACTION_ORDER + " INTEGER NOT NULL, " +
        "FOREIGN KEY(" + ACTION_STEP_ID + ") REFERENCES " + TABLE_PRESET_STEPS + "(" + STEP_ID + ") ON DELETE CASCADE" +
        ")";
    
    // Indexes for better performance
    private static final String CREATE_PRESET_STEPS_INDEX = 
        "CREATE INDEX idx_preset_steps_preset_id ON " + TABLE_PRESET_STEPS + "(" + STEP_PRESET_ID + ")";
    
    private static final String CREATE_PRESET_ACTIONS_INDEX = 
        "CREATE INDEX idx_preset_actions_step_id ON " + TABLE_PRESET_ACTIONS + "(" + ACTION_STEP_ID + ")";
    
    private static final String CREATE_PRESET_NAME_INDEX = 
        "CREATE INDEX idx_preset_name ON " + TABLE_PRESETS + "(" + PRESET_NAME + ")";
    
    private static PresetDatabase instance;
    private PresetDao presetDao;
    
    private PresetDatabase(Context context) {
        super(context, DATABASE_NAME, null, DATABASE_VERSION);
        this.presetDao = new PresetDao(this);
    }
    
    public static synchronized PresetDatabase getInstance(Context context) {
        if (instance == null) {
            instance = new PresetDatabase(context.getApplicationContext());
        }
        return instance;
    }
    
    @Override
    public void onCreate(SQLiteDatabase db) {
        // Create tables
        db.execSQL(CREATE_PRESETS_TABLE);
        db.execSQL(CREATE_PRESET_STEPS_TABLE);
        db.execSQL(CREATE_PRESET_ACTIONS_TABLE);
        
        // Create indexes
        db.execSQL(CREATE_PRESET_STEPS_INDEX);
        db.execSQL(CREATE_PRESET_ACTIONS_INDEX);
        db.execSQL(CREATE_PRESET_NAME_INDEX);
        
        // Insert default presets
        insertDefaultPresets(db);
    }
    
    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        // Handle database upgrades
        if (oldVersion < newVersion) {
            // For now, just recreate tables
            db.execSQL("DROP TABLE IF EXISTS " + TABLE_PRESET_ACTIONS);
            db.execSQL("DROP TABLE IF EXISTS " + TABLE_PRESET_STEPS);
            db.execSQL("DROP TABLE IF EXISTS " + TABLE_PRESETS);
            onCreate(db);
        }
    }
    
    @Override
    public void onConfigure(SQLiteDatabase db) {
        super.onConfigure(db);
        db.setForeignKeyConstraintsEnabled(true);
    }
    
    public PresetDao getPresetDao() {
        return presetDao;
    }
    
    private void insertDefaultPresets(SQLiteDatabase db) {
        long currentTime = System.currentTimeMillis();
        
        // Default Preset 1: Welcome Greeting
        String insertPreset1 = "INSERT INTO " + TABLE_PRESETS + " (" +
            PRESET_NAME + ", " + PRESET_DESCRIPTION + ", " + PRESET_CATEGORY + ", " +
            PRESET_CREATED_AT + ", " + PRESET_MODIFIED_AT + ", " + PRESET_TOTAL_DURATION +
            ") VALUES ('Welcome Greeting', 'Wave hand, say hello, and move forward', 'GESTURE', " +
            currentTime + ", " + currentTime + ", 8000)";
        db.execSQL(insertPreset1);
        
        // Default Preset 2: Security Patrol
        String insertPreset2 = "INSERT INTO " + TABLE_PRESETS + " (" +
            PRESET_NAME + ", " + PRESET_DESCRIPTION + ", " + PRESET_CATEGORY + ", " +
            PRESET_CREATED_AT + ", " + PRESET_MODIFIED_AT + ", " + PRESET_TOTAL_DURATION +
            ") VALUES ('Security Patrol', 'Look around, move forward, check surroundings', 'SECURITY', " +
            currentTime + ", " + currentTime + ", 12000)";
        db.execSQL(insertPreset2);
        
        // Default Preset 3: Dance Routine
        String insertPreset3 = "INSERT INTO " + TABLE_PRESETS + " (" +
            PRESET_NAME + ", " + PRESET_DESCRIPTION + ", " + PRESET_CATEGORY + ", " +
            PRESET_CREATED_AT + ", " + PRESET_MODIFIED_AT + ", " + PRESET_TOTAL_DURATION +
            ") VALUES ('Simple Dance', 'Wave arms, rotate, and move in pattern', 'DANCE', " +
            currentTime + ", " + currentTime + ", 15000)";
        db.execSQL(insertPreset3);
        
        // You can add more default presets here
    }
    
    public void clearAllData() {
        SQLiteDatabase db = this.getWritableDatabase();
        db.execSQL("DELETE FROM " + TABLE_PRESET_ACTIONS);
        db.execSQL("DELETE FROM " + TABLE_PRESET_STEPS);
        db.execSQL("DELETE FROM " + TABLE_PRESETS);
        db.close();
    }
    
    public void resetToDefaults() {
        SQLiteDatabase db = this.getWritableDatabase();
        clearAllData();
        insertDefaultPresets(db);
        db.close();
    }
}
