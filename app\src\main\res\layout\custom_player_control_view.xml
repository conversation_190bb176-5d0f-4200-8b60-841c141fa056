<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:background="@drawable/player_control_background"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- Progress Bar -->
    <com.google.android.exoplayer2.ui.DefaultTimeBar
        android:id="@id/exo_progress"
        android:layout_width="match_parent"
        android:layout_height="26dp"
        android:layout_marginBottom="8dp" />

    <!-- Control Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal">

        <!-- Current Time -->
        <TextView
            android:id="@id/exo_position"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@android:color/white"
            android:textSize="14sp"
            android:layout_marginEnd="16dp" />

        <!-- Rewind <PERSON><PERSON> -->
        <ImageButton
            android:id="@id/exo_rew"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:contentDescription="Rewind"
            android:src="@drawable/exo_controls_rewind"
            android:tint="@android:color/white"
            android:layout_marginEnd="8dp" />

        <!-- Play/Pause Button -->
        <ImageButton
            android:id="@id/exo_play_pause"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:contentDescription="Play/Pause"
            android:src="@drawable/exo_controls_play"
            android:tint="@android:color/white"
            android:layout_marginHorizontal="8dp" />

        <!-- Fast Forward Button -->
        <ImageButton
            android:id="@id/exo_ffwd"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:contentDescription="Fast Forward"
            android:src="@drawable/exo_controls_fastforward"
            android:tint="@android:color/white"
            android:layout_marginStart="8dp" />

        <!-- Duration -->
        <TextView
            android:id="@id/exo_duration"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@android:color/white"
            android:textSize="14sp"
            android:layout_marginStart="16dp" />

    </LinearLayout>

</LinearLayout>
