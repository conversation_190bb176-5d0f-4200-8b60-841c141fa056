<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black">

    <!-- ExoPlayer Video View -->
    <com.google.android.exoplayer2.ui.StyledPlayerView
        android:id="@+id/player_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerInParent="true"
        app:use_controller="true"
        app:show_buffering="when_playing"
        app:controller_layout_id="@layout/custom_player_control_view" />

    <!-- Close Button Overlay -->
    <ImageButton
        android:id="@+id/close_button"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:layout_margin="16dp"
        android:background="@drawable/close_button_background"
        android:contentDescription="Close video"
        android:src="@drawable/ic_close_white"
        android:scaleType="centerInside"
        android:padding="12dp" />

</RelativeLayout>
