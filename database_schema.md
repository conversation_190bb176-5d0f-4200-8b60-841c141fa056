# Database Schema

## Database Overview

The application utilizes two distinct databases to manage its data:

- **PersonDatabase**: A Room-based database responsible for storing data related to face recognition and chat history. It contains four tables: `Person`, `PersonEmbedding`, `ChatConversation`, and `ChatHistory`.
- **PresetDatabase**: An SQLite database managed by `SQLiteOpenHelper`. It is designed to store and manage robot action presets, which are sequences of actions that the robot can perform. It consists of three tables: `Preset`, `PresetStep`, and `PresetAction`.
## PersonDatabase (Room)

```mermaid
erDiagram
    Person ||--o{ PersonEmbedding : "has"
    ChatConversation ||--o{ ChatHistory : "has"
### PersonDatabase

The `PersonDatabase` is a Room database designed to handle all data related to person recognition and chat history. It is the primary database for storing user-specific information and interactions.

- **`Person`**: This table stores information about individuals who have been identified by the robot's vision system. Each person has a unique ID, a name, and an associated face embedding.
- **`PersonEmbedding`**: This table stores multiple face embeddings for each person, allowing for more robust and accurate face recognition.
- **`ChatConversation`**: This table manages chat sessions, with each entry representing a distinct conversation with the AI.
- **`ChatHistory`**: This table stores the complete history of chat messages, linking each message to a specific conversation.

    Person {
        int uid PK
        string name
        string embeddingJson
        long createdTimestamp
        int recognitionCount
        long lastSeenTimestamp
        float confidenceThreshold
    }

    PersonEmbedding {
        int id PK
        int personId FK
        string embeddingJson
        long createdTimestamp
        float qualityScore
        boolean isPrimary
    }

    ChatConversation {
        string conversationId PK
        string title
        long createdTimestamp
        long lastUpdatedTimestamp
        int messageCount
        boolean isActive
        string languageCode
    }

    ChatHistory {
        long id PK
        string conversationId FK
        string messageText
        int messageType
        long timestamp
        int sequenceNumber
### PresetDatabase

The `PresetDatabase` is an `SQLiteOpenHelper`-based database that stores and manages robot action presets. These presets are predefined sequences of actions that the robot can execute.

- **`Preset`**: This table contains the main information for each preset, including its name, description, and category.
- **`PresetStep`**: This table defines the individual steps within a preset. Each step has a start time and duration, allowing for timed sequences of actions.
- **`PresetAction`**: This table holds the specific actions to be performed in each step. Actions can include movements, speech, or other robot behaviors.
        string languageCode
        long responseTimeMs
        string contextUsed
    }
```

## PresetDatabase (SQLiteOpenHelper)

```mermaid
erDiagram
    Preset ||--o{ PresetStep : "has"
    PresetStep ||--o{ PresetAction : "has"

    Preset {
        long id PK
        string name
        string description
        string category
        long createdAt
        long modifiedAt
        boolean isActive
        int totalDurationMs
    }

    PresetStep {
        long id PK
        long presetId FK
        int startTimeMs
        int durationMs
        string name
        int order
    }

    PresetAction {
        long id PK
        long stepId FK
        string type
        string description
        string parameters
        int order
    }
## Database Best Practices

### Migrations

- **PersonDatabase**: Since this is a Room database, migrations should be handled using the `Room.addMigrations()` method. Each schema change should be accompanied by a corresponding `Migration` class that correctly updates the database schema.
- **PresetDatabase**: For the `PresetDatabase`, migrations must be handled manually in the `onUpgrade()` method of the `SQLiteOpenHelper`. It is crucial to write robust migration logic to preserve user data.

### Future Modifications

- **Schema Changes**: When modifying the database schema, always update the version number and implement the necessary migration logic.
- **Data Access**: All database operations should be performed on background threads to avoid blocking the main UI thread.
- **Testing**: Thoroughly test all database-related changes, including migrations, to ensure data integrity and prevent crashes.