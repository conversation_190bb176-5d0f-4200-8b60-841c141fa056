<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.StemRobot" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/robot_primary</item>
        <item name="colorPrimaryVariant">@color/robot_primary_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/robot_accent</item>
        <item name="colorSecondaryVariant">@color/robot_accent</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor" tools:targetApi="l">?attr/colorPrimaryVariant</item>
        <!-- Background colors -->
        <item name="android:windowBackground">@color/robot_background</item>
        <item name="colorSurface">@color/content_background</item>
        <item name="colorOnSurface">@color/text_primary</item>
        <!-- Customize your theme here. -->
    </style>

    <style name="Theme.StemRobot.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <style name="Theme.StemRobot.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar" />

    <style name="Theme.StemRobot.PopupOverlay" parent="ThemeOverlay.AppCompat.Light" />

    <!-- Compact Bottom Navigation Style -->
    <style name="CompactBottomNavigationView" parent="Widget.MaterialComponents.BottomNavigationView">
        <item name="android:layout_height">48dp</item>
        <item name="itemTextAppearanceActive">@style/CompactBottomNavigationText</item>
        <item name="itemTextAppearanceInactive">@style/CompactBottomNavigationText</item>
        <item name="android:paddingTop">4dp</item>
        <item name="android:paddingBottom">4dp</item>
    </style>

    <!-- Compact text style for bottom navigation -->
    <style name="CompactBottomNavigationText" parent="TextAppearance.MaterialComponents.Caption">
        <item name="android:textSize">10sp</item>
    </style>

</resources>
