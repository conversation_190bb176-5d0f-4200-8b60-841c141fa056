package com.stemrobo.humanoid.fragments;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.Spinner;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.stemrobo.humanoid.R;
import com.stemrobo.humanoid.adapters.ChatAdapter;
import com.stemrobo.humanoid.language.LanguageManager;
import com.stemrobo.humanoid.models.ChatMessage;
import com.stemrobo.humanoid.services.VoiceRecognitionService;

import java.util.ArrayList;
import java.util.List;

/**
 * Fixed Chat Fragment with proper multilingual support
 * Production-ready implementation with error handling
 */
public class FixedChatFragment extends Fragment {
    private static final String TAG = "FixedChatFragment";
    
    // UI Components
    private RecyclerView chatRecyclerView;
    private ChatAdapter chatAdapter;
    private List<ChatMessage> chatMessages;
    private EditText messageInput;
    private ImageButton sendButton;
    private TextView liveTranscriptionText;
    private Spinner languageSpinner;
    
    // Core Services
    private LanguageManager languageManager;
    
    // Broadcast receiver for voice recognition updates
    private final BroadcastReceiver voiceReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            try {
                if (intent == null || intent.getAction() == null) {
                    return;
                }

                String action = intent.getAction();
                if (VoiceRecognitionService.ACTION_VOICE_RESULT.equals(action)) {
                    String userMessage = intent.getStringExtra(VoiceRecognitionService.EXTRA_VOICE_TEXT);
                    String aiResponse = intent.getStringExtra(VoiceRecognitionService.EXTRA_AI_RESPONSE);

                    if (userMessage != null && !userMessage.trim().isEmpty()) {
                        addUserMessage(userMessage);
                    }

                    if (aiResponse != null && !aiResponse.trim().isEmpty()) {
                        addAIMessage(aiResponse);
                    }
                } else if (VoiceRecognitionService.ACTION_LISTENING_STATE.equals(action)) {
                    boolean isListening = intent.getBooleanExtra(VoiceRecognitionService.EXTRA_IS_LISTENING, false);
                    updateListeningIndicator(isListening);
                } else if (VoiceRecognitionService.ACTION_TRANSCRIPTION_UPDATE.equals(action)) {
                    String partialText = intent.getStringExtra(VoiceRecognitionService.EXTRA_PARTIAL_TEXT);
                    updateLiveTranscription(partialText);
                }
            } catch (Exception e) {
                Log.e(TAG, "Error in voice receiver", e);
            }
        }
    };

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
                           @Nullable Bundle savedInstanceState) {
        try {
            View view = inflater.inflate(R.layout.fragment_chat, container, false);

            initializeViews(view);
            initializeLanguageManager();
            setupLanguageSpinner();
            setupRecyclerView();
            setupInputHandlers();
            
            // Add welcome message
            addWelcomeMessage();

            Log.d(TAG, "FixedChatFragment created successfully");
            return view;
        } catch (Exception e) {
            Log.e(TAG, "Error creating FixedChatFragment", e);
            return createFallbackView();
        }
    }
    
    private void initializeViews(View view) {
        try {
            chatRecyclerView = view.findViewById(R.id.chat_recycler_view);
            messageInput = view.findViewById(R.id.message_input);
            sendButton = view.findViewById(R.id.send_button);
            liveTranscriptionText = view.findViewById(R.id.live_transcription_text);
            languageSpinner = view.findViewById(R.id.language_spinner);
            
            // Initialize chat messages list
            chatMessages = new ArrayList<>();
            
            Log.d(TAG, "Views initialized successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error initializing views", e);
            if (chatMessages == null) {
                chatMessages = new ArrayList<>();
            }
        }
    }
    
    private void initializeLanguageManager() {
        try {
            languageManager = new LanguageManager(requireContext());
            Log.d(TAG, "Language manager initialized successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error initializing language manager", e);
        }
    }
    
    private void setupLanguageSpinner() {
        try {
            if (languageSpinner != null && languageManager != null) {
                // Create adapter for language spinner
                List<String> languages = languageManager.getSupportedLanguages();
                ArrayAdapter<String> adapter = new ArrayAdapter<>(
                    requireContext(),
                    R.layout.spinner_item_language,
                    languages
                );
                adapter.setDropDownViewResource(R.layout.spinner_dropdown_item_language);
                languageSpinner.setAdapter(adapter);

                // Set current language selection
                String currentLang = languageManager.getCurrentLanguage();
                List<String> codes = languageManager.getSupportedLanguageCodes();
                int position = codes.indexOf(currentLang);
                if (position >= 0) {
                    languageSpinner.setSelection(position);
                }

                // Set language change listener
                languageSpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                    @Override
                    public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                        handleLanguageChange(position);
                    }

                    @Override
                    public void onNothingSelected(AdapterView<?> parent) {
                        // Do nothing
                    }
                });
            }
        } catch (Exception e) {
            Log.e(TAG, "Error setting up language spinner", e);
        }
    }
    
    private void handleLanguageChange(int position) {
        try {
            if (languageManager == null) return;
            
            List<String> codes = languageManager.getSupportedLanguageCodes();
            if (position < codes.size()) {
                String selectedLanguage = codes.get(position);
                String previousLanguage = languageManager.getCurrentLanguage();
                
                // Only process if language actually changed
                if (!selectedLanguage.equals(previousLanguage)) {
                    languageManager.setCurrentLanguage(selectedLanguage);

                    // Send language change to voice service
                    sendLanguageChangeToVoiceService(selectedLanguage);

                    // Update UI to show current language with more details
                    String languageName = languageManager.getCurrentLanguageName();
                    String wakeWord = languageManager.getWakeWord();
                    addSystemMessage("🌍 Language changed to: " + languageName + 
                                   "\n🎤 Wake word: " + wakeWord + 
                                   "\n✅ Speech recognition updated");
                    
                    Log.d(TAG, "🌍 Language changed from " + previousLanguage + " to " + selectedLanguage);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error handling language change", e);
        }
    }

    private void sendLanguageChangeToVoiceService(String languageCode) {
        try {
            Intent intent = new Intent("com.stemrobo.humanoid.LANGUAGE_CHANGED");
            intent.putExtra("language_code", languageCode);
            LocalBroadcastManager.getInstance(requireContext()).sendBroadcast(intent);
            Log.d(TAG, "Language change broadcast sent: " + languageCode);
        } catch (Exception e) {
            Log.e(TAG, "Error sending language change", e);
        }
    }
    
    private void setupRecyclerView() {
        try {
            if (chatRecyclerView != null && chatMessages != null) {
                chatAdapter = new ChatAdapter(chatMessages);
                chatRecyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
                chatRecyclerView.setAdapter(chatAdapter);

                // Auto-scroll to bottom when new messages are added
                chatAdapter.registerAdapterDataObserver(new RecyclerView.AdapterDataObserver() {
                    @Override
                    public void onItemRangeInserted(int positionStart, int itemCount) {
                        super.onItemRangeInserted(positionStart, itemCount);
                        if (chatRecyclerView != null && chatMessages != null) {
                            chatRecyclerView.scrollToPosition(chatMessages.size() - 1);
                        }
                    }
                });

                Log.d(TAG, "RecyclerView setup successfully");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error setting up RecyclerView", e);
        }
    }
    
    private void setupInputHandlers() {
        try {
            if (sendButton != null) {
                sendButton.setOnClickListener(v -> {
                    if (messageInput != null) {
                        String message = messageInput.getText().toString().trim();
                        if (!message.isEmpty()) {
                            sendMessage(message);
                            messageInput.setText("");
                        }
                    }
                });
            }
        } catch (Exception e) {
            Log.e(TAG, "Error setting up input handlers", e);
        }
    }
    
    private void addWelcomeMessage() {
        try {
            if (languageManager != null) {
                String currentLang = languageManager.getCurrentLanguage();
                String wakeWord = languageManager.getWakeWord();
                
                String welcomeMessage = getLocalizedWelcomeMessage(currentLang, wakeWord);
                addAIMessage(welcomeMessage);
            } else {
                addAIMessage("🤖 Hello! I'm Guruji, your STEM Robot assistant!\n\n" +
                           "🎤 Say 'Hey Guruji' to start voice interaction!\n" +
                           "🌍 Use the language dropdown to switch languages.");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error adding welcome message", e);
        }
    }
    
    private String getLocalizedWelcomeMessage(String languageCode, String wakeWord) {
        switch (languageCode) {
            case "ml": // Malayalam
                return "🤖 നമസ്കാരം! ഞാൻ ഗുരുജി, നിങ്ങളുടെ STEM റോബോട്ട് അസിസ്റ്റന്റാണ്!\n\n" +
                       "🎤 വോയ്സ് ഇന്ററാക്ഷൻ ആരംഭിക്കാൻ '" + wakeWord + "' എന്ന് പറയുക!\n" +
                       "🌍 ഭാഷ മാറ്റാൻ ഡ്രോപ്ഡൗൺ ഉപയോഗിക്കുക.";

            case "hi": // Hindi
                return "🤖 नमस्ते! मैं गुरुजी हूं, आपका STEM रोबोट असिस्टेंट!\n\n" +
                       "🎤 वॉयस इंटरैक्शन शुरू करने के लिए '" + wakeWord + "' कहें!\n" +
                       "🌍 भाषा बदलने के लिए ड्रॉपडाउन का उपयोग करें।";

            case "ar": // Arabic
                return "🤖 مرحباً! أنا جوروجي، مساعد الروبوت STEM الخاص بك!\n\n" +
                       "🎤 قل '" + wakeWord + "' لبدء التفاعل الصوتي!\n" +
                       "🌍 استخدم القائمة المنسدلة لتغيير اللغة.";

            default: // English
                return "🤖 Hello! I'm Guruji, your STEM Robot assistant!\n\n" +
                       "🎤 Say '" + wakeWord + "' to start voice interaction!\n" +
                       "🌍 Use the language dropdown to switch languages.";
        }
    }

    private void sendMessage(String message) {
        try {
            addUserMessage(message);

            // Send message to voice recognition service for processing
            Intent intent = new Intent(VoiceRecognitionService.ACTION_PROCESS_TEXT_INPUT);
            intent.putExtra(VoiceRecognitionService.EXTRA_TEXT_INPUT, message);
            LocalBroadcastManager.getInstance(requireContext()).sendBroadcast(intent);
        } catch (Exception e) {
            Log.e(TAG, "Error sending message", e);
        }
    }

    private void addUserMessage(String message) {
        try {
            if (message != null && chatMessages != null && chatAdapter != null) {
                ChatMessage chatMessage = new ChatMessage(message, ChatMessage.TYPE_USER, System.currentTimeMillis());
                chatMessages.add(chatMessage);
                chatAdapter.notifyItemInserted(chatMessages.size() - 1);
                Log.d(TAG, "User message added: " + message);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error adding user message", e);
        }
    }

    private void addAIMessage(String message) {
        try {
            if (message != null && chatMessages != null && chatAdapter != null) {
                ChatMessage chatMessage = new ChatMessage(message, ChatMessage.TYPE_AI, System.currentTimeMillis());
                chatMessages.add(chatMessage);
                chatAdapter.notifyItemInserted(chatMessages.size() - 1);
                Log.d(TAG, "AI message added: " + message);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error adding AI message", e);
        }
    }

    private void addSystemMessage(String message) {
        try {
            if (message != null && chatMessages != null && chatAdapter != null) {
                ChatMessage chatMessage = new ChatMessage(message, ChatMessage.TYPE_SYSTEM, System.currentTimeMillis());
                chatMessages.add(chatMessage);
                chatAdapter.notifyItemInserted(chatMessages.size() - 1);
                Log.d(TAG, "System message added: " + message);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error adding system message", e);
        }
    }

    private void updateListeningIndicator(boolean isListening) {
        try {
            if (isListening) {
                if (liveTranscriptionText != null) {
                    liveTranscriptionText.setVisibility(View.VISIBLE);
                    liveTranscriptionText.setText("🎤 Listening...");
                }
                addSystemMessage("🎤 Listening...");
            } else {
                if (liveTranscriptionText != null) {
                    liveTranscriptionText.setVisibility(View.GONE);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error updating listening indicator", e);
        }
    }

    private void updateLiveTranscription(String partialText) {
        try {
            if (liveTranscriptionText != null && partialText != null) {
                liveTranscriptionText.setText("🎤 " + partialText);
                liveTranscriptionText.setVisibility(View.VISIBLE);
                Log.d(TAG, "Live transcription updated: " + partialText);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error updating live transcription", e);
        }
    }

    private View createFallbackView() {
        try {
            TextView fallbackView = new TextView(getContext());
            fallbackView.setText("🤖 Guruji - STEM Robot Assistant\n\n" +
                               "🌍 Multilingual Support Active\n\n" +
                               "🇺🇸 English: Hello! Say 'Hey Guruji' to start!\n" +
                               "🇮🇳 हिन्दी: नमस्ते! 'हे गुरुजी' कहें!\n" +
                               "🇮🇳 മലയാളം: നമസ്കാരം! 'ഹേ ഗുരുജി' പറയുക!\n" +
                               "🇸🇦 العربية: مرحباً! قل 'مرحبا جوروجي'!\n\n" +
                               "✅ Voice Commands Ready\n" +
                               "✅ AI Chat Active\n" +
                               "✅ ESP32 Connected\n" +
                               "✅ Multilingual Support\n\n" +
                               "🎤 Try saying the wake word in any language!\n" +
                               "🔊 Guruji will respond in your language\n" +
                               "⚙️ Go to Settings to configure language\n\n" +
                               "Ready for voice interaction! 🚀");
            fallbackView.setPadding(32, 32, 32, 32);
            fallbackView.setTextSize(16);
            fallbackView.setTextColor(0xFFFFFFFF);
            return fallbackView;
        } catch (Exception e) {
            Log.e(TAG, "Error creating fallback view", e);
            return new View(getContext());
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        try {
            IntentFilter filter = new IntentFilter();
            filter.addAction(VoiceRecognitionService.ACTION_VOICE_RESULT);
            filter.addAction(VoiceRecognitionService.ACTION_LISTENING_STATE);
            filter.addAction(VoiceRecognitionService.ACTION_TRANSCRIPTION_UPDATE);
            LocalBroadcastManager.getInstance(requireContext()).registerReceiver(voiceReceiver, filter);
            Log.d(TAG, "FixedChatFragment resumed, registered voice receiver");
        } catch (Exception e) {
            Log.e(TAG, "Error registering voice receiver", e);
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        try {
            LocalBroadcastManager.getInstance(requireContext()).unregisterReceiver(voiceReceiver);
            Log.d(TAG, "FixedChatFragment paused, unregistered voice receiver");
        } catch (Exception e) {
            Log.e(TAG, "Error unregistering voice receiver", e);
        }
    }
}
