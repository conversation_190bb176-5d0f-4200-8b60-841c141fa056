package com.stemrobo.humanoid.database;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.room.PrimaryKey;

/**
 * Person entity for storing face recognition data
 * Each person has a unique ID, name, and face embedding for recognition
 */
@Entity(tableName = "known_persons")
public class Person {
    
    @PrimaryKey(autoGenerate = true)
    public int uid;

    @ColumnInfo(name = "name")
    public String name;

    // Face embedding stored as JSON string (192-dimensional vector from FaceNet)
    @ColumnInfo(name = "embedding_json")
    public String embeddingJson;
    
    // Timestamp when person was registered
    @ColumnInfo(name = "created_timestamp")
    public long createdTimestamp;
    
    // Number of times this person has been recognized
    @ColumnInfo(name = "recognition_count")
    public int recognitionCount;
    
    // Last time this person was recognized
    @ColumnInfo(name = "last_seen_timestamp")
    public long lastSeenTimestamp;
    
    // Confidence threshold used for this person (can be personalized)
    @ColumnInfo(name = "confidence_threshold")
    public float confidenceThreshold;

    // Default constructor
    public Person() {
        this.createdTimestamp = System.currentTimeMillis();
        this.recognitionCount = 0;
        this.lastSeenTimestamp = 0;
        this.confidenceThreshold = 0.6f; // Default threshold
    }

    // Constructor with name and embedding
    @Ignore
    public Person(String name, String embeddingJson) {
        this();
        this.name = name;
        this.embeddingJson = embeddingJson;
    }
    
    // Update recognition statistics
    public void updateRecognition() {
        this.recognitionCount++;
        this.lastSeenTimestamp = System.currentTimeMillis();
    }
    
    // Get formatted creation date
    public String getFormattedCreatedDate() {
        return new java.text.SimpleDateFormat("MMM dd, yyyy HH:mm", java.util.Locale.getDefault())
                .format(new java.util.Date(createdTimestamp));
    }
    
    // Get formatted last seen date
    public String getFormattedLastSeenDate() {
        if (lastSeenTimestamp == 0) {
            return "Never";
        }
        return new java.text.SimpleDateFormat("MMM dd, yyyy HH:mm", java.util.Locale.getDefault())
                .format(new java.util.Date(lastSeenTimestamp));
    }
    
    @Override
    public String toString() {
        return "Person{" +
                "uid=" + uid +
                ", name='" + name + '\'' +
                ", recognitionCount=" + recognitionCount +
                ", confidenceThreshold=" + confidenceThreshold +
                '}';
    }
}
