package com.stemrobo.humanoid.database;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.room.PrimaryKey;

/**
 * Entity for storing chat conversation sessions
 * Each conversation represents a chat session with the AI
 */
@Entity(tableName = "chat_conversations")
public class ChatConversation {
    
    @PrimaryKey
    @NonNull
    @ColumnInfo(name = "conversation_id")
    public String conversationId;
    
    @ColumnInfo(name = "title")
    public String title; // Auto-generated or user-defined title
    
    @ColumnInfo(name = "created_timestamp")
    public long createdTimestamp;
    
    @ColumnInfo(name = "last_updated_timestamp")
    public long lastUpdatedTimestamp;
    
    @ColumnInfo(name = "message_count")
    public int messageCount;
    
    @ColumnInfo(name = "is_active")
    public boolean isActive; // Whether this is the current active conversation
    
    @ColumnInfo(name = "language_code")
    public String languageCode; // Language used in this conversation
    
    // Default constructor
    public ChatConversation() {
        this.createdTimestamp = System.currentTimeMillis();
        this.lastUpdatedTimestamp = System.currentTimeMillis();
        this.messageCount = 0;
        this.isActive = false;
    }
    
    // Constructor with conversation ID
    @Ignore
    public ChatConversation(@NonNull String conversationId) {
        this();
        this.conversationId = conversationId;
        this.title = "Chat " + java.text.DateFormat.getDateTimeInstance().format(new java.util.Date());
    }

    // Constructor with conversation ID and title
    @Ignore
    public ChatConversation(@NonNull String conversationId, String title) {
        this();
        this.conversationId = conversationId;
        this.title = title;
    }
    
    // Getters and setters
    public String getConversationId() {
        return conversationId;
    }
    
    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public long getCreatedTimestamp() {
        return createdTimestamp;
    }
    
    public void setCreatedTimestamp(long createdTimestamp) {
        this.createdTimestamp = createdTimestamp;
    }
    
    public long getLastUpdatedTimestamp() {
        return lastUpdatedTimestamp;
    }
    
    public void setLastUpdatedTimestamp(long lastUpdatedTimestamp) {
        this.lastUpdatedTimestamp = lastUpdatedTimestamp;
    }
    
    public int getMessageCount() {
        return messageCount;
    }
    
    public void setMessageCount(int messageCount) {
        this.messageCount = messageCount;
    }
    
    public boolean isActive() {
        return isActive;
    }
    
    public void setActive(boolean active) {
        isActive = active;
    }
    
    public String getLanguageCode() {
        return languageCode;
    }
    
    public void setLanguageCode(String languageCode) {
        this.languageCode = languageCode;
    }
    
    /**
     * Update conversation with new message
     */
    public void updateWithNewMessage() {
        this.messageCount++;
        this.lastUpdatedTimestamp = System.currentTimeMillis();
    }
}
