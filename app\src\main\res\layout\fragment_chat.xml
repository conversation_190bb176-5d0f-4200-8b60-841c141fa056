<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/content_background">

    <!-- Chat <PERSON>er -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="8dp"
        android:background="@color/robot_primary"
        android:gravity="center_vertical">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="🤖 Guruji AI Assistant"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:textStyle="bold" />

        <!-- Language Selection Dropdown -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginEnd="8dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="🌍"
                android:textSize="14sp"
                android:layout_marginEnd="4dp" />

            <Spinner
                android:id="@+id/language_spinner"
                android:layout_width="100dp"
                android:layout_height="32dp"
                android:background="@drawable/spinner_background" />

        </LinearLayout>

        <TextView
            android:id="@+id/listening_indicator"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="🎤 Ready"
            android:textColor="@color/white"
            android:textSize="12sp"
            android:visibility="visible" />

    </LinearLayout>

    <!-- Chat Messages RecyclerView -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/chat_recycler_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:padding="8dp"
        android:clipToPadding="false"
        android:scrollbars="vertical" />

    <!-- Message Input Area -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@color/status_bar_background">

        <!-- Control Buttons Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="8dp"
            android:gravity="center"
            android:background="@color/status_bar_background">

            <!-- Mute Button -->
            <ImageButton
                android:id="@+id/mute_button"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginEnd="12dp"
                android:background="@drawable/control_button_background"
                android:src="@drawable/ic_unmute"
                android:contentDescription="Mute/Unmute AI Speech"
                android:scaleType="centerInside" />

            <!-- Clear Chat Button -->
            <ImageButton
                android:id="@+id/clear_chat_button"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginEnd="12dp"
                android:background="@drawable/control_button_background"
                android:src="@drawable/ic_clear_chat"
                android:contentDescription="Clear Chat"
                android:scaleType="centerInside" />

            <!-- Screen Saver Button -->
            <ImageButton
                android:id="@+id/screen_saver_button"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:background="@drawable/control_button_background"
                android:src="@drawable/ic_screen_saver"
                android:contentDescription="Screen Saver Mode"
                android:scaleType="centerInside" />

            <!-- Stop TTS Button -->
            <ImageButton
                android:id="@+id/stop_tts_button"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginStart="12dp"
                android:background="@drawable/control_button_background"
                android:src="@android:drawable/ic_media_pause"
                android:contentDescription="Stop TTS"
                android:scaleType="centerInside"
                android:backgroundTint="#FF0000" />

        </LinearLayout>

        <!-- Input Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="8dp"
            android:gravity="center_vertical">

            <!-- Input Container with Live Transcription Inside -->
            <RelativeLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1">

                <EditText
                    android:id="@+id/message_input"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Type a message or say 'Hey Robot'..."
                    android:textColor="@color/text_primary"
                    android:textColorHint="@color/text_hint"
                    android:background="@drawable/message_input_background"
                    android:padding="12dp"
                    android:maxLines="3"
                    android:inputType="textMultiLine|textCapSentences"
                    android:imeOptions="actionSend" />

                <!-- Live Transcription Overlay INSIDE Input -->
                <TextView
                    android:id="@+id/live_transcription_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_alignTop="@id/message_input"
                    android:layout_alignStart="@id/message_input"
                    android:layout_alignEnd="@id/message_input"
                    android:text=""
                    android:textColor="@color/transcription_text"
                    android:textSize="14sp"
                    android:padding="12dp"
                    android:background="@drawable/transcription_overlay_background"
                    android:gravity="center_vertical"
                    android:visibility="gone"
                    android:elevation="2dp" />

            </RelativeLayout>

            <!-- Push to Talk Button -->
            <ImageButton
                android:id="@+id/push_to_talk_button"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginStart="8dp"
                android:background="@drawable/control_button_background"
                android:src="@drawable/ic_mic"
                android:contentDescription="Push to Talk"
                android:scaleType="centerInside" />

            <ImageButton
                android:id="@+id/send_button"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginStart="8dp"
                android:background="@drawable/send_button_background"
                android:src="@drawable/ic_send"
                android:contentDescription="Send message"
                android:scaleType="centerInside" />

        </LinearLayout>

    </LinearLayout>

    <!-- Screen saver functionality moved to MainActivity -->

</LinearLayout>
