package com.stemrobo.humanoid.models;

/**
 * Model class for chat messages
 */
public class ChatMessage {
    public static final int TYPE_USER = 0;
    public static final int TYPE_AI = 1;
    public static final int TYPE_SYSTEM = 2;

    private String message;
    private int type;
    private long timestamp;
    private String conversationId; // For linking to conversation sessions

    public ChatMessage(String message, int type, long timestamp) {
        this.message = message;
        this.type = type;
        this.timestamp = timestamp;
    }

    public ChatMessage(String message, int type, long timestamp, String conversationId) {
        this.message = message;
        this.type = type;
        this.timestamp = timestamp;
        this.conversationId = conversationId;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public String getConversationId() {
        return conversationId;
    }

    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }
}
