package com.stemrobo.humanoid.ai;

import android.content.Context;
import android.content.SharedPreferences;
import android.preference.PreferenceManager;
import android.util.Log;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

/**
 * Service for providing real-time information to AI chat
 * Handles time, weather, news, and other live data
 */
public class RealTimeDataService {
    private static final String TAG = "RealTimeDataService";
    
    // API Keys - Hardcoded for production use
    private static final String WEATHER_API_KEY = "f1752a056a11c778d1b15bc3318ca583";
    private static final String NEWS_API_KEY = "a718d8f4901f45f8aa7e477059716493";

    // Preference keys
    private static final String PREF_WEATHER_API_KEY = "weather_api_key";
    private static final String PREF_NEWS_API_KEY = "news_api_key";
    private static final String PREF_LOCATION_ENABLED = "location_enabled";
    private static final String PREF_LATITUDE = "latitude";
    private static final String PREF_LONGITUDE = "longitude";
    private static final String PREF_CITY_NAME = "city_name";
    
    // API URLs
    private static final String WEATHER_BASE_URL = "https://api.openweathermap.org/data/2.5/weather";
    private static final String NEWS_BASE_URL = "https://newsapi.org/v2/top-headlines";
    private static final String WORLD_TIME_URL = "https://worldtimeapi.org/api/timezone/";
    
    // Cache settings
    private static final long CACHE_DURATION_MS = 5 * 60 * 1000; // 5 minutes
    private static final long WEATHER_CACHE_DURATION_MS = 10 * 60 * 1000; // 10 minutes
    private static final long NEWS_CACHE_DURATION_MS = 30 * 60 * 1000; // 30 minutes
    
    private final Context context;
    private final ExecutorService executor;
    private final OkHttpClient httpClient;
    private final SharedPreferences preferences;

    // Cache variables (no caching for time/date/news - always real-time)
    private String cachedWeatherData;
    private long weatherCacheTime;
    
    // Default location (India - can be overridden by user settings)
    private double defaultLatitude = 28.6139; // New Delhi, India
    private double defaultLongitude = 77.2090;
    private String defaultCity = "New Delhi";
    
    public interface RealTimeDataCallback {
        void onSuccess(String data);
        void onError(String error);
    }
    
    public RealTimeDataService(Context context) {
        this.context = context;
        this.executor = Executors.newCachedThreadPool();
        this.httpClient = new OkHttpClient();
        this.preferences = PreferenceManager.getDefaultSharedPreferences(context);

        // Load user location preferences
        loadLocationSettings();
    }
    
    /**
     * Get configuration status for display
     */
    public String getConfigurationStatus() {
        StringBuilder status = new StringBuilder();

        // Check hardcoded API keys
        boolean weatherConfigured = !WEATHER_API_KEY.equals("YOUR_OPENWEATHER_API_KEY");
        boolean newsConfigured = !NEWS_API_KEY.equals("YOUR_NEWS_API_KEY");

        status.append("Weather API: ").append(weatherConfigured ? "✅ Configured (Built-in)" : "❌ Not configured").append("\n");
        status.append("News API: ").append(newsConfigured ? "✅ Configured (Built-in)" : "❌ Not configured").append("\n");

        // Check location
        boolean locationEnabled = preferences.getBoolean(PREF_LOCATION_ENABLED, false);
        status.append("Location: ").append(locationEnabled ? "✅ Custom location" : "📍 Default (New Delhi, India)").append("\n");

        // Time is always available
        status.append("Time Service: ✅ Always available");

        return status.toString();
    }

    /**
     * Set user location for weather data
     */
    public void setUserLocation(double latitude, double longitude, String cityName) {
        SharedPreferences.Editor editor = preferences.edit();
        editor.putString(PREF_LATITUDE, String.valueOf(latitude));
        editor.putString(PREF_LONGITUDE, String.valueOf(longitude));
        editor.putString(PREF_CITY_NAME, cityName);
        editor.putBoolean(PREF_LOCATION_ENABLED, true);
        editor.apply();
    }

    /**
     * Get current time information (always real-time, no caching)
     */
    public void getCurrentTime(RealTimeDataCallback callback) {
        executor.execute(() -> {
            try {
                // Always get fresh local time info (no caching for time/date)
                String localTime = getLocalTimeInfo();

                // Return local time immediately - no need for world time API or caching
                callback.onSuccess(localTime);

            } catch (Exception e) {
                Log.e(TAG, "Error getting time data", e);
                callback.onError("Unable to get current time information");
            }
        });
    }
    
    /**
     * Get weather information
     */
    public void getWeatherData(RealTimeDataCallback callback) {
        // Check cache first
        if (isWeatherCacheValid()) {
            callback.onSuccess(cachedWeatherData);
            return;
        }
        
        if (WEATHER_API_KEY.equals("YOUR_OPENWEATHER_API_KEY")) {
            callback.onError("Weather service not configured. Please add OpenWeather API key.");
            return;
        }
        
        executor.execute(() -> {
            try {
                String url = WEATHER_BASE_URL + 
                    "?lat=" + defaultLatitude + 
                    "&lon=" + defaultLongitude + 
                    "&appid=" + WEATHER_API_KEY + 
                    "&units=metric";
                
                Request request = new Request.Builder()
                    .url(url)
                    .build();
                
                httpClient.newCall(request).enqueue(new Callback() {
                    @Override
                    public void onFailure(Call call, IOException e) {
                        Log.e(TAG, "Weather API call failed", e);
                        callback.onError("Unable to get weather information");
                    }
                    
                    @Override
                    public void onResponse(Call call, Response response) throws IOException {
                        if (response.isSuccessful() && response.body() != null) {
                            String responseBody = response.body().string();
                            String formattedWeather = formatWeatherData(responseBody);
                            cacheWeatherData(formattedWeather);
                            callback.onSuccess(formattedWeather);
                        } else {
                            callback.onError("Weather service temporarily unavailable");
                        }
                        response.close();
                    }
                });
                
            } catch (Exception e) {
                Log.e(TAG, "Error getting weather data", e);
                callback.onError("Unable to get weather information");
            }
        });
    }
    
    /**
     * Get news headlines (always fresh, no caching)
     */
    public void getNewsData(RealTimeDataCallback callback) {
        if (NEWS_API_KEY.equals("YOUR_NEWS_API_KEY")) {
            callback.onError("News service not configured. Please add News API key.");
            return;
        }

        executor.execute(() -> {
            try {
                String url = NEWS_BASE_URL +
                    "?country=us&category=technology&pageSize=5&apiKey=" + NEWS_API_KEY;

                Request request = new Request.Builder()
                    .url(url)
                    .build();

                httpClient.newCall(request).enqueue(new Callback() {
                    @Override
                    public void onFailure(Call call, IOException e) {
                        Log.e(TAG, "News API call failed", e);
                        callback.onError("Unable to get news information");
                    }

                    @Override
                    public void onResponse(Call call, Response response) throws IOException {
                        if (response.isSuccessful() && response.body() != null) {
                            String responseBody = response.body().string();
                            String formattedNews = formatNewsData(responseBody);
                            // No caching - always fresh news
                            callback.onSuccess(formattedNews);
                        } else {
                            callback.onError("News service temporarily unavailable");
                        }
                        response.close();
                    }
                });

            } catch (Exception e) {
                Log.e(TAG, "Error getting news data", e);
                callback.onError("Unable to get news information");
            }
        });
    }
    
    /**
     * Get comprehensive real-time context for AI
     */
    public void getRealTimeContext(RealTimeDataCallback callback) {
        StringBuilder contextBuilder = new StringBuilder();
        contextBuilder.append("Current real-time information:\n");
        
        CompletableFuture<String> timeFuture = new CompletableFuture<>();
        CompletableFuture<String> weatherFuture = new CompletableFuture<>();
        CompletableFuture<String> newsFuture = new CompletableFuture<>();
        
        // Get time data
        getCurrentTime(new RealTimeDataCallback() {
            @Override
            public void onSuccess(String data) {
                timeFuture.complete(data);
            }
            
            @Override
            public void onError(String error) {
                timeFuture.complete("Time: " + getLocalTimeInfo());
            }
        });
        
        // Get weather data
        getWeatherData(new RealTimeDataCallback() {
            @Override
            public void onSuccess(String data) {
                weatherFuture.complete(data);
            }
            
            @Override
            public void onError(String error) {
                weatherFuture.complete("Weather: Not available");
            }
        });
        
        // Get news data
        getNewsData(new RealTimeDataCallback() {
            @Override
            public void onSuccess(String data) {
                newsFuture.complete(data);
            }
            
            @Override
            public void onError(String error) {
                newsFuture.complete("News: Not available");
            }
        });
        
        // Combine all data when ready
        CompletableFuture.allOf(timeFuture, weatherFuture, newsFuture)
            .thenRun(() -> {
                try {
                    contextBuilder.append(timeFuture.get()).append("\n");
                    contextBuilder.append(weatherFuture.get()).append("\n");
                    contextBuilder.append(newsFuture.get()).append("\n");
                    callback.onSuccess(contextBuilder.toString());
                } catch (Exception e) {
                    Log.e(TAG, "Error combining real-time data", e);
                    callback.onError("Unable to get complete real-time information");
                }
            });
    }
    
    // Helper methods for local time with India timezone
    private String getLocalTimeInfo() {
        try {
            // Set India timezone
            java.util.TimeZone indiaTimeZone = java.util.TimeZone.getTimeZone("Asia/Kolkata");

            // Get current time in India timezone
            SimpleDateFormat indiaFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
            indiaFormat.setTimeZone(indiaTimeZone);
            String indiaTime = indiaFormat.format(new Date());

            // Get day of week and date info
            SimpleDateFormat dayFormat = new SimpleDateFormat("EEEE, MMMM dd, yyyy", Locale.getDefault());
            dayFormat.setTimeZone(indiaTimeZone);
            String dateInfo = dayFormat.format(new Date());

            // Get time of day
            java.util.Calendar cal = java.util.Calendar.getInstance(indiaTimeZone);
            int hour = cal.get(java.util.Calendar.HOUR_OF_DAY);
            String timeOfDay;
            if (hour < 6) timeOfDay = "Early Morning";
            else if (hour < 12) timeOfDay = "Morning";
            else if (hour < 17) timeOfDay = "Afternoon";
            else if (hour < 20) timeOfDay = "Evening";
            else timeOfDay = "Night";

            return String.format(
                "Current time in India: %s IST (%s)\nDate: %s\nTimezone: India Standard Time (IST, UTC+5:30)",
                indiaTime, timeOfDay, dateInfo
            );
        } catch (Exception e) {
            // Fallback to basic time
            SimpleDateFormat basicFormat = new SimpleDateFormat("EEEE, MMMM dd, yyyy 'at' hh:mm a", Locale.getDefault());
            return "Current time: " + basicFormat.format(new Date());
        }
    }
    
    // Cache management methods (no time caching - always real-time)
    
    private boolean isWeatherCacheValid() {
        return cachedWeatherData != null && (System.currentTimeMillis() - weatherCacheTime) < WEATHER_CACHE_DURATION_MS;
    }
    
    // News data is no longer cached - always real-time
    
    // Time data is no longer cached - always real-time
    
    private void cacheWeatherData(String data) {
        cachedWeatherData = data;
        weatherCacheTime = System.currentTimeMillis();
    }
    
    // News data is no longer cached - always real-time
    
    private void loadLocationSettings() {
        defaultLatitude = Double.parseDouble(preferences.getString("latitude", String.valueOf(defaultLatitude)));
        defaultLongitude = Double.parseDouble(preferences.getString("longitude", String.valueOf(defaultLongitude)));
        defaultCity = preferences.getString("city", defaultCity);
    }
    
    /**
     * Get world time data from API
     */
    private void getWorldTimeData(RealTimeDataCallback callback) {
        try {
            String url = WORLD_TIME_URL + "Etc/UTC";

            Request request = new Request.Builder()
                .url(url)
                .build();

            httpClient.newCall(request).enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    callback.onError("World time API unavailable");
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    if (response.isSuccessful() && response.body() != null) {
                        String responseBody = response.body().string();
                        callback.onSuccess(responseBody);
                    } else {
                        callback.onError("World time API error");
                    }
                    response.close();
                }
            });

        } catch (Exception e) {
            callback.onError("Error accessing world time API");
        }
    }

    /**
     * Combine local and world time data
     */
    private String combineTimeData(String localTime, String worldTimeData) {
        try {
            JsonObject worldTime = JsonParser.parseString(worldTimeData).getAsJsonObject();
            String utcTime = worldTime.get("utc_datetime").getAsString();
            String timezone = worldTime.get("timezone").getAsString();

            return localTime + " (UTC: " + utcTime.substring(11, 19) + ", Timezone: " + timezone + ")";
        } catch (Exception e) {
            return localTime; // Fallback to local time only
        }
    }

    /**
     * Format weather data for AI consumption
     */
    private String formatWeatherData(String weatherJson) {
        try {
            JsonObject weather = JsonParser.parseString(weatherJson).getAsJsonObject();
            JsonObject main = weather.getAsJsonObject("main");
            JsonObject weatherDesc = weather.getAsJsonArray("weather").get(0).getAsJsonObject();

            double temp = main.get("temp").getAsDouble();
            double feelsLike = main.get("feels_like").getAsDouble();
            int humidity = main.get("humidity").getAsInt();
            String description = weatherDesc.get("description").getAsString();
            String cityName = weather.get("name").getAsString();

            return String.format("Weather in %s: %s, %.1f°C (feels like %.1f°C), humidity %d%%",
                cityName, description, temp, feelsLike, humidity);

        } catch (Exception e) {
            Log.e(TAG, "Error formatting weather data", e);
            return "Weather: Unable to parse weather information";
        }
    }

    /**
     * Format news data for AI consumption
     */
    private String formatNewsData(String newsJson) {
        try {
            JsonObject news = JsonParser.parseString(newsJson).getAsJsonObject();
            StringBuilder newsBuilder = new StringBuilder();
            newsBuilder.append("Latest tech news headlines:\n");

            if (news.has("articles")) {
                for (int i = 0; i < Math.min(3, news.getAsJsonArray("articles").size()); i++) {
                    JsonObject article = news.getAsJsonArray("articles").get(i).getAsJsonObject();
                    String title = article.get("title").getAsString();
                    newsBuilder.append("- ").append(title).append("\n");
                }
            }

            return newsBuilder.toString();

        } catch (Exception e) {
            Log.e(TAG, "Error formatting news data", e);
            return "News: Unable to parse news information";
        }
    }

    /**
     * Check if real-time data is needed based on user query
     */
    public boolean isRealTimeDataNeeded(String userInput) {
        String lowerInput = userInput.toLowerCase();

        // Time-related keywords
        if (lowerInput.contains("time") || lowerInput.contains("date") ||
            lowerInput.contains("today") || lowerInput.contains("now") ||
            lowerInput.contains("current")) {
            return true;
        }

        // Weather-related keywords
        if (lowerInput.contains("weather") || lowerInput.contains("temperature") ||
            lowerInput.contains("rain") || lowerInput.contains("sunny") ||
            lowerInput.contains("cloudy") || lowerInput.contains("hot") ||
            lowerInput.contains("cold")) {
            return true;
        }

        // News-related keywords
        if (lowerInput.contains("news") || lowerInput.contains("headlines") ||
            lowerInput.contains("latest") || lowerInput.contains("happening")) {
            return true;
        }

        return false;
    }



    /**
     * Clear cached data (time/date/news are never cached - always real-time)
     */
    public void clearCache() {
        // Only clear weather cache - time and news are always real-time
        cachedWeatherData = null;
        weatherCacheTime = 0;
    }

    /**
     * Check if service is properly configured
     */
    public boolean isConfigured() {
        return !WEATHER_API_KEY.equals("YOUR_OPENWEATHER_API_KEY") ||
               !NEWS_API_KEY.equals("YOUR_NEWS_API_KEY");
    }

}
