package com.stemrobo.humanoid.utils;

import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.stemrobo.humanoid.activities.LMSVideoPlayerActivity;
import com.stemrobo.humanoid.activities.LMSYouTubePlayerActivity;

/**
 * Helper class for testing LMS functionality
 */
public class LMSTestHelper {
    private static final String TAG = "LMSTestHelper";
    
    /**
     * Test LMS introduction video
     */
    public static void testLMSIntroduction(Context context) {
        try {
            Intent intent = new Intent(context, LMSVideoPlayerActivity.class);
            intent.putExtra(LMSVideoPlayerActivity.EXTRA_VIDEO_PATH, "LMS-intro.mp4");
            intent.putExtra(LMSVideoPlayerActivity.EXTRA_VIDEO_TITLE, "STEM-Xpert LMS Introduction");
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            context.startActivity(intent);
            
            Log.d(TAG, "✅ LMS Introduction test launched successfully");
        } catch (Exception e) {
            Log.e(TAG, "❌ Error testing LMS Introduction", e);
        }
    }
    
    /**
     * Test LMS class video
     */
    public static void testLMSClassVideo(Context context, int classNumber) {
        try {
            Intent intent = new Intent(context, LMSYouTubePlayerActivity.class);
            intent.putExtra(LMSYouTubePlayerActivity.EXTRA_CLASS_NUMBER, classNumber);
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            context.startActivity(intent);
            
            Log.d(TAG, "✅ LMS Class " + classNumber + " test launched successfully");
        } catch (Exception e) {
            Log.e(TAG, "❌ Error testing LMS Class " + classNumber, e);
        }
    }
    
    /**
     * Test voice commands for LMS (Multilingual)
     */
    public static void logLMSCommands() {
        Log.d(TAG, "📋 Multilingual LMS Voice Commands:");

        Log.d(TAG, "🇺🇸 English:");
        Log.d(TAG, "  🎬 Introduction: 'introduce lms', 'lms introduction', 'lms intro'");
        Log.d(TAG, "  📚 Class Videos: '1st lms', 'class 5 lms', 'grade 10 lms', 'five lms'");

        Log.d(TAG, "🇮🇳 Malayalam:");
        Log.d(TAG, "  🎬 Introduction: 'എൽഎംഎസ് പരിചയപ്പെടുത്തുക', 'എൽഎംഎസ് ആമുഖം'");
        Log.d(TAG, "  📚 Class Videos: 'അഞ്ച് എൽഎംএസ്', 'അഞ്ചാം എൽഎംഎസ്', 'ക്ലാസ് എൽഎംഎസ്'");

        Log.d(TAG, "🇮🇳 Hindi:");
        Log.d(TAG, "  🎬 Introduction: 'एलएमएस का परिचय', 'एलएमएस परिचय'");
        Log.d(TAG, "  📚 Class Videos: 'पांच एलएमएस', 'पांचवा एलएमएस', 'कक्षा एलएमएस'");

        Log.d(TAG, "🇸🇦 Arabic:");
        Log.d(TAG, "  🎬 Introduction: 'تعريف إل إم إس', 'مقدمة إل إم إس'");
        Log.d(TAG, "  📚 Class Videos: 'خمسة إل إم إس', 'الخامس إل إم إس', 'صف إل إم إس'");

        Log.d(TAG, "  📱 Supported classes: 1st through 12th grade in all languages");
        Log.d(TAG, "  🎯 Example: Say wake word then LMS command in any supported language");
    }

    /**
     * Test specific multilingual LMS commands
     */
    public static void testMultilingualCommands(Context context) {
        Log.d(TAG, "🧪 Testing Multilingual LMS Commands:");

        // Test English commands
        Log.d(TAG, "Testing English: 'introduce lms'");
        Log.d(TAG, "Testing English: 'class 5 lms'");

        // Test Malayalam commands
        Log.d(TAG, "Testing Malayalam: 'എൽഎംഎസ് പരിചയം'");
        Log.d(TAG, "Testing Malayalam: 'അഞ്ചാം എൽഎംഎസ്'");

        // Test Hindi commands
        Log.d(TAG, "Testing Hindi: 'एलएमएस परिचय'");
        Log.d(TAG, "Testing Hindi: 'पांचवा एलएमएस'");

        // Test Arabic commands
        Log.d(TAG, "Testing Arabic: 'مقدمة إل إم إس'");
        Log.d(TAG, "Testing Arabic: 'الخامس إل إم إس'");

        Log.d(TAG, "✅ All multilingual test commands logged. Check VoiceRecognitionService logs for processing details.");
    }
}
