<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_primary"
    android:padding="16dp">

    <!-- Header Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="16dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="🎭 Action Presets"
            android:textColor="@color/text_primary"
            android:textSize="20sp"
            android:textStyle="bold" />

        <Button
            android:id="@+id/btn_add_preset"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:text="➕ Add"
            android:textSize="12sp"
            android:backgroundTint="@color/robot_primary"
            android:textColor="@color/white"
            android:layout_marginEnd="8dp" />

        <Button
            android:id="@+id/btn_preset_help"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:text="❓"
            android:textSize="12sp"
            android:backgroundTint="@color/robot_secondary"
            android:textColor="@color/white" />

    </LinearLayout>

    <!-- Search and Filter Section -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        app:cardBackgroundColor="@color/control_button_background"
        app:cardCornerRadius="8dp"
        app:cardElevation="2dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="12dp">

            <!-- Search Bar -->
            <EditText
                android:id="@+id/edit_search_presets"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:hint="🔍 Search presets..."
                android:textColor="@color/text_primary"
                android:textColorHint="@color/text_secondary"
                android:background="@drawable/edit_text_background"
                android:padding="8dp"
                android:layout_marginBottom="8dp" />

            <!-- Category Filter -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Category:"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp"
                    android:layout_marginEnd="8dp" />

                <Spinner
                    android:id="@+id/spinner_category_filter"
                    android:layout_width="0dp"
                    android:layout_height="40dp"
                    android:layout_weight="1"
                    android:background="@drawable/spinner_background" />

                <Button
                    android:id="@+id/btn_clear_filters"
                    android:layout_width="60dp"
                    android:layout_height="35dp"
                    android:text="Clear"
                    android:textSize="10sp"
                    android:backgroundTint="@color/status_warning"
                    android:textColor="@color/white"
                    android:layout_marginStart="8dp" />

            </LinearLayout>

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- Quick Actions Section -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        app:cardBackgroundColor="@color/control_button_background"
        app:cardCornerRadius="8dp"
        app:cardElevation="2dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="12dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="⚡ Quick Actions"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:textStyle="bold"
                android:layout_marginBottom="8dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center">

                <Button
                    android:id="@+id/btn_stop_all"
                    android:layout_width="0dp"
                    android:layout_height="40dp"
                    android:layout_weight="1"
                    android:text="🛑 Stop All"
                    android:textSize="10sp"
                    android:backgroundTint="@color/status_error"
                    android:textColor="@color/white"
                    android:layout_marginEnd="4dp" />

                <Button
                    android:id="@+id/btn_import_presets"
                    android:layout_width="0dp"
                    android:layout_height="40dp"
                    android:layout_weight="1"
                    android:text="📥 Import"
                    android:textSize="10sp"
                    android:backgroundTint="@color/robot_accent"
                    android:textColor="@color/white"
                    android:layout_marginHorizontal="4dp" />

                <Button
                    android:id="@+id/btn_export_presets"
                    android:layout_width="0dp"
                    android:layout_height="40dp"
                    android:layout_weight="1"
                    android:text="📤 Export"
                    android:textSize="10sp"
                    android:backgroundTint="@color/robot_accent"
                    android:textColor="@color/white"
                    android:layout_marginStart="4dp" />

            </LinearLayout>

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- Presets List -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        app:cardBackgroundColor="@color/control_button_background"
        app:cardCornerRadius="8dp"
        app:cardElevation="2dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <!-- List Header -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:padding="12dp"
                android:background="@color/robot_primary">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Preset Name"
                    android:textColor="@color/white"
                    android:textSize="12sp"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="60dp"
                    android:layout_height="wrap_content"
                    android:text="Duration"
                    android:textColor="@color/white"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:gravity="center" />

                <TextView
                    android:layout_width="80dp"
                    android:layout_height="wrap_content"
                    android:text="Actions"
                    android:textColor="@color/white"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:gravity="center" />

            </LinearLayout>

            <!-- Presets RecyclerView -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_presets"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:padding="8dp"
                android:clipToPadding="false" />

            <!-- Empty State -->
            <LinearLayout
                android:id="@+id/layout_empty_state"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="center"
                android:padding="32dp"
                android:visibility="gone">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="🎭"
                    android:textSize="48sp"
                    android:layout_marginBottom="16dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="No presets found"
                    android:textColor="@color/text_secondary"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Create your first preset to get started!"
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp"
                    android:gravity="center"
                    android:layout_marginBottom="16dp" />

                <Button
                    android:id="@+id/btn_create_first_preset"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="➕ Create First Preset"
                    android:backgroundTint="@color/robot_primary"
                    android:textColor="@color/white" />

            </LinearLayout>

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- Status Bar -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginTop="8dp">

        <TextView
            android:id="@+id/text_preset_count"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="0 presets"
            android:textColor="@color/text_secondary"
            android:textSize="12sp" />

        <TextView
            android:id="@+id/text_execution_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Ready"
            android:textColor="@color/status_success"
            android:textSize="12sp"
            android:textStyle="bold" />

    </LinearLayout>

</LinearLayout>
