<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/content_background"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Header -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="⚙️ Robot Settings"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            android:gravity="center"
            android:layout_marginBottom="24dp" />

        <!-- Robot Configuration Section -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="🤖 Robot Configuration"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/robot_primary"
            android:layout_marginBottom="12dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/settings_section_background"
            android:padding="16dp"
            android:layout_marginBottom="16dp">

            <!-- Robot Type Switch -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="12dp">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Robot Type"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Switch between omnidirectional (mecanum) and normal two-wheel setup"
                        android:textColor="@color/text_secondary"
                        android:textSize="12sp" />

                </LinearLayout>

                <Switch
                    android:id="@+id/robot_type_switch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="true" />

            </LinearLayout>

            <TextView
                android:id="@+id/robot_type_status"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="✅ Mecanum Wheel (Omnidirectional) Mode"
                android:textColor="@color/robot_primary"
                android:textSize="12sp"
                android:gravity="center" />

        </LinearLayout>

        <!-- Language Settings Section -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="🌍 Language Settings"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/robot_primary"
            android:layout_marginBottom="12dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/settings_section_background"
            android:padding="16dp"
            android:layout_marginBottom="16dp">

            <!-- Language Selection -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Select Language / भाषा चुनें / ഭാഷ തിരഞ്ഞെടുക്കുക / اختر اللغة"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:layout_marginBottom="8dp" />

            <Spinner
                android:id="@+id/language_spinner"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/message_input_background"
                android:padding="12dp"
                android:layout_marginBottom="12dp" />

            <!-- Language Status -->
            <TextView
                android:id="@+id/language_status"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="✅ Language set - Wake word: Hey Robot"
                android:textColor="@color/robot_primary"
                android:textSize="12sp"
                android:gravity="center" />

        </LinearLayout>

        <!-- AI Settings Section -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="🤖 AI Settings"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/robot_primary"
            android:layout_marginBottom="12dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/settings_section_background"
            android:padding="16dp"
            android:layout_marginBottom="16dp">

            <!-- Max AI Output -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Maximum AI Response Length"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:layout_marginBottom="8dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <SeekBar
                    android:id="@+id/max_ai_output_seekbar"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:max="300"
                    android:progress="150" />

                <TextView
                    android:id="@+id/max_ai_output_value"
                    android:layout_width="80dp"
                    android:layout_height="wrap_content"
                    android:text="150 words"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp"
                    android:gravity="end" />

            </LinearLayout>

            <!-- AI Context Caching -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="AI Context Memory (Conversation Pairs)"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="8dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <SeekBar
                    android:id="@+id/ai_context_pairs_seekbar"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:max="20"
                    android:progress="10" />

                <TextView
                    android:id="@+id/ai_context_pairs_value"
                    android:layout_width="80dp"
                    android:layout_height="wrap_content"
                    android:text="10 pairs"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp"
                    android:gravity="end" />

            </LinearLayout>

        </LinearLayout>

        <!-- Voice Settings Section -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="🎤 Voice Settings"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/robot_primary"
            android:layout_marginBottom="12dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/settings_section_background"
            android:padding="16dp"
            android:layout_marginBottom="16dp">

            <!-- Speech Speed -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Speech Speed"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:layout_marginBottom="8dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="16dp">

                <SeekBar
                    android:id="@+id/speech_speed_seekbar"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:max="200"
                    android:progress="100" />

                <TextView
                    android:id="@+id/speech_speed_value"
                    android:layout_width="60dp"
                    android:layout_height="wrap_content"
                    android:text="1.0x"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp"
                    android:gravity="end" />

            </LinearLayout>

            <!-- Voice Gender -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Voice Gender"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:layout_marginBottom="8dp" />

            <Spinner
                android:id="@+id/voice_gender_spinner"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:background="@drawable/settings_section_background"
                android:padding="12dp" />

            <!-- Microphone Sensitivity -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Microphone Sensitivity"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:layout_marginBottom="8dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="16dp">

                <SeekBar
                    android:id="@+id/mic_sensitivity_seekbar"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:max="100"
                    android:progress="50" />

                <TextView
                    android:id="@+id/mic_sensitivity_value"
                    android:layout_width="60dp"
                    android:layout_height="wrap_content"
                    android:text="50%"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp"
                    android:gravity="end" />

            </LinearLayout>

            <!-- Microphone Duration -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Listening Duration (Silence Timeout)"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:layout_marginBottom="8dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <SeekBar
                    android:id="@+id/mic_duration_seekbar"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:max="100"
                    android:progress="40" />

                <TextView
                    android:id="@+id/mic_duration_value"
                    android:layout_width="60dp"
                    android:layout_height="wrap_content"
                    android:text="4.0s"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp"
                    android:gravity="end" />

            </LinearLayout>

            <!-- Idle Listening Timeout -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Idle Listening Timeout"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="8dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <SeekBar
                    android:id="@+id/idle_timeout_seekbar"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:max="300"
                    android:progress="60" />

                <TextView
                    android:id="@+id/idle_timeout_value"
                    android:layout_width="60dp"
                    android:layout_height="wrap_content"
                    android:text="60s"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp"
                    android:gravity="end" />

            </LinearLayout>

        </LinearLayout>

        <!-- Voice Command Duration Settings Section -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="⏱️ Voice Command Duration"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/robot_primary"
            android:layout_marginBottom="12dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/settings_section_background"
            android:padding="16dp"
            android:layout_marginBottom="16dp">

            <!-- Forward/Backward Duration -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Forward/Backward Movement Duration"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:layout_marginBottom="8dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <SeekBar
                    android:id="@+id/voice_forward_duration_seekbar"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:max="100"
                    android:progress="30" />

                <TextView
                    android:id="@+id/voice_forward_duration_value"
                    android:layout_width="60dp"
                    android:layout_height="wrap_content"
                    android:text="3.0s"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp"
                    android:gravity="end" />

            </LinearLayout>

            <!-- Left/Right Duration -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Left/Right Turn Duration"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="8dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <SeekBar
                    android:id="@+id/voice_side_duration_seekbar"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:max="100"
                    android:progress="20" />

                <TextView
                    android:id="@+id/voice_side_duration_value"
                    android:layout_width="60dp"
                    android:layout_height="wrap_content"
                    android:text="2.0s"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp"
                    android:gravity="end" />

            </LinearLayout>

            <!-- Mecanum Wheel Movement Durations -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="🔄 Mecanum Wheel Movement Durations"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:textStyle="bold"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="8dp" />

            <!-- Slide Movement Duration -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Slide Left/Right Duration"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:layout_marginBottom="8dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <SeekBar
                    android:id="@+id/voice_slide_duration_seekbar"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:max="100"
                    android:progress="25" />

                <TextView
                    android:id="@+id/voice_slide_duration_value"
                    android:layout_width="60dp"
                    android:layout_height="wrap_content"
                    android:text="2.5s"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp"
                    android:gravity="end" />

            </LinearLayout>

            <!-- Diagonal Movement Duration -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Diagonal Movement Duration"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="8dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <SeekBar
                    android:id="@+id/voice_diagonal_duration_seekbar"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:max="100"
                    android:progress="30" />

                <TextView
                    android:id="@+id/voice_diagonal_duration_value"
                    android:layout_width="60dp"
                    android:layout_height="wrap_content"
                    android:text="3.0s"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp"
                    android:gravity="end" />

            </LinearLayout>

            <!-- Rotation Duration -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Rotation Duration"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="8dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <SeekBar
                    android:id="@+id/voice_rotate_duration_seekbar"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:max="100"
                    android:progress="20" />

                <TextView
                    android:id="@+id/voice_rotate_duration_value"
                    android:layout_width="60dp"
                    android:layout_height="wrap_content"
                    android:text="2.0s"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp"
                    android:gravity="end" />

            </LinearLayout>

            <!-- Test Voice Commands Button -->
            <Button
                android:id="@+id/test_voice_commands_button"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="🎤 Test Voice Commands"
                android:textColor="@color/white"
                android:background="@drawable/control_button_background"
                android:layout_marginTop="16dp" />

        </LinearLayout>

        <!-- ESP32 Settings Section -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="🔧 ESP32 Connection"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/robot_primary"
            android:layout_marginBottom="12dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/settings_section_background"
            android:padding="16dp"
            android:layout_marginBottom="16dp">

            <!-- Connection Mode Status -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Connection Mode Status"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:layout_marginBottom="8dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:background="@drawable/message_input_background"
                android:padding="12dp"
                android:layout_marginBottom="16dp">

                <TextView
                    android:id="@+id/connection_mode_indicator"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="🔌 USB Connected"
                    android:textColor="@color/status_connected"
                    android:textSize="14sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/connection_ip_display"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Direct USB"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp" />

            </LinearLayout>

            <!-- Manual Communication Mode Control -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Manual Communication Mode"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="8dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:background="@drawable/message_input_background"
                android:padding="12dp"
                android:layout_marginBottom="16dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Force Communication Mode"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp" />

                <Button
                    android:id="@+id/btn_toggle_communication_mode"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Switch to WiFi"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:backgroundTint="@color/robot_primary"
                    android:textColor="@android:color/white"
                    android:padding="8dp" />

            </LinearLayout>

            <!-- USB Debug Controls -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="USB Debug Controls"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="8dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:background="@drawable/message_input_background"
                android:padding="12dp"
                android:layout_marginBottom="16dp">

                <Button
                    android:id="@+id/btn_scan_usb"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Scan USB"
                    android:textSize="12sp"
                    android:backgroundTint="@color/robot_primary"
                    android:textColor="@android:color/white"
                    android:layout_marginEnd="8dp" />

                <Button
                    android:id="@+id/btn_usb_debug"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="USB Info"
                    android:textSize="12sp"
                    android:backgroundTint="@color/robot_primary"
                    android:textColor="@android:color/white" />

            </LinearLayout>

            <!-- USB Fix Button -->
            <Button
                android:id="@+id/btn_fix_usb"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="🔧 Fix USB Connection"
                android:textSize="14sp"
                android:textStyle="bold"
                android:backgroundTint="@android:color/holo_red_dark"
                android:textColor="@android:color/white"
                android:padding="12dp"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="16dp" />

            <!-- USB Serial Configuration -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="USB Serial Configuration"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="8dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:background="@drawable/message_input_background"
                android:padding="12dp"
                android:layout_marginBottom="8dp">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:layout_marginEnd="8dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Baud Rate"
                        android:textColor="@color/text_primary"
                        android:textSize="12sp" />

                    <Spinner
                        android:id="@+id/baud_rate_spinner"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Port Number"
                        android:textColor="@color/text_primary"
                        android:textSize="12sp" />

                    <EditText
                        android:id="@+id/port_number_input"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="0"
                        android:text="0"
                        android:inputType="number"
                        android:textColor="@color/text_primary"
                        android:textColorHint="@color/text_hint"
                        android:background="@drawable/message_input_background"
                        android:padding="8dp"
                        android:layout_marginTop="4dp" />

                </LinearLayout>

            </LinearLayout>

            <!-- Motor Controller IP -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Motor Controller IP"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:layout_marginBottom="8dp" />

            <EditText
                android:id="@+id/motor_controller_ip_input"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="***************"
                android:text="***************"
                android:inputType="textNoSuggestions"
                android:textColor="@color/text_primary"
                android:textColorHint="@color/text_hint"
                android:background="@drawable/message_input_background"
                android:padding="12dp"
                android:layout_marginBottom="16dp" />

            <!-- Servo Controller IP -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Servo Controller IP"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:layout_marginBottom="8dp" />

            <EditText
                android:id="@+id/servo_controller_ip_input"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="***************"
                android:text="***************"
                android:inputType="textNoSuggestions"
                android:textColor="@color/text_primary"
                android:textColorHint="@color/text_hint"
                android:background="@drawable/message_input_background"
                android:padding="12dp"
                android:layout_marginBottom="16dp" />

            <!-- Sensor Controller IP -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Sensor Controller IP"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:layout_marginBottom="8dp" />

            <EditText
                android:id="@+id/sensor_controller_ip_input"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="***************"
                android:text="***************"
                android:inputType="textNoSuggestions"
                android:textColor="@color/text_primary"
                android:textColorHint="@color/text_hint"
                android:background="@drawable/message_input_background"
                android:padding="12dp"
                android:layout_marginBottom="16dp" />

            <!-- ESP32 Port -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="ESP32 Port"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:layout_marginBottom="8dp" />

            <EditText
                android:id="@+id/esp32_port_input"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="80"
                android:text="80"
                android:inputType="number"
                android:textColor="@color/text_primary"
                android:textColorHint="@color/text_hint"
                android:background="@drawable/message_input_background"
                android:padding="12dp"
                android:layout_marginBottom="16dp" />

            <!-- Test Connection Button -->
            <Button
                android:id="@+id/test_connection_button"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="🔍 Test All Connections"
                android:textColor="@color/text_primary"
                android:background="@drawable/control_button_background"
                android:padding="12dp"
                android:layout_marginBottom="8dp" />

            <!-- Test WiFi Connection Button -->
            <Button
                android:id="@+id/test_wifi_button"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="📶 Test WiFi Connection"
                android:textColor="@color/text_primary"
                android:background="@drawable/control_button_background"
                android:padding="12dp"
                android:layout_marginBottom="8dp" />

            <!-- Force WiFi Mode Button -->
            <Button
                android:id="@+id/force_wifi_button"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="🔄 Force WiFi Mode"
                android:textColor="@color/text_primary"
                android:background="@drawable/control_button_background"
                android:padding="12dp" />

        </LinearLayout>

        <!-- Walking Animation Settings Section -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="🚶 Walking Animation Settings"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/robot_primary"
            android:layout_marginBottom="12dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/settings_section_background"
            android:padding="16dp"
            android:layout_marginBottom="16dp">

            <!-- Walking Speed Control -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Walking Speed (Arm Movement Timing)"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:layout_marginBottom="8dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="16dp">

                <SeekBar
                    android:id="@+id/walking_speed_seekbar"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:max="28"
                    android:progress="6" />

                <TextView
                    android:id="@+id/walking_speed_value"
                    android:layout_width="80dp"
                    android:layout_height="wrap_content"
                    android:text="800ms"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp"
                    android:gravity="end" />

            </LinearLayout>

            <!-- Left Arm Rest Position -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Left Arm Rest Position"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:layout_marginBottom="8dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="16dp">

                <SeekBar
                    android:id="@+id/left_arm_rest_seekbar"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:max="180"
                    android:progress="0" />

                <TextView
                    android:id="@+id/left_arm_rest_value"
                    android:layout_width="60dp"
                    android:layout_height="wrap_content"
                    android:text="0°"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp"
                    android:gravity="end" />

            </LinearLayout>

            <!-- Right Arm Rest Position -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Right Arm Rest Position"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:layout_marginBottom="8dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="16dp">

                <SeekBar
                    android:id="@+id/right_arm_rest_seekbar"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:max="180"
                    android:progress="0" />

                <TextView
                    android:id="@+id/right_arm_rest_value"
                    android:layout_width="60dp"
                    android:layout_height="wrap_content"
                    android:text="0°"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp"
                    android:gravity="end" />

            </LinearLayout>

            <!-- Test Walking Animation Button -->
            <Button
                android:id="@+id/test_walking_button"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="🚶 Test Walking Animation"
                android:textColor="@color/white"
                android:background="@drawable/control_button_background"
                android:layout_marginTop="8dp" />

        </LinearLayout>

        <!-- Feature Settings Section -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="🎛️ Feature Settings"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/robot_primary"
            android:layout_marginBottom="12dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/settings_section_background"
            android:padding="16dp"
            android:layout_marginBottom="24dp">

            <!-- Wake Word Detection -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="16dp">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Wake Word Detection"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Enable 'Hey Robot' activation"
                        android:textColor="@color/text_secondary"
                        android:textSize="12sp" />

                </LinearLayout>

                <Switch
                    android:id="@+id/wake_word_switch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="true" />

            </LinearLayout>

            <!-- Auto Listen -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="16dp">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Auto Listen Mode"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Continue listening after wake word"
                        android:textColor="@color/text_secondary"
                        android:textSize="12sp" />

                </LinearLayout>

                <Switch
                    android:id="@+id/auto_listen_switch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="true" />

            </LinearLayout>

            <!-- Voice Feedback -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="16dp">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Voice Feedback"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Enable spoken responses"
                        android:textColor="@color/text_secondary"
                        android:textSize="12sp" />

                </LinearLayout>

                <Switch
                    android:id="@+id/voice_feedback_switch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="true" />

            </LinearLayout>

            <!-- Debug Mode -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Debug Mode"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Show detailed logs and diagnostics"
                        android:textColor="@color/text_secondary"
                        android:textSize="12sp" />

                </LinearLayout>

                <Switch
                    android:id="@+id/debug_mode_switch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="false" />

            </LinearLayout>

            <!-- Ultrasonic Sensor Enable/Disable -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginTop="16dp">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="📡 Ultrasonic Sensor"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Enable distance monitoring and human detection"
                        android:textColor="@color/text_secondary"
                        android:textSize="12sp" />

                </LinearLayout>

                <Switch
                    android:id="@+id/ultrasonic_sensor_switch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="true" />

            </LinearLayout>

            <!-- Ultrasonic Face Integration -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginTop="16dp">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="🤝 Ultrasonic + Face Integration"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Auto-activate camera and greet faces when detected by ultrasonic sensor"
                        android:textColor="@color/text_secondary"
                        android:textSize="12sp" />

                </LinearLayout>

                <Switch
                    android:id="@+id/ultrasonic_face_integration_switch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="false" />

            </LinearLayout>

            <!-- Smart Greeting Settings Section -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="🤖 Smart Greeting Settings"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                android:textStyle="bold"
                android:layout_marginTop="24dp"
                android:layout_marginBottom="16dp" />

            <!-- Face Detection Duration -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Face Detection Duration (seconds)"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:layout_marginBottom="8dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="16dp">

                <SeekBar
                    android:id="@+id/face_detection_duration_seekbar"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:max="95"
                    android:progress="15"
                    android:layout_marginEnd="12dp" />

                <TextView
                    android:id="@+id/face_detection_duration_value"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="2.0s"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp"
                    android:minWidth="40dp"
                    android:gravity="center" />

            </LinearLayout>

            <!-- Greeting Distance Threshold -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Greeting Distance Threshold (cm)"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:layout_marginBottom="8dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="16dp">

                <SeekBar
                    android:id="@+id/greeting_distance_seekbar"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:max="100"
                    android:progress="30"
                    android:layout_marginEnd="12dp" />

                <TextView
                    android:id="@+id/greeting_distance_value"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="30cm"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp"
                    android:minWidth="50dp"
                    android:gravity="center" />

            </LinearLayout>

            <!-- Greeting Cooldown Time -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Greeting Cooldown Time (seconds)"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:layout_marginBottom="8dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="16dp">

                <SeekBar
                    android:id="@+id/greeting_cooldown_seekbar"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:max="55"
                    android:progress="10"
                    android:layout_marginEnd="12dp" />

                <TextView
                    android:id="@+id/greeting_cooldown_value"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="15s"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp"
                    android:minWidth="40dp"
                    android:gravity="center" />

            </LinearLayout>

            <!-- Handshake Duration -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Handshake Hold Duration (seconds)"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:layout_marginBottom="8dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="16dp">

                <SeekBar
                    android:id="@+id/handshake_duration_seekbar"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:max="29"
                    android:progress="4"
                    android:layout_marginEnd="12dp" />

                <TextView
                    android:id="@+id/handshake_duration_value"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="5s"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp"
                    android:minWidth="40dp"
                    android:gravity="center" />

            </LinearLayout>

            <!-- Disable Smart Greeting -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="16dp">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="🚫 Disable Smart Greeting"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Turn off automatic face detection and greeting"
                        android:textColor="@color/text_secondary"
                        android:textSize="12sp" />

                </LinearLayout>

                <Switch
                    android:id="@+id/disable_smart_greeting_switch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="false" />

            </LinearLayout>

            <!-- Background Camera -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="16dp">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="📹 Background Camera"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Always-on camera for continuous face detection"
                        android:textColor="@color/text_secondary"
                        android:textSize="12sp" />

                </LinearLayout>

                <Switch
                    android:id="@+id/background_camera_switch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="true" />

            </LinearLayout>

            <!-- Ultrasonic Detection Distance -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginTop="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="🎯 Detection Distance Threshold"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp"
                    android:layout_marginBottom="8dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <EditText
                        android:id="@+id/ultrasonic_detection_distance_input"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:hint="10"
                        android:text="10"
                        android:inputType="number"
                        android:textColor="@color/text_primary"
                        android:textColorHint="@color/text_hint"
                        android:background="@drawable/message_input_background"
                        android:padding="12dp"
                        android:layout_marginEnd="8dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="cm"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp" />

                </LinearLayout>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Objects within this distance will trigger face detection and greeting"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp"
                    android:layout_marginTop="4dp" />

            </LinearLayout>

        </LinearLayout>

        <!-- ESP32 Serial Monitor Section -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="📟 ESP32 Serial Monitor"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/robot_primary"
            android:layout_marginBottom="12dp"
            android:layout_marginTop="16dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/settings_section_background"
            android:padding="16dp"
            android:layout_marginBottom="16dp">

            <!-- Monitor Controls -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="12dp">

                <Button
                    android:id="@+id/btn_start_monitor"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Start Monitor"
                    android:textColor="@color/text_primary"
                    android:background="@drawable/control_button_background"
                    android:layout_marginEnd="4dp"
                    android:padding="8dp" />

                <Button
                    android:id="@+id/btn_stop_monitor"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Stop Monitor"
                    android:textColor="@color/text_primary"
                    android:background="@drawable/control_button_background"
                    android:layout_marginHorizontal="4dp"
                    android:padding="8dp" />

                <Button
                    android:id="@+id/btn_clear_monitor"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Clear"
                    android:textColor="@color/text_primary"
                    android:background="@drawable/control_button_background"
                    android:layout_marginStart="4dp"
                    android:padding="8dp" />

            </LinearLayout>

            <!-- Auto-scroll and timestamp options -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="12dp">

                <Switch
                    android:id="@+id/switch_auto_scroll"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="true" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Auto-scroll"
                    android:textColor="@color/text_secondary"
                    android:layout_marginStart="8dp" />

                <Switch
                    android:id="@+id/switch_show_timestamps"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="true" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Timestamps"
                    android:textColor="@color/text_secondary"
                    android:layout_marginStart="8dp" />

            </LinearLayout>

            <!-- Serial Monitor Display -->
            <ScrollView
                android:id="@+id/serial_monitor_scroll"
                android:layout_width="match_parent"
                android:layout_height="300dp"
                android:background="@color/content_background"
                android:padding="8dp"
                android:layout_marginBottom="12dp">

                <TextView
                    android:id="@+id/serial_monitor_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Serial Monitor - Ready\nClick 'Start Monitor' to begin monitoring ESP32 communication..."
                    android:textColor="@color/text_primary"
                    android:textSize="12sp"
                    android:fontFamily="monospace"
                    android:textIsSelectable="true" />

            </ScrollView>

            <!-- Send Command Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <EditText
                    android:id="@+id/serial_command_input"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:hint="Enter ESP32 command..."
                    android:textColor="@color/text_primary"
                    android:textColorHint="@color/text_hint"
                    android:background="@drawable/message_input_background"
                    android:padding="12dp"
                    android:layout_marginEnd="8dp"
                    android:fontFamily="monospace" />

                <Button
                    android:id="@+id/btn_send_command"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Send"
                    android:textColor="@color/text_primary"
                    android:background="@drawable/send_button_background"
                    android:padding="12dp" />

            </LinearLayout>

        </LinearLayout>

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center"
            android:layout_marginBottom="32dp">

            <Button
                android:id="@+id/reset_button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Reset to Defaults"
                android:textColor="@color/text_primary"
                android:background="@drawable/control_button_background"
                android:layout_marginEnd="8dp"
                android:padding="12dp" />

            <Button
                android:id="@+id/save_button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Save Settings"
                android:textColor="@color/text_primary"
                android:background="@drawable/send_button_background"
                android:layout_marginStart="8dp"
                android:padding="12dp" />

        </LinearLayout>

    </LinearLayout>

</ScrollView>
