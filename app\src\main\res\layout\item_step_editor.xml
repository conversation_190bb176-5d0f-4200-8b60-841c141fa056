<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardBackgroundColor="@color/background_secondary"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp">

        <!-- Step Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <!-- Step Number -->
            <TextView
                android:id="@+id/text_step_number"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:text="1"
                android:textColor="@color/white"
                android:textSize="14sp"
                android:textStyle="bold"
                android:gravity="center"
                android:background="@drawable/step_number_background" />

            <!-- Step Name -->
            <EditText
                android:id="@+id/edit_step_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:hint="Step name"
                android:textColor="@color/text_primary"
                android:textColorHint="@color/text_secondary"
                android:textSize="16sp"
                android:textStyle="bold"
                android:background="@android:color/transparent"
                android:layout_marginStart="12dp"
                android:layout_marginEnd="8dp" />

            <!-- Step Menu -->
            <Button
                android:id="@+id/btn_step_menu"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:text="..."
                android:textSize="16sp"
                android:backgroundTint="@color/robot_secondary"
                android:textColor="@color/white" />

        </LinearLayout>

        <!-- Timing Controls -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="12dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Time:"
                android:textSize="16sp"
                android:layout_marginEnd="8dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Start:"
                android:textColor="@color/text_secondary"
                android:textSize="12sp"
                android:layout_marginEnd="4dp" />

            <EditText
                android:id="@+id/edit_start_time"
                android:layout_width="60dp"
                android:layout_height="32dp"
                android:hint="0.0"
                android:textColor="@color/text_primary"
                android:textSize="12sp"
                android:inputType="numberDecimal"
                android:background="@drawable/edit_text_background"
                android:padding="4dp"
                android:gravity="center"
                android:layout_marginEnd="8dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Duration:"
                android:textColor="@color/text_secondary"
                android:textSize="12sp"
                android:layout_marginEnd="4dp" />

            <EditText
                android:id="@+id/edit_duration"
                android:layout_width="60dp"
                android:layout_height="32dp"
                android:hint="1.0"
                android:textColor="@color/text_primary"
                android:textSize="12sp"
                android:inputType="numberDecimal"
                android:background="@drawable/edit_text_background"
                android:padding="4dp"
                android:gravity="center"
                android:layout_marginEnd="4dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="s"
                android:textColor="@color/text_secondary"
                android:textSize="12sp" />

        </LinearLayout>

        <!-- Actions Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Actions"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:textStyle="bold" />

            <Button
                android:id="@+id/btn_add_action"
                android:layout_width="wrap_content"
                android:layout_height="32dp"
                android:text="+ Add"
                android:textSize="10sp"
                android:backgroundTint="@color/status_success"
                android:textColor="@color/white" />

        </LinearLayout>

        <!-- Actions List -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_actions"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:nestedScrollingEnabled="false" />

        <!-- Empty Actions State -->
        <LinearLayout
            android:id="@+id/layout_empty_actions"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:orientation="vertical"
            android:gravity="center"
            android:visibility="visible">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Actions"
                android:textSize="20sp"
                android:layout_marginBottom="4dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="No actions in this step"
                android:textColor="@color/text_secondary"
                android:textSize="12sp" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
