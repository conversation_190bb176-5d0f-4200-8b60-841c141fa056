package com.stemrobo.humanoid.fragments;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.Spinner;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.stemrobo.humanoid.R;
import com.stemrobo.humanoid.adapters.ChatAdapter;
import com.stemrobo.humanoid.language.LanguageManager;
import com.stemrobo.humanoid.models.ChatMessage;
import com.stemrobo.humanoid.services.VoiceRecognitionService;

import java.util.ArrayList;
import java.util.List;

/**
 * Fragment for AI Chat interface
 * Displays conversation between user and AI robot
 */
public class ChatFragment extends Fragment {
    private static final String TAG = "ChatFragment";
    
    private RecyclerView chatRecyclerView;
    private ChatAdapter chatAdapter;
    private List<ChatMessage> chatMessages;
    private EditText messageInput;
    private ImageButton sendButton;
    private ImageButton pushToTalkButton;

    // Live transcription components
    private TextView liveTranscriptionText;

    // Language support
    private Spinner languageSpinner;
    private LanguageManager languageManager;
    
    // Broadcast receiver for voice recognition updates
    private BroadcastReceiver voiceReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            try {
                if (intent == null || intent.getAction() == null) {
                    return;
                }

                String action = intent.getAction();
                if (VoiceRecognitionService.ACTION_VOICE_RESULT.equals(action)) {
                    String userMessage = intent.getStringExtra(VoiceRecognitionService.EXTRA_VOICE_TEXT);
                    String aiResponse = intent.getStringExtra(VoiceRecognitionService.EXTRA_AI_RESPONSE);

                    if (userMessage != null && !userMessage.trim().isEmpty()) {
                        addUserMessage(userMessage);
                    }

                    if (aiResponse != null && !aiResponse.trim().isEmpty()) {
                        addAIMessage(aiResponse);
                    }
                } else if (VoiceRecognitionService.ACTION_LISTENING_STATE.equals(action)) {
                    boolean isListening = intent.getBooleanExtra(VoiceRecognitionService.EXTRA_IS_LISTENING, false);
                    updateListeningIndicator(isListening);
                } else if (VoiceRecognitionService.ACTION_TRANSCRIPTION_UPDATE.equals(action)) {
                    String partialText = intent.getStringExtra(VoiceRecognitionService.EXTRA_PARTIAL_TEXT);
                    updateLiveTranscription(partialText);
                }
            } catch (Exception e) {
                Log.e(TAG, "Error in voice receiver", e);
            }
        }
    };
    
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
                           @Nullable Bundle savedInstanceState) {
        try {
            View view = inflater.inflate(R.layout.fragment_chat, container, false);

            initializeViews(view);
            setupRecyclerView();
            setupInputHandlers();

            Log.d(TAG, "ChatFragment created successfully");
            return view;
        } catch (Exception e) {
            Log.e(TAG, "Error creating ChatFragment", e);

            // Create fallback view with multilingual content
            TextView fallbackView = new TextView(getContext());
            fallbackView.setText(createMultilingualContent());
            fallbackView.setPadding(32, 32, 32, 32);
            fallbackView.setTextSize(16);
            fallbackView.setTextColor(0xFFFFFFFF);

            return fallbackView;
        }
    }
    
    private void initializeViews(View view) {
        try {
            chatRecyclerView = view.findViewById(R.id.chat_recycler_view);
            messageInput = view.findViewById(R.id.message_input);
            sendButton = view.findViewById(R.id.send_button);

            // Initialize transcription views (now inside input area)
            liveTranscriptionText = view.findViewById(R.id.live_transcription_text);

            // Initialize language support
            languageSpinner = view.findViewById(R.id.language_spinner);
            languageManager = new LanguageManager(requireContext());
            setupLanguageSpinner();

            // Initialize chat messages list
            chatMessages = new ArrayList<>();

            // Add multilingual welcome message
            addAIMessage("🤖 Hello! I'm Guruji, your STEM Robot assistant!\n\n" +
                        "🇺🇸 Say 'Hey Guruji' to start!\n" +
                        "🇮🇳 'हे गुरुजी' कहें!\n" +
                        "🇮🇳 'ഹേ ഗുരുജി' പറയുക!\n" +
                        "🇸🇦 قل 'مرحبا جوروجي'!\n\n" +
                        "🎤 Voice commands ready in all languages!");

            Log.d(TAG, "Views initialized successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error initializing views", e);
            // Initialize empty list to prevent crashes
            if (chatMessages == null) {
                chatMessages = new ArrayList<>();
            }
        }
    }

    private void setupLanguageSpinner() {
        try {
            if (languageSpinner != null && languageManager != null) {
                // Create adapter for language spinner
                List<String> languages = languageManager.getSupportedLanguages();
                android.widget.ArrayAdapter<String> adapter = new android.widget.ArrayAdapter<>(
                    requireContext(),
                    R.layout.spinner_item_language,
                    languages
                );
                adapter.setDropDownViewResource(R.layout.spinner_dropdown_item_language);
                languageSpinner.setAdapter(adapter);

                // Set current language selection
                List<String> languageCodes = languageManager.getSupportedLanguageCodes();
                String currentLang = languageManager.getCurrentLanguage();
                int position = languageCodes.indexOf(currentLang);
                if (position >= 0) {
                    languageSpinner.setSelection(position);
                }

                // Set language change listener with crash protection
                languageSpinner.setOnItemSelectedListener(new android.widget.AdapterView.OnItemSelectedListener() {
                    private boolean isInitialSelection = true;

                    @Override
                    public void onItemSelected(android.widget.AdapterView<?> parent, android.view.View view, int position, long id) {
                        try {
                            // Skip the initial selection to prevent crash on setup
                            if (isInitialSelection) {
                                isInitialSelection = false;
                                return;
                            }

                            if (languageManager == null) {
                                android.util.Log.e(TAG, "LanguageManager is null during language selection");
                                return;
                            }

                            List<String> codes = languageManager.getSupportedLanguageCodes();
                            if (codes == null || position >= codes.size()) {
                                android.util.Log.e(TAG, "Invalid language position: " + position);
                                return;
                            }

                            String selectedLanguage = codes.get(position);
                            String previousLanguage = languageManager.getCurrentLanguage();

                            // Only process if language actually changed
                            if (!selectedLanguage.equals(previousLanguage)) {
                                android.util.Log.d(TAG, "🌍 Processing language change from " + previousLanguage + " to " + selectedLanguage);

                                // Update language manager
                                languageManager.setCurrentLanguage(selectedLanguage);

                                // Send language change to voice service with error handling
                                sendLanguageChangeToVoiceService(selectedLanguage);

                                // Update UI to show current language with more details
                                try {
                                    String languageName = languageManager.getCurrentLanguageName();
                                    String wakeWord = languageManager.getWakeWord();

                                    // Only add system message if chat is ready
                                    if (chatMessages != null && chatAdapter != null) {
                                        addSystemMessage("🌍 Language changed to: " + languageName + "\n🎤 Wake word: " + wakeWord + "\n✅ Speech recognition updated");
                                    }
                                } catch (Exception e) {
                                    android.util.Log.e(TAG, "Error updating UI after language change", e);
                                }

                                android.util.Log.d(TAG, "✅ Language successfully changed from " + previousLanguage + " to " + selectedLanguage);
                            }
                        } catch (Exception e) {
                            android.util.Log.e(TAG, "Critical error in language selection", e);
                            // Try to recover by resetting to English
                            try {
                                if (languageManager != null) {
                                    languageManager.setCurrentLanguage("en");
                                }
                            } catch (Exception recoveryError) {
                                android.util.Log.e(TAG, "Failed to recover from language selection error", recoveryError);
                            }
                        }
                    }

                    @Override
                    public void onNothingSelected(android.widget.AdapterView<?> parent) {
                        // Do nothing
                    }
                });
            }
        } catch (Exception e) {
            android.util.Log.e(TAG, "Error setting up language spinner", e);
        }
    }

    private void sendLanguageChangeToVoiceService(String languageCode) {
        try {
            if (languageCode == null || languageCode.trim().isEmpty()) {
                android.util.Log.e(TAG, "Invalid language code: " + languageCode);
                return;
            }

            android.util.Log.d(TAG, "📡 Sending language change broadcast: " + languageCode);

            android.content.Intent intent = new android.content.Intent("com.stemrobo.humanoid.LANGUAGE_CHANGED");
            intent.putExtra("language_code", languageCode);

            // Add extra safety checks
            if (getContext() != null) {
                androidx.localbroadcastmanager.content.LocalBroadcastManager.getInstance(getContext()).sendBroadcast(intent);
                android.util.Log.d(TAG, "✅ Language change broadcast sent successfully");
            } else {
                android.util.Log.e(TAG, "Context is null, cannot send language change broadcast");
            }
        } catch (Exception e) {
            android.util.Log.e(TAG, "❌ Critical error sending language change broadcast", e);
            // Don't rethrow - handle gracefully
        }
    }

    private void setupRecyclerView() {
        try {
            if (chatRecyclerView != null && chatMessages != null) {
                chatAdapter = new ChatAdapter(chatMessages);
                chatRecyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
                chatRecyclerView.setAdapter(chatAdapter);

                // Auto-scroll to bottom when new messages are added
                chatAdapter.registerAdapterDataObserver(new RecyclerView.AdapterDataObserver() {
                    @Override
                    public void onItemRangeInserted(int positionStart, int itemCount) {
                        super.onItemRangeInserted(positionStart, itemCount);
                        if (chatRecyclerView != null && chatMessages != null) {
                            chatRecyclerView.scrollToPosition(chatMessages.size() - 1);
                        }
                    }
                });

                Log.d(TAG, "RecyclerView setup successfully");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error setting up RecyclerView", e);
        }
    }
    
    private void setupInputHandlers() {
        sendButton.setOnClickListener(v -> {
            String message = messageInput.getText().toString().trim();
            if (!message.isEmpty()) {
                sendTextMessage(message);
                messageInput.setText("");
            }
        });
        
        // Handle enter key in input field
        messageInput.setOnEditorActionListener((v, actionId, event) -> {
            String message = messageInput.getText().toString().trim();
            if (!message.isEmpty()) {
                sendTextMessage(message);
                messageInput.setText("");
                return true;
            }
            return false;
        });
    }
    
    private void sendTextMessage(String message) {
        // Add user message to chat
        addUserMessage(message);

        // Send message to voice recognition service for processing
        Intent intent = new Intent(VoiceRecognitionService.ACTION_PROCESS_TEXT_INPUT);
        intent.putExtra(VoiceRecognitionService.EXTRA_TEXT_INPUT, message);
        LocalBroadcastManager.getInstance(requireContext()).sendBroadcast(intent);
    }
    
    private void addUserMessage(String message) {
        try {
            if (message != null && chatMessages != null && chatAdapter != null) {
                ChatMessage chatMessage = new ChatMessage(message, ChatMessage.TYPE_USER, System.currentTimeMillis());
                chatMessages.add(chatMessage);
                chatAdapter.notifyItemInserted(chatMessages.size() - 1);

                Log.d(TAG, "User message added: " + message);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error adding user message", e);
        }
    }

    private void addAIMessage(String message) {
        try {
            if (message != null && chatMessages != null && chatAdapter != null) {
                ChatMessage chatMessage = new ChatMessage(message, ChatMessage.TYPE_AI, System.currentTimeMillis());
                chatMessages.add(chatMessage);
                chatAdapter.notifyItemInserted(chatMessages.size() - 1);

                Log.d(TAG, "AI message added: " + message);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error adding AI message", e);
        }
    }

    private void addSystemMessage(String message) {
        try {
            if (message != null && chatMessages != null && chatAdapter != null) {
                ChatMessage chatMessage = new ChatMessage(message, ChatMessage.TYPE_SYSTEM, System.currentTimeMillis());
                chatMessages.add(chatMessage);
                chatAdapter.notifyItemInserted(chatMessages.size() - 1);

                Log.d(TAG, "System message added: " + message);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error adding system message", e);
        }
    }
    
    private void updateListeningIndicator(boolean isListening) {
        try {
            // Update UI to show listening state
            if (isListening) {
                // Show transcription overlay inside input when listening starts
                if (liveTranscriptionText != null) {
                    liveTranscriptionText.setVisibility(android.view.View.VISIBLE);
                    liveTranscriptionText.setText("🎤 Listening...");
                }
                addSystemMessage("🎤 Listening...");
            } else {
                // Hide transcription overlay when listening stops
                if (liveTranscriptionText != null) {
                    liveTranscriptionText.setVisibility(android.view.View.GONE);
                }
            }
        } catch (Exception e) {
            // Handle any view access errors gracefully
        }
    }

    /**
     * Update live transcription display with partial speech results
     */
    private void updateLiveTranscription(String partialText) {
        if (liveTranscriptionText != null && partialText != null) {
            liveTranscriptionText.setText(partialText);
        }
    }
    
    @Override
    public void onResume() {
        super.onResume();

        try {
            // Register broadcast receiver for voice updates
            IntentFilter filter = new IntentFilter();
            filter.addAction(VoiceRecognitionService.ACTION_VOICE_RESULT);
            filter.addAction(VoiceRecognitionService.ACTION_LISTENING_STATE);
            filter.addAction(VoiceRecognitionService.ACTION_TRANSCRIPTION_UPDATE);
            LocalBroadcastManager.getInstance(requireContext()).registerReceiver(voiceReceiver, filter);

            Log.d(TAG, "ChatFragment resumed, registered voice receiver");
        } catch (Exception e) {
            Log.e(TAG, "Error registering voice receiver", e);
        }
    }

    @Override
    public void onPause() {
        super.onPause();

        try {
            // Unregister broadcast receiver
            LocalBroadcastManager.getInstance(requireContext()).unregisterReceiver(voiceReceiver);

            Log.d(TAG, "ChatFragment paused, unregistered voice receiver");
        } catch (Exception e) {
            Log.e(TAG, "Error unregistering voice receiver", e);
        }
    }

    public void clearChat() {
        try {
            if (chatMessages != null && chatAdapter != null) {
                chatMessages.clear();
                chatAdapter.notifyDataSetChanged();
                addAIMessage("Chat cleared. How can I help you?");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error clearing chat", e);
        }
    }

    /**
     * Create multilingual content for fallback display
     */
    private String createMultilingualContent() {
        return "🤖 Guruji - STEM Robot Assistant\n" +
               "🌍 Multilingual Support Active\n\n" +

               "🇺🇸 English: Hello! Say 'Hey Guruji' to start!\n" +
               "🇮🇳 हिन्दी: नमस्ते! 'हे गुरुजी' कहें!\n" +
               "🇮🇳 മലയാളം: നമസ്കാരം! 'ഹേ ഗുരുജി' പറയുക!\n" +
               "🇸🇦 العربية: مرحباً! قل 'مرحبا جوروجي'!\n\n" +

               "✅ Voice Commands Ready\n" +
               "✅ AI Chat Active\n" +
               "✅ ESP32 Connected\n" +
               "✅ Multilingual Support\n\n" +

               "🎤 Try saying the wake word in any language!\n" +
               "🔊 Guruji will respond in your language\n" +
               "⚙️ Go to Settings to configure language\n\n" +

               "Ready for voice interaction! 🚀";
    }
}
