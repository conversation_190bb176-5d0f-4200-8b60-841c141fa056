package com.stemrobo.humanoid.fragments;

import android.app.AlertDialog;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.stemrobo.humanoid.MainActivity;
import com.stemrobo.humanoid.R;
import com.stemrobo.humanoid.communication.ESP32CommunicationManager;

import java.util.Map;

public class StatusFragment extends Fragment implements ESP32CommunicationManager.CommunicationListener {
    
    private TextView motorStatus, servoStatus, sensorStatus;
    private TextView batteryLevel, heartRate, temperature;
    private TextView statusUltrasonicDistance, statusHumanDetected, statusAutoDetection;
    private Button testConnectionButton;
    private ESP32CommunicationManager communicationManager;
    
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, 
                           @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_status, container, false);
    }
    
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        initializeViews(view);
        
        // Get communication manager from MainActivity
        if (getActivity() instanceof MainActivity) {
            communicationManager = ((MainActivity) getActivity()).getCommunicationManager();
            communicationManager.setCommunicationListener(this);
        }
        
        updateConnectionStatus();
    }
    
    private void initializeViews(View view) {
        motorStatus = view.findViewById(R.id.motor_status_text);
        servoStatus = view.findViewById(R.id.servo_status_text);
        sensorStatus = view.findViewById(R.id.sensor_status_text);
        batteryLevel = view.findViewById(R.id.battery_level_text);
        heartRate = view.findViewById(R.id.heart_rate_text);
        temperature = view.findViewById(R.id.temperature_text);
        testConnectionButton = view.findViewById(R.id.test_connection_button);

        // Human Detection status elements
        statusUltrasonicDistance = view.findViewById(R.id.status_ultrasonic_distance);
        statusHumanDetected = view.findViewById(R.id.status_human_detected);
        statusAutoDetection = view.findViewById(R.id.status_auto_detection);

        // Set up test connection button
        testConnectionButton.setOnClickListener(v -> testAllConnections());
    }
    
    private void updateConnectionStatus() {
        if (communicationManager != null) {
            Map<String, Boolean> status = communicationManager.getAllConnectionStatus();
            
            updateStatusText(motorStatus, status.getOrDefault("motor", false));
            updateStatusText(servoStatus, status.getOrDefault("servo", false));
            updateStatusText(sensorStatus, status.getOrDefault("sensor", false));
        }
    }
    
    private void updateStatusText(TextView textView, boolean connected) {
        if (getContext() != null) {
            if (connected) {
                textView.setText("Connected");
                textView.setTextColor(getContext().getColor(R.color.status_connected));
            } else {
                textView.setText("Disconnected");
                textView.setTextColor(getContext().getColor(R.color.status_disconnected));
            }
        }
    }
    
    // Communication listener methods
    @Override
    public void onCommandSent(String controllerId, com.stemrobo.humanoid.models.RobotCommand command) {
        // Update UI if needed
    }
    
    @Override
    public void onResponseReceived(String controllerId, com.stemrobo.humanoid.models.RobotResponse response) {
        if (getActivity() != null) {
            getActivity().runOnUiThread(() -> {
                // Update sensor data if available
                if (response.getData() != null) {
                    Map<String, Object> data = response.getData();
                    
                    if (data.containsKey("battery")) {
                        batteryLevel.setText(data.get("battery") + "%");
                    }
                    if (data.containsKey("heartRate")) {
                        heartRate.setText(data.get("heartRate") + " BPM");
                    }
                    if (data.containsKey("temperature")) {
                        temperature.setText(data.get("temperature") + "°C");
                    }

                    // Update ultrasonic sensor data
                    if (data.containsKey("ultrasonic_distance")) {
                        statusUltrasonicDistance.setText(data.get("ultrasonic_distance") + " cm");
                    }
                    if (data.containsKey("human_detected")) {
                        boolean humanDetected = Boolean.parseBoolean(data.get("human_detected").toString());
                        statusHumanDetected.setText(humanDetected ? "Yes" : "No");
                        if (getContext() != null) {
                            statusHumanDetected.setTextColor(getContext().getColor(
                                humanDetected ? R.color.status_connected : R.color.status_disconnected));
                        }
                    }
                    if (data.containsKey("human_detection_enabled")) {
                        boolean autoDetectionEnabled = Boolean.parseBoolean(data.get("human_detection_enabled").toString());
                        statusAutoDetection.setText(autoDetectionEnabled ? "Enabled" : "Disabled");
                        if (getContext() != null) {
                            statusAutoDetection.setTextColor(getContext().getColor(
                                autoDetectionEnabled ? R.color.status_connected : R.color.status_disconnected));
                        }
                    }
                }
            });
        }
    }
    
    @Override
    public void onConnectionStatusChanged(String controllerId, boolean connected) {
        if (getActivity() != null) {
            getActivity().runOnUiThread(() -> {
                switch (controllerId) {
                    case "motor":
                        updateStatusText(motorStatus, connected);
                        break;
                    case "servo":
                        updateStatusText(servoStatus, connected);
                        break;
                    case "sensor":
                        updateStatusText(sensorStatus, connected);
                        break;
                }
            });
        }
    }
    
    @Override
    public void onError(String controllerId, String error) {
        // Handle errors if needed
    }

    private void testAllConnections() {
        if (communicationManager == null) {
            Toast.makeText(getContext(), "Communication manager not available", Toast.LENGTH_SHORT).show();
            return;
        }

        // Show testing dialog
        AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
        builder.setTitle("Testing ESP32 Connections");
        builder.setMessage("Testing connections to all ESP32 controllers...");
        builder.setCancelable(false);
        AlertDialog testDialog = builder.create();
        testDialog.show();

        // Test all connections
        communicationManager.testAllConnections(new ESP32CommunicationManager.ConnectionTestListener() {
            @Override
            public void onTestResult(String controllerId, boolean success, String message) {
                if (getActivity() != null) {
                    getActivity().runOnUiThread(() -> {
                        String status = success ? "✓" : "✗";
                        String resultMessage = controllerId.toUpperCase() + " Controller: " + status + " " + message;

                        // Update connection status immediately
                        switch (controllerId) {
                            case "motor":
                                updateStatusText(motorStatus, success);
                                break;
                            case "servo":
                                updateStatusText(servoStatus, success);
                                break;
                            case "sensor":
                                updateStatusText(sensorStatus, success);
                                break;
                        }
                    });
                }
            }

            @Override
            public void onAllTestsComplete(Map<String, Boolean> results) {
                if (getActivity() != null) {
                    getActivity().runOnUiThread(() -> {
                        testDialog.dismiss();

                        // Show final results
                        StringBuilder resultText = new StringBuilder("Connection Test Results:\n\n");
                        resultText.append("Motor Controller: ").append(results.get("motor") ? "✓ Connected" : "✗ Failed").append("\n");
                        resultText.append("Servo Controller: ").append(results.get("servo") ? "✓ Connected" : "✗ Failed").append("\n");
                        resultText.append("Sensor Controller: ").append(results.get("sensor") ? "✓ Connected" : "✗ Failed").append("\n");

                        int connectedCount = 0;
                        for (Boolean connected : results.values()) {
                            if (connected) connectedCount++;
                        }

                        resultText.append("\nConnected: ").append(connectedCount).append("/3 controllers");

                        AlertDialog.Builder resultBuilder = new AlertDialog.Builder(getContext());
                        resultBuilder.setTitle("Connection Test Complete");
                        resultBuilder.setMessage(resultText.toString());
                        resultBuilder.setPositiveButton("OK", null);
                        resultBuilder.show();
                    });
                }
            }
        });
    }
}
