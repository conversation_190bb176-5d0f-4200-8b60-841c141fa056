package com.stemrobo.humanoid.ai;

import android.util.Log;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * Service for integrating with Google Gemini AI API
 * Handles conversation and general AI responses
 */
public class GeminiAIService {
    private static final String TAG = "GeminiAIService";
    
    // Replace with your actual Gemini API key
    private static final String API_KEY = "AIzaSyAZZ_pO5Mx7_M7Kbrfi21h77xfyMNziPnA";
    private static final String BASE_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent";

    // Guruji Identity System Prompt
    private static final String ROBOT_SYSTEM_PROMPT =
        "My name is <PERSON><PERSON>, an AI assistant created by STEM-Xpert company. I am designed to help with robotics, " +
        "programming, and educational content. I provide responses optimized for speech synthesis without emojis, " +
        "brackets, or special characters. I keep responses short and crisp for general questions, but provide " +
        "detailed moderate-sized answers when asked specific questions. I represent STEM-Xpert's commitment to " +
        "quality STEM education and robotics innovation.";
    
    private final OkHttpClient httpClient;
    private final ExecutorService executor;
    
    public GeminiAIService() {
        httpClient = new OkHttpClient();
        executor = Executors.newCachedThreadPool();
    }
    
    /**
     * Get AI response for user input
     * @param userInput The user's message
     * @param callback Callback to handle the response
     */
    public void getAIResponse(String userInput, AIResponseCallback callback) {
        executor.execute(() -> {
            try {
                String response = generateResponse(userInput);
                callback.onSuccess(response);
            } catch (Exception e) {
                Log.e(TAG, "Error getting AI response", e);
                callback.onError("Sorry, I'm having trouble thinking right now. Please try again.");
            }
        });
    }

    /**
     * Get AI response for user input with language support
     * @param userInput The user's message
     * @param languageCode Language code for response (null for default)
     * @param callback Callback to handle the response
     */
    public void getAIResponse(String userInput, String languageCode, AIResponseCallback callback) {
        executor.execute(() -> {
            try {
                String response = callGeminiAPI(userInput, languageCode);
                callback.onSuccess(response);
            } catch (Exception e) {
                android.util.Log.e(TAG, "Error getting AI response", e);
                callback.onError("Sorry, I'm having trouble thinking right now. Please try again.");
            }
        });
    }

    /**
     * Get AI response with conversation context for memory-aware conversations
     * @param userInput The user's message
     * @param languageCode Language code for response (null for default)
     * @param conversationContext Previous conversation context
     * @param callback Callback to handle the response
     */
    public void getAIResponseWithContext(String userInput, String languageCode, String conversationContext, AIResponseCallback callback) {
        executor.execute(() -> {
            try {
                long startTime = System.currentTimeMillis();
                String response = callGeminiAPIWithContext(userInput, languageCode, conversationContext);
                long responseTime = System.currentTimeMillis() - startTime;

                // Create enhanced callback with timing information
                if (callback instanceof EnhancedAIResponseCallback) {
                    ((EnhancedAIResponseCallback) callback).onSuccessWithTiming(response, responseTime, conversationContext);
                } else {
                    callback.onSuccess(response);
                }
            } catch (Exception e) {
                android.util.Log.e(TAG, "Error getting AI response with context", e);
                callback.onError("Sorry, I'm having trouble thinking right now. Please try again.");
            }
        });
    }

    /**
     * Get AI response synchronously (for testing)
     */
    public CompletableFuture<String> getAIResponseAsync(String userInput) {
        CompletableFuture<String> future = new CompletableFuture<>();
        
        getAIResponse(userInput, new AIResponseCallback() {
            @Override
            public void onSuccess(String response) {
                future.complete(response);
            }
            
            @Override
            public void onError(String error) {
                future.completeExceptionally(new RuntimeException(error));
            }
        });
        
        return future;
    }
    
    private String generateResponse(String userInput) throws IOException, JSONException {
        // Use real Gemini API call
        return callGeminiAPI(userInput);
    }
    

    
    /**
     * Make actual Gemini API call (implement when API key is available)
     */
    private String callGeminiAPI(String userInput) throws IOException, JSONException {
        return callGeminiAPI(userInput, null);
    }

    public String callGeminiAPI(String userInput, String languageCode) throws IOException, JSONException {
        JSONObject requestJson = new JSONObject();
        JSONArray contents = new JSONArray();

        // Add system prompt as first message
        JSONObject systemContent = new JSONObject();
        JSONArray systemParts = new JSONArray();
        JSONObject systemPart = new JSONObject();
        // Use language-specific system prompt if language code is provided
        String systemPrompt = ROBOT_SYSTEM_PROMPT;
        if (languageCode != null) {
            try {
                com.stemrobo.humanoid.language.LanguageManager tempLangManager =
                    new com.stemrobo.humanoid.language.LanguageManager(null);
                tempLangManager.setCurrentLanguage(languageCode);
                systemPrompt = tempLangManager.getSystemPrompt() + " " + tempLangManager.getLanguageInstruction();
            } catch (Exception e) {
                // Fall back to default prompt if language manager fails
                systemPrompt = ROBOT_SYSTEM_PROMPT;
            }
        }
        systemPart.put("text", systemPrompt);
        systemParts.put(systemPart);
        systemContent.put("parts", systemParts);
        systemContent.put("role", "user");
        contents.put(systemContent);

        // Add user input as second message
        JSONObject userContent = new JSONObject();
        JSONArray userParts = new JSONArray();
        JSONObject userPart = new JSONObject();
        userPart.put("text", userInput);
        userParts.put(userPart);
        userContent.put("parts", userParts);
        userContent.put("role", "user");
        contents.put(userContent);

        requestJson.put("contents", contents);
        
        RequestBody body = RequestBody.create(
            requestJson.toString(),
            MediaType.get("application/json; charset=utf-8")
        );
        
        Request request = new Request.Builder()
            .url(BASE_URL + "?key=" + API_KEY)
            .post(body)
            .addHeader("Content-Type", "application/json")
            .build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected response code: " + response);
            }
            
            String responseBody = response.body().string();
            JSONObject responseJson = new JSONObject(responseBody);
            
            JSONArray candidates = responseJson.getJSONArray("candidates");
            if (candidates.length() > 0) {
                JSONObject candidate = candidates.getJSONObject(0);
                JSONObject responseContent = candidate.getJSONObject("content");
                JSONArray responseParts = responseContent.getJSONArray("parts");
                if (responseParts.length() > 0) {
                    JSONObject responsePart = responseParts.getJSONObject(0);
                    return responsePart.getString("text");
                }
            }
            
            return "I'm sorry, I couldn't generate a response right now.";
        }
    }

    /**
     * Call Gemini API with conversation context for memory-aware responses
     */
    private String callGeminiAPIWithContext(String userInput, String languageCode, String conversationContext) throws IOException, JSONException {
        // Build enhanced prompt with conversation context
        String enhancedPrompt = buildContextualPrompt(userInput, conversationContext, languageCode);

        // Use the existing API call method with the enhanced prompt
        return callGeminiAPI(enhancedPrompt, languageCode);
    }

    /**
     * Build a contextual prompt that includes conversation history
     */
    private String buildContextualPrompt(String userInput, String conversationContext, String languageCode) {
        StringBuilder promptBuilder = new StringBuilder();

        // Add system context for the AI
        promptBuilder.append("You are a helpful AI assistant for a STEM robotics educational platform. ");
        promptBuilder.append("You should provide educational, engaging, and age-appropriate responses. ");

        // Add language instruction if specified
        if (languageCode != null && !languageCode.isEmpty() && !languageCode.equals("en")) {
            promptBuilder.append("Please respond in the language with code: ").append(languageCode).append(". ");
        }

        // Add conversation context if available
        if (conversationContext != null && !conversationContext.trim().isEmpty()) {
            promptBuilder.append("\n\n").append(conversationContext).append("\n");
        }

        // Add current user input
        promptBuilder.append("\nCurrent question: ").append(userInput);

        // Add instruction for contextual response
        if (conversationContext != null && !conversationContext.trim().isEmpty()) {
            promptBuilder.append("\n\nPlease provide a response that takes into account our previous conversation while directly addressing the current question.");
        }

        return promptBuilder.toString();
    }
    
    /**
     * Callback interface for AI responses
     */
    public interface AIResponseCallback {
        void onSuccess(String response);
        void onError(String error);
    }

    /**
     * Enhanced callback interface with timing and context information
     */
    public interface EnhancedAIResponseCallback extends AIResponseCallback {
        void onSuccessWithTiming(String response, long responseTimeMs, String contextUsed);
    }
    
    /**
     * Clean up resources
     */
    public void shutdown() {
        executor.shutdown();
    }
}
