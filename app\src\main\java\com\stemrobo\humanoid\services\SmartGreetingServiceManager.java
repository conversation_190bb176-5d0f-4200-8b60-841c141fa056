package com.stemrobo.humanoid.services;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.IBinder;
import android.speech.tts.TextToSpeech;
import java.util.Locale;
import com.stemrobo.humanoid.communication.ESP32CommunicationManager;

/**
 * Smart Greeting Service Manager
 * Coordinates background camera service and ESP32 distance monitoring
 * Provides unified Smart Greeting functionality
 */
public class SmartGreetingServiceManager implements 
    BackgroundCameraService.FaceDetectionCallback,
    ESP32CommunicationManager.DistanceUpdateCallback {
    
    private static final String TAG = "SmartGreetingServiceManager";
    
    // Singleton instance
    private static SmartGreetingServiceManager instance;
    
    // Context and services
    private Context context;
    private BackgroundCameraService backgroundCameraService;
    private ESP32CommunicationManager communicationManager;
    private TextToSpeech textToSpeech;
    
    // Service binding
    private boolean isCameraServiceBound = false;
    private ServiceConnection cameraServiceConnection;
    
    // Smart Greeting state
    private boolean isSmartGreetingEnabled = false;
    private boolean isMonitoringActive = false;
    
    // Current status
    private int currentFaceCount = 0;
    private float currentDistance = 999.0f;
    private boolean greetingInProgress = false;
    private long lastGreetingTime = 0;
    
    // Settings (configurable via SharedPreferences)
    private float GREETING_DISTANCE_THRESHOLD = 30.0f; // 30cm (configurable)
    private long GREETING_COOLDOWN = 15000; // 15 seconds (configurable)
    private long GREETING_RETURN_DELAY = 6000; // 6 seconds (configurable)
    
    // Callback interface for status updates
    public interface SmartGreetingCallback {
        void onFaceCountUpdated(int faceCount);
        void onDistanceUpdated(float distance);
        void onGreetingTriggered(String greetingType);
        void onSmartGreetingStatusChanged(boolean isActive);
    }
    
    private SmartGreetingCallback callback;
    
    private SmartGreetingServiceManager() {
        // Private constructor for singleton
    }
    
    public static synchronized SmartGreetingServiceManager getInstance() {
        if (instance == null) {
            instance = new SmartGreetingServiceManager();
        }
        return instance;
    }
    
    /**
     * Initialize the Smart Greeting Service Manager
     */
    public void initialize(Context context) {
        this.context = context;
        this.communicationManager = ESP32CommunicationManager.getInstance();

        // Set up service connection
        setupCameraServiceConnection();

        // Load settings from SharedPreferences
        loadSettingsFromPreferences();

        // CRITICAL: Initialize the actual SmartGreetingManager that handles the greeting logic
        initializeSmartGreetingManager();

        System.out.println(TAG + ": Smart Greeting Service Manager initialized");
    }

    /**
     * Initialize the actual SmartGreetingManager for greeting logic
     */
    private void initializeSmartGreetingManager() {
        try {
            // Get the singleton SmartGreetingManager instance
            com.stemrobo.humanoid.behaviors.SmartGreetingManager greetingManager =
                com.stemrobo.humanoid.behaviors.SmartGreetingManager.getInstance();

            if (greetingManager != null) {
                // Initialize it if not already done
                greetingManager.initialize();

                // Load settings from SharedPreferences
                if (context != null) {
                    android.content.SharedPreferences prefs = context.getSharedPreferences("com.stemrobo.humanoid_preferences", android.content.Context.MODE_PRIVATE);
                    greetingManager.loadSettingsFromPreferences(prefs);
                }

                System.out.println(TAG + ": SmartGreetingManager integrated with service manager");
            }
        } catch (Exception e) {
            System.out.println(TAG + ": Error initializing SmartGreetingManager: " + e.getMessage());
        }
    }

    /**
     * Load settings from SharedPreferences
     */
    private void loadSettingsFromPreferences() {
        if (context != null) {
            try {
                android.content.SharedPreferences prefs = context.getSharedPreferences("com.stemrobo.humanoid_preferences", android.content.Context.MODE_PRIVATE);

                // Load configurable settings
                GREETING_DISTANCE_THRESHOLD = prefs.getInt("greeting_distance_threshold", 30);
                GREETING_COOLDOWN = prefs.getInt("greeting_cooldown", 15000);
                GREETING_RETURN_DELAY = prefs.getInt("greeting_return_delay", 6000);

                // Check if Smart Greeting is disabled
                boolean smartGreetingDisabled = prefs.getBoolean("smart_greeting_disabled", false);
                isSmartGreetingEnabled = !smartGreetingDisabled;

                System.out.println(TAG + ": Settings loaded from preferences");
                System.out.println(TAG + ": Distance threshold: " + GREETING_DISTANCE_THRESHOLD + "cm");
                System.out.println(TAG + ": Cooldown: " + GREETING_COOLDOWN + "ms");
                System.out.println(TAG + ": Return delay: " + GREETING_RETURN_DELAY + "ms");
                System.out.println(TAG + ": Smart Greeting enabled: " + isSmartGreetingEnabled);
            } catch (Exception e) {
                System.out.println(TAG + ": Error loading settings: " + e.getMessage());
            }
        }
    }
    
    /**
     * Start Smart Greeting monitoring
     */
    public void startSmartGreeting() {
        if (isMonitoringActive) {
            System.out.println(TAG + ": Smart Greeting already active");
            return;
        }
        
        isSmartGreetingEnabled = true;
        isMonitoringActive = true;
        
        System.out.println(TAG + ": Starting Smart Greeting monitoring");
        
        // Start background camera service
        startBackgroundCameraService();
        
        // DON'T start distance monitoring here - let SmartGreetingManager handle it
        // The SmartGreetingManager is already registered as a CommunicationListener
        // and receives distance updates through onResponseReceived()
        System.out.println(TAG + ": Distance monitoring delegated to SmartGreetingManager");
        
        // Notify callback
        if (callback != null) {
            callback.onSmartGreetingStatusChanged(true);
        }
        
        System.out.println(TAG + ": Smart Greeting monitoring started successfully");
    }
    
    /**
     * Stop Smart Greeting monitoring
     */
    public void stopSmartGreeting() {
        if (!isMonitoringActive) {
            return;
        }
        
        isSmartGreetingEnabled = false;
        isMonitoringActive = false;
        
        System.out.println(TAG + ": Stopping Smart Greeting monitoring");
        
        // Stop background camera service
        stopBackgroundCameraService();
        
        // DON'T stop distance monitoring - let SmartGreetingManager handle it
        System.out.println(TAG + ": Distance monitoring remains with SmartGreetingManager");
        
        // Reset status
        currentFaceCount = 0;
        currentDistance = 999.0f;
        greetingInProgress = false;
        
        // Notify callback
        if (callback != null) {
            callback.onFaceCountUpdated(0);
            callback.onDistanceUpdated(999.0f);
            callback.onSmartGreetingStatusChanged(false);
        }
        
        System.out.println(TAG + ": Smart Greeting monitoring stopped");
    }
    
    /**
     * Set up camera service connection
     */
    private void setupCameraServiceConnection() {
        cameraServiceConnection = new ServiceConnection() {
            @Override
            public void onServiceConnected(ComponentName name, IBinder service) {
                System.out.println(TAG + ": Background camera service connected");
                BackgroundCameraService.BackgroundCameraBinder binder = 
                    (BackgroundCameraService.BackgroundCameraBinder) service;
                backgroundCameraService = binder.getService();
                backgroundCameraService.setCallback(SmartGreetingServiceManager.this);
                isCameraServiceBound = true;
            }
            
            @Override
            public void onServiceDisconnected(ComponentName name) {
                System.out.println(TAG + ": Background camera service disconnected");
                backgroundCameraService = null;
                isCameraServiceBound = false;
            }
        };
    }
    
    /**
     * Start background camera service
     */
    private void startBackgroundCameraService() {
        if (context == null) {
            System.out.println(TAG + ": Context is null, cannot start camera service");
            return;
        }
        
        try {
            Intent serviceIntent = new Intent(context, BackgroundCameraService.class);
            serviceIntent.putExtra("action", "START_FACE_DETECTION");
            
            // Start and bind service
            context.startService(serviceIntent);
            context.bindService(serviceIntent, cameraServiceConnection, Context.BIND_AUTO_CREATE);
            
            System.out.println(TAG + ": Background camera service start initiated");
        } catch (Exception e) {
            System.out.println(TAG + ": Error starting background camera service: " + e.getMessage());
        }
    }
    
    /**
     * Stop background camera service
     */
    private void stopBackgroundCameraService() {
        try {
            if (backgroundCameraService != null) {
                backgroundCameraService.stopBackgroundFaceDetection();
            }
            
            if (isCameraServiceBound && context != null) {
                context.unbindService(cameraServiceConnection);
                isCameraServiceBound = false;
            }
            
            System.out.println(TAG + ": Background camera service stopped");
        } catch (Exception e) {
            System.out.println(TAG + ": Error stopping background camera service: " + e.getMessage());
        }
    }
    
    /**
     * Start distance monitoring
     */
    private void startDistanceMonitoring() {
        if (communicationManager != null) {
            communicationManager.setDistanceUpdateCallback(this);
            communicationManager.startContinuousDistanceMonitoring();
            System.out.println(TAG + ": Distance monitoring started");
        }
    }
    
    /**
     * Stop distance monitoring
     */
    private void stopDistanceMonitoring() {
        if (communicationManager != null) {
            communicationManager.stopContinuousDistanceMonitoring();
            communicationManager.setDistanceUpdateCallback(null);
            System.out.println(TAG + ": Distance monitoring stopped");
        }
    }
    
    // BackgroundCameraService.FaceDetectionCallback implementation
    @Override
    public void onFaceCountUpdated(int faceCount) {
        currentFaceCount = faceCount;

        // Notify callback
        if (callback != null) {
            callback.onFaceCountUpdated(faceCount);
        }

        // ENHANCED: Use the actual SmartGreetingManager for greeting logic
        try {
            com.stemrobo.humanoid.behaviors.SmartGreetingManager greetingManager =
                com.stemrobo.humanoid.behaviors.SmartGreetingManager.getInstance();

            if (greetingManager != null && faceCount > 0) {
                // The SmartGreetingManager has the proper face detection and greeting logic
                // We'll let it handle the greeting decision based on its internal logic
                System.out.println(TAG + ": " + faceCount + " faces detected, SmartGreetingManager will handle greeting logic");
            }
        } catch (Exception e) {
            System.out.println(TAG + ": Error notifying SmartGreetingManager: " + e.getMessage());
            // Fallback to service manager's own logic
            checkGreetingTrigger();
        }

        System.out.println(TAG + ": Face count updated: " + faceCount);
    }
    
    @Override
    public void onFaceDetectionStatusChanged(boolean isActive) {
        System.out.println(TAG + ": Face detection status changed: " + isActive);
    }
    
    @Override
    public void onServiceStatusChanged(boolean isRunning) {
        System.out.println(TAG + ": Camera service status changed: " + isRunning);
    }
    
    // ESP32CommunicationManager.DistanceUpdateCallback implementation
    @Override
    public void onDistanceUpdated(float distance) {
        currentDistance = distance;
        
        // Notify callback
        if (callback != null) {
            callback.onDistanceUpdated(distance);
        }
        
        // Check if greeting should be triggered
        checkGreetingTrigger();
        
        System.out.println(TAG + ": Distance updated: " + distance + "cm");
    }
    
    @Override
    public void onDistanceStreamingStatusChanged(boolean isActive) {
        System.out.println(TAG + ": Distance streaming status changed: " + isActive);
    }
    
    /**
     * Check if Smart Greeting should be triggered
     */
    private void checkGreetingTrigger() {
        if (!isSmartGreetingEnabled || greetingInProgress) {
            return;
        }
        
        // Check cooldown period
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastGreetingTime < GREETING_COOLDOWN) {
            return;
        }
        
        // Check greeting conditions: faces detected AND within handshake range
        if (currentFaceCount > 0 && currentDistance <= GREETING_DISTANCE_THRESHOLD) {
            triggerSmartGreeting();
        }
    }
    
    /**
     * Trigger Smart Greeting
     */
    private void triggerSmartGreeting() {
        greetingInProgress = true;
        lastGreetingTime = System.currentTimeMillis();
        
        String greetingType = "Handshake + Hi";
        
        System.out.println(TAG + ": Smart Greeting triggered! Type: " + greetingType + 
                          " (Faces: " + currentFaceCount + ", Distance: " + currentDistance + "cm)");
        
        // Send enhanced greeting commands to ESP32
        if (communicationManager != null) {
            // Send smooth greeting start command (move arm to 50° smoothly)
            communicationManager.sendSensorCommand("GREETING_START", null);

            // Speak greeting using TTS
            communicationManager.sendHandshakeGreeting();

            System.out.println(TAG + ": Enhanced greeting commands sent to ESP32");
        }
        
        // Notify callback
        if (callback != null) {
            callback.onGreetingTriggered(greetingType);
        }
        
        // Reset greeting flag and send end command after delay
        new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(() -> {
            // Send greeting end command (move arm back to 90° rest position)
            if (communicationManager != null) {
                communicationManager.sendSensorCommand("GREETING_END", null);
                System.out.println(TAG + ": Enhanced greeting end command sent to ESP32");
            }

            greetingInProgress = false;
            System.out.println(TAG + ": Smart Greeting completed");
        }, GREETING_RETURN_DELAY); // Configurable greeting duration
    }
    
    /**
     * Set callback for Smart Greeting updates
     */
    public void setCallback(SmartGreetingCallback callback) {
        this.callback = callback;
    }
    
    /**
     * Get current Smart Greeting status
     */
    public boolean isSmartGreetingActive() {
        return isMonitoringActive;
    }
    
    /**
     * Get current face count
     */
    public int getCurrentFaceCount() {
        return currentFaceCount;
    }
    
    /**
     * Get current distance
     */
    public float getCurrentDistance() {
        return currentDistance;
    }
    
    /**
     * Get service status summary
     */
    public String getStatusSummary() {
        return String.format("Smart Greeting: %s, Faces: %d, Distance: %.1fcm, Camera Service: %s",
                           isMonitoringActive ? "Active" : "Inactive",
                           currentFaceCount,
                           currentDistance,
                           isCameraServiceBound ? "Connected" : "Disconnected");
    }
    
    /**
     * Cleanup resources
     */
    public void cleanup() {
        stopSmartGreeting();
        
        if (isCameraServiceBound && context != null) {
            try {
                context.unbindService(cameraServiceConnection);
            } catch (Exception e) {
                System.out.println(TAG + ": Error during cleanup: " + e.getMessage());
            }
        }
        
        System.out.println(TAG + ": Smart Greeting Service Manager cleaned up");
    }
}
