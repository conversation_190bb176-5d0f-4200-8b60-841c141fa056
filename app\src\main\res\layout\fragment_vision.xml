<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/content_background"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Camera Preview Section -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardBackgroundColor="@color/control_button_background"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/camera_preview"
                    android:textColor="@color/text_primary"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="16dp" />

                <!-- Real Camera Preview with Detection Overlays -->
                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="240dp"
                    android:layout_marginBottom="8dp">

                    <androidx.camera.view.PreviewView
                        android:id="@+id/camera_preview"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent" />

                    <com.stemrobo.humanoid.vision.FaceBoxOverlay
                        android:id="@+id/face_box_overlay"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent" />

                    <com.stemrobo.humanoid.vision.ObjectDetectionOverlay
                        android:id="@+id/object_detection_overlay"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent" />

                    <com.stemrobo.humanoid.vision.yolo.YoloDetectionOverlay
                        android:id="@+id/yolo_detection_overlay"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent" />

                </FrameLayout>

                <!-- Camera Controls -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginBottom="8dp">

                    <!-- First row of buttons -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center"
                        android:layout_marginBottom="8dp">

                        <Button
                            android:id="@+id/switch_camera_button"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Switch Camera"
                            android:backgroundTint="@color/robot_accent"
                            android:layout_marginEnd="4dp" />

                        <Button
                            android:id="@+id/register_person_button"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Register Person"
                            android:backgroundTint="@color/robot_primary"
                            android:layout_marginStart="4dp" />

                    </LinearLayout>

                    <!-- Second row - Clear Database button -->
                    <Button
                        android:id="@+id/clear_database_button"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Clear Face Database"
                        android:backgroundTint="@color/status_disconnected"
                        android:textColor="@android:color/white" />

                </LinearLayout>

                <!-- Detection Results -->
                <TextView
                    android:id="@+id/detection_results"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="No detection results"
                    android:textColor="@color/text_hint"
                    android:textSize="12sp"
                    android:gravity="center"
                    android:padding="8dp"
                    android:background="@drawable/status_background" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Detection Controls Section -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardBackgroundColor="@color/control_button_background"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Detection Controls"
                    android:textColor="@color/text_primary"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="16dp" />

                <!-- Person Detection -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/person_detection"
                        android:textColor="@color/text_secondary" />

                    <Switch
                        android:id="@+id/person_detection_switch"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />

                </LinearLayout>

                <!-- Object Detection -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/object_detection"
                        android:textColor="@color/text_secondary" />

                    <Switch
                        android:id="@+id/object_detection_switch"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />

                </LinearLayout>

                <!-- Gesture Recognition -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="16dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/gesture_recognition"
                        android:textColor="@color/text_secondary" />

                    <Switch
                        android:id="@+id/gesture_recognition_switch"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />

                </LinearLayout>

                <!-- Detection Status -->
                <TextView
                    android:id="@+id/detection_status"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Detection Inactive"
                    android:textColor="@color/status_disconnected"
                    android:textSize="14sp"
                    android:gravity="center"
                    android:padding="8dp"
                    android:background="@drawable/status_background" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Action Controls Section -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardBackgroundColor="@color/control_button_background"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Actions"
                    android:textColor="@color/text_primary"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="16dp" />

                <!-- Follow Person Button -->
                <Button
                    android:id="@+id/follow_person_button"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/follow_person"
                    android:backgroundTint="@color/robot_accent"
                    android:layout_marginBottom="8dp" />

                <!-- Greeting Button -->
                <Button
                    android:id="@+id/greeting_button"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Greet Person"
                    android:backgroundTint="@color/robot_primary"
                    android:layout_marginBottom="8dp" />

                <!-- Object Detection Activity Button -->
                <Button
                    android:id="@+id/object_detection_activity_button"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Full Screen Object Detection"
                    android:backgroundTint="@color/robot_accent"
                    android:layout_marginBottom="8dp" />

                <!-- Stop All Actions Button -->
                <Button
                    android:id="@+id/stop_actions_button"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Stop All Actions"
                    android:backgroundTint="@color/status_disconnected" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

    </LinearLayout>

</ScrollView>
