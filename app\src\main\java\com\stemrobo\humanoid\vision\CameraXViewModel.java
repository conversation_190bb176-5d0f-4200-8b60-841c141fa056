package com.stemrobo.humanoid.vision;

import android.app.Application;
import androidx.annotation.NonNull;
import androidx.camera.lifecycle.ProcessCameraProvider;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import com.google.common.util.concurrent.ListenableFuture;
import java.util.concurrent.ExecutionException;

/**
 * ViewModel for managing CameraX ProcessCameraProvider
 * Based on working ML Kit vision reference implementation
 */
public class CameraXViewModel extends AndroidViewModel {
    
    private static final String TAG = "CameraXViewModel";
    
    private final MutableLiveData<ProcessCameraProvider> cameraProviderLiveData = new MutableLiveData<>();
    
    public CameraXViewModel(@NonNull Application application) {
        super(application);
    }
    
    /**
     * Get ProcessCameraProvider as LiveData
     * This follows the working pattern from the reference implementation
     */
    public LiveData<ProcessCameraProvider> getProcessCameraProvider() {
        if (cameraProviderLiveData.getValue() == null) {
            ListenableFuture<ProcessCameraProvider> cameraProviderFuture = 
                ProcessCameraProvider.getInstance(getApplication());
            
            cameraProviderFuture.addListener(() -> {
                try {
                    ProcessCameraProvider cameraProvider = cameraProviderFuture.get();
                    cameraProviderLiveData.setValue(cameraProvider);
                    android.util.Log.d(TAG, "CameraProvider initialized successfully");
                } catch (ExecutionException e) {
                    android.util.Log.e(TAG, "ExecutionException getting camera provider", e);
                } catch (InterruptedException e) {
                    android.util.Log.e(TAG, "InterruptedException getting camera provider", e);
                }
            }, ContextCompat.getMainExecutor(getApplication()));
        }
        
        return cameraProviderLiveData;
    }
    
    /**
     * Check if camera provider is available
     */
    public boolean isCameraProviderAvailable() {
        return cameraProviderLiveData.getValue() != null;
    }
    
    /**
     * Get current camera provider (synchronous)
     */
    public ProcessCameraProvider getCurrentCameraProvider() {
        return cameraProviderLiveData.getValue();
    }
}
