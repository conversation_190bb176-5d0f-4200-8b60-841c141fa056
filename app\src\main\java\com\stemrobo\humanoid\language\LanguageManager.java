package com.stemrobo.humanoid.language;

import android.content.Context;
import android.content.SharedPreferences;
import android.preference.PreferenceManager;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * Manages multilingual support for the STEM Robot
 * Supports English, Malayalam, Hindi, and Arabic
 */
public class LanguageManager {
    private static final String TAG = "LanguageManager";
    private static final String PREF_SELECTED_LANGUAGE = "selected_language";
    
    // Supported Languages
    public static final String LANG_ENGLISH = "en";
    public static final String LANG_MALAYALAM = "ml";
    public static final String LANG_HINDI = "hi";
    public static final String LANG_ARABIC = "ar";
    
    // Language Display Names
    private static final Map<String, String> LANGUAGE_NAMES = new HashMap<>();
    static {
        LANGUAGE_NAMES.put(LANG_ENGLISH, "English");
        LANGUAGE_NAMES.put(LANG_MALAYALAM, "മലയാളം");
        LANGUAGE_NAMES.put(<PERSON><PERSON>_<PERSON>INDI, "हिन्दी");
        LANGUAGE_NAMES.put(LANG_ARABIC, "العربية");
    }
    
    // Speech Recognition Locale Mapping
    private static final Map<String, Locale> SPEECH_LOCALES = new HashMap<>();
    static {
        SPEECH_LOCALES.put(LANG_ENGLISH, Locale.ENGLISH);
        SPEECH_LOCALES.put(LANG_MALAYALAM, new Locale("ml", "IN"));
        SPEECH_LOCALES.put(LANG_HINDI, new Locale("hi", "IN"));
        SPEECH_LOCALES.put(LANG_ARABIC, new Locale("ar", "SA"));
    }
    
    // Multi-language Wake Words
    private static final Map<String, String[]> WAKE_WORDS = new HashMap<>();
    static {
        // English Wake Words
        WAKE_WORDS.put(LANG_ENGLISH, new String[]{
            "hi stem", "hello stem", "hey stem", "hello guruji", "hi guruji","hello",
            "hey guruji", "wake up", "activate"
        });

        // Malayalam Wake Words
        WAKE_WORDS.put(LANG_MALAYALAM, new String[]{
            "ഹായ് സ്റ്റെം", "ഹലോ സ്റ്റെം", "ഹേ സ്റ്റെം", "ഹലോ ഗുരുജി", "ഹായ് ഗുരുജി",
            "ഹേ ഗുരുജി", "എഴുന്നേൽക്കുക", "സജീവമാക്കുക"
        });

        // Hindi Wake Words
        WAKE_WORDS.put(LANG_HINDI, new String[]{
            "हाय स्टेम", "हैलो स्टेम", "हे स्टेम", "हैलो गुरुजी", "हाय गुरुजी",
            "हे गुरुजी", "जागो", "सक्रिय करो"
        });

        // Arabic Wake Words
        WAKE_WORDS.put(LANG_ARABIC, new String[]{
            "مرحبا ستيم", "أهلا ستيم", "هاي ستيم", "مرحبا جوروجي", "أهلا جوروجي",
            "هاي جوروجي", "استيقظ", "تفعيل"
        });
    }

    // LMS Commands in Different Languages
    private static final Map<String, String[]> LMS_COMMANDS = new HashMap<>();
    static {
        // English LMS Commands
        LMS_COMMANDS.put(LANG_ENGLISH, new String[]{
            // LMS Introduction
            "introduce lms", "lms introduction", "lms intro", "show lms", "start lms", "lms induction",
            // Class-specific patterns (will be checked with numbers)
            "lms", "class lms", "grade lms", "standard lms", "level lms"
        });

        // Malayalam LMS Commands
        LMS_COMMANDS.put(LANG_MALAYALAM, new String[]{
            // LMS Introduction
            "എൽ എം എസ് പരിചയപ്പെടുത്തുക", "എൽ എം എസ്", "എൽ എം എസ് കാണിക്കുക", "എൽഎംഎസ് ആരംഭിക്കുക",
            "എൽഎംഎസ് പരിചയം", "എൽ എം എസ് ഇൻട്രോ", "എൽഎംഎസ് വിവരണം",
            // Class-specific patterns
            "എൽ എം എസ്", "ക്ലാസ് എൽ എം എസ്", "ഗ്രേഡ് എൽഎംഎസ്", "സ്റ്റാൻഡേർഡ് എൽഎംഎസ്"
        });

        // Hindi LMS Commands
        LMS_COMMANDS.put(LANG_HINDI, new String[]{
            // LMS Introduction
            "एलएमएस का परिचय", "एलएमएस परिचय", "एलएमएस दिखाओ", "एलएमएस शुरू करो",
            "एलएमएस का विवरण", "एलएमएस इंट्रो", "एलएमएस प्रस्तुति",
            // Class-specific patterns
            "एलएमएस", "क्लास एलएमएस", "ग्रेड एलएमएस", "कक्षा एलएमएस", "स्तर एलएमएस"
        });

        // Arabic LMS Commands
        LMS_COMMANDS.put(LANG_ARABIC, new String[]{
            // LMS Introduction
            "تعريف إل إم إس", "مقدمة إل إم إس", "أظهر إل إم إس", "ابدأ إل إم إس",
            "وصف إل إم إس", "عرض إل إم إس", "تقديم إل إم إس",
            // Class-specific patterns
            "إل إم إس", "صف إل إم إس", "درجة إل إم إس", "مستوى إل إم إس", "فصل إل إم إس"
        });
    }

    // Number Words in Different Languages (1-12)
    private static final Map<String, String[]> NUMBER_WORDS = new HashMap<>();
    static {
        // English Numbers
        NUMBER_WORDS.put(LANG_ENGLISH, new String[]{
            "one", "two", "three", "four", "five", "six", "seven", "eight", "nine", "ten", "eleven", "twelve",
            "first", "second", "third", "fourth", "fifth", "sixth", "seventh", "eighth", "ninth", "tenth", "eleventh", "twelfth",
            "1st", "2nd", "3rd", "4th", "5th", "6th", "7th", "8th", "9th", "10th", "11th", "12th"
        });

        // Malayalam Numbers
        NUMBER_WORDS.put(LANG_MALAYALAM, new String[]{
            "ഒന്ന്", "രണ്ട്", "മൂന്ന്", "നാല്", "അഞ്ച്", "ആറ്", "ഏഴ്", "എട്ട്", "ഒമ്പത്", "പത്ത്", "പതിനൊന്ന്", "പന്ത്രണ്ട്",
            "ഒന്നാം", "രണ്ടാം", "മൂന്നാം", "നാലാം", "അഞ്ചാം", "ആറാം", "ഏഴാം", "എട്ടാം", "ഒമ്പതാം", "പത്താം", "പതിനൊന്നാം", "പന്ത്രണ്ടാം",
            "ഒന്നാമത്തെ", "രണ്ടാമത്തെ", "മൂന്നാമത്തെ", "നാലാമത്തെ", "അഞ്ചാമത്തെ", "ആറാമത്തെ", "ഏഴാമത്തെ", "എട്ടാമത്തെ", "ഒമ്പതാമത്തെ", "പത്താമത്തെ", "പതിനൊന്നാമത്തെ", "പന്ത്രണ്ടാമത്തെ"
        });

        // Hindi Numbers
        NUMBER_WORDS.put(LANG_HINDI, new String[]{
            "एक", "दो", "तीन", "चार", "पांच", "छह", "सात", "आठ", "नौ", "दस", "ग्यारह", "बारह",
            "पहला", "दूसरा", "तीसरा", "चौथा", "पांचवा", "छठा", "सातवां", "आठवां", "नौवां", "दसवां", "ग्यारहवां", "बारहवां",
            "पहली", "दूसरी", "तीसरी", "चौथी", "पांचवी", "छठी", "सातवीं", "आठवीं", "नौवीं", "दसवीं", "ग्यारहवीं", "बारहवीं"
        });

        // Arabic Numbers
        NUMBER_WORDS.put(LANG_ARABIC, new String[]{
            "واحد", "اثنان", "ثلاثة", "أربعة", "خمسة", "ستة", "سبعة", "ثمانية", "تسعة", "عشرة", "أحد عشر", "اثنا عشر",
            "الأول", "الثاني", "الثالث", "الرابع", "الخامس", "السادس", "السابع", "الثامن", "التاسع", "العاشر", "الحادي عشر", "الثاني عشر",
            "أولى", "ثانية", "ثالثة", "رابعة", "خامسة", "سادسة", "سابعة", "ثامنة", "تاسعة", "عاشرة", "حادية عشرة", "ثانية عشرة"
        });
    }

    // Comprehensive Robot Commands in Different Languages
    private static final Map<String, String[]> ROBOT_COMMANDS = new HashMap<>();
    static {
        // English Commands - Multiple variations for each action
        ROBOT_COMMANDS.put(LANG_ENGLISH, new String[]{
            // Forward movement
            "move forward", "go forward", "move front", "go front", "forward", "front", "go", "move", "advance",
            // Backward movement
            "move backward", "go backward", "move back", "go back", "backward", "back", "reverse", "retreat",
            // Left turn
            "turn left", "go left", "left", "rotate left", "spin left",
            // Right turn
            "turn right", "go right", "right", "rotate right", "spin right",
            // Stop
            "stop", "halt", "pause", "freeze", "brake",
            // Wave
            "wave", "wave hand", "say hello", "greet", "wave hello",
            // Point
            "point", "point finger", "indicate", "show direction",
            // Rest
            "rest", "rest position", "relax", "default position", "home position",
            // Head center
            "center head", "head center", "look center", "head straight", "center",
            // Look up
            "look up", "head up", "up", "look above", "tilt up",
            // Look down
            "look down", "head down", "down", "look below", "tilt down"
        });

        // Malayalam Commands - Multiple variations
        ROBOT_COMMANDS.put(LANG_MALAYALAM, new String[]{
            // Forward movement
            "മുന്നോട്ട് പോകുക", "മുന്നിലേക്ക് പോകുക", "മുന്നോട്ട്", "മുന്നിലേക്ക്", "പോകുക", "നീങ്ങുക",
            // Backward movement
            "പിന്നോട്ട് പോകുക", "പിന്നിലേക്ക് പോകുക", "പിന്നോട്ട്", "പിന്നിലേക്ക്", "തിരിച്ച് പോകുക",
            // Left turn
            "ഇടത്തോട്ട് തിരിയുക", "ഇടത്തേക്ക് പോകുക", "ഇടത്തോട്ട്", "ഇടത്തേക്ക്", "ഇടത്ത്",
            // Right turn
            "വലത്തോട്ട് തിരിയുക", "വലത്തേക്ക് പോകുക", "വലത്തോട്ട്", "വലത്തേക്ക്", "വലത്ത്",
            // Stop
            "നിര്‍ത്തുക", "നിര്‍ത്തുക", "നിൽക്കുക", "നിര്‍ത്തുക",
            // Wave
            "കൈ വീശുക", "ഹലോ പറയുക", "അഭിവാദനം", "കൈ കാണിക്കുക",
            // Point
            "ചൂണ്ടുക", "വിരൽ ചൂണ്ടുക", "കാണിക്കുക", "ദിശ കാണിക്കുക",
            // Rest
            "വിശ്രമിക്കുക", "വിശ്രമ സ്ഥാനം", "സാധാരണ സ്ഥാനം", "ആരാമിക്കുക",
            // Head center
            "തല മധ്യത്തിൽ", "തല നേരെ", "മധ്യത്തിൽ നോക്കുക", "നേരെ നോക്കുക",
            // Look up
            "മുകളിലേക്ക് നോക്കുക", "തല മുകളിലേക്ക്", "മുകളിൽ", "മുകളിലേക്ക്",
            // Look down
            "താഴേക്ക് നോക്കുക", "തല താഴേക്ക്", "താഴേ", "താഴേക്ക്"
        });

        // Hindi Commands - Multiple variations
        ROBOT_COMMANDS.put(LANG_HINDI, new String[]{
            // Forward movement
            "आगे बढ़ो", "आगे जाओ", "सामने जाओ", "आगे", "सामने", "जाओ", "चलो", "आगे चलो",
            // Backward movement
            "पीछे जाओ", "पीछे चलो", "पीछे", "वापस जाओ", "पीछे हटो",
            // Left turn
            "बाएं मुड़ो", "बाएं जाओ", "बाएं", "बाएं तरफ", "बाएं घूमो",
            // Right turn
            "दाएं मुड़ो", "दाएं जाओ", "दाएं", "दाएं तरफ", "दाएं घूमो",
            // Stop
            "रुको", "रुक जाओ", "ठहरो", "बंद करो", "स्टॉप",
            // Wave
            "हाथ हिलाओ", "नमस्ते करो", "अभिवादन करो", "हैलो कहो",
            // Point
            "इशारा करो", "उंगली से दिखाओ", "दिशा दिखाओ", "पॉइंट करो",
            // Rest
            "आराम करो", "आराम की स्थिति", "सामान्य स्थिति", "रिलैक्स करो",
            // Head center
            "सिर बीच में", "सिर सीधा", "बीच में देखो", "सीधा देखो",
            // Look up
            "ऊपर देखो", "सिर ऊपर", "ऊपर", "ऊपर की तरफ",
            // Look down
            "नीचे देखो", "सिर नीचे", "नीचे", "नीचे की तरफ"
        });

        // Arabic Commands - Multiple variations
        ROBOT_COMMANDS.put(LANG_ARABIC, new String[]{
            // Forward movement
            "تحرك للأمام", "اذهب للأمام", "إلى الأمام", "أمام", "تقدم", "امشي", "تحرك",
            // Backward movement
            "تحرك للخلف", "اذهب للخلف", "إلى الخلف", "خلف", "ارجع", "تراجع",
            // Left turn
            "انعطف يسارا", "اذهب يسارا", "يسار", "إلى اليسار", "استدر يسارا",
            // Right turn
            "انعطف يمينا", "اذهب يمينا", "يمين", "إلى اليمين", "استدر يمينا",
            // Stop
            "توقف", "قف", "اتوقف", "توقف الآن", "قف الآن",
            // Wave
            "لوح بيدك", "قل مرحبا", "حيي", "لوح",
            // Point
            "أشر", "أشر بإصبعك", "اشر إلى", "أظهر الاتجاه",
            // Rest
            "استرح", "وضعية الراحة", "الوضع الطبيعي", "ارتاح",
            // Head center
            "وسط الرأس", "الرأس في المنتصف", "انظر للوسط", "مستقيم",
            // Look up
            "انظر لأعلى", "الرأس لأعلى", "أعلى", "إلى أعلى",
            // Look down
            "انظر لأسفل", "الرأس لأسفل", "أسفل", "إلى أسفل"
        });
    }
    
    // Comprehensive Command Translation to English (for ESP32)
    private static final Map<String, String> COMMAND_TRANSLATIONS = new HashMap<>();
    static {
        // Malayalam to English - Forward movement
        COMMAND_TRANSLATIONS.put("മുന്നോട്ട് പോകുക", "move forward");
        COMMAND_TRANSLATIONS.put("മുന്നിലേക്ക് പോകുക", "move forward");
        COMMAND_TRANSLATIONS.put("മുന്നോട്ട്", "move forward");
        COMMAND_TRANSLATIONS.put("മുന്നിലേക്ക്", "move forward");
        COMMAND_TRANSLATIONS.put("പോകുക", "move forward");
        COMMAND_TRANSLATIONS.put("നീങ്ങുക", "move forward");

        // Malayalam to English - Backward movement
        COMMAND_TRANSLATIONS.put("പിന്നോട്ട് പോകുക", "move backward");
        COMMAND_TRANSLATIONS.put("പിന്നിലേക്ക് പോകുക", "move backward");
        COMMAND_TRANSLATIONS.put("പിന്നോട്ട്", "move backward");
        COMMAND_TRANSLATIONS.put("പിന്നിലേക്ക്", "move backward");
        COMMAND_TRANSLATIONS.put("തിരിച്ച് പോകുക", "move backward");

        // Malayalam to English - Left turn
        COMMAND_TRANSLATIONS.put("ഇടത്തോട്ട് തിരിയുക", "turn left");
        COMMAND_TRANSLATIONS.put("ഇടത്തേക്ക് പോകുക", "turn left");
        COMMAND_TRANSLATIONS.put("ഇടത്തോട്ട്", "turn left");
        COMMAND_TRANSLATIONS.put("ഇടത്തേക്ക്", "turn left");
        COMMAND_TRANSLATIONS.put("ഇടത്ത്", "turn left");

        // Malayalam to English - Right turn
        COMMAND_TRANSLATIONS.put("വലത്തോട്ട് തിരിയുക", "turn right");
        COMMAND_TRANSLATIONS.put("വലത്തേക്ക് പോകുക", "turn right");
        COMMAND_TRANSLATIONS.put("വലത്തോട്ട്", "turn right");
        COMMAND_TRANSLATIONS.put("വലത്തേക്ക്", "turn right");
        COMMAND_TRANSLATIONS.put("വലത്ത്", "turn right");

        // Malayalam to English - Stop
        COMMAND_TRANSLATIONS.put("നിര്‍ത്തുക", "stop");
        COMMAND_TRANSLATIONS.put("നിൽക്കുക", "stop");

        // Malayalam to English - Wave
        COMMAND_TRANSLATIONS.put("കൈ വീശുക", "wave");
        COMMAND_TRANSLATIONS.put("ഹലോ പറയുക", "wave");
        COMMAND_TRANSLATIONS.put("അഭിവാദനം", "wave");
        COMMAND_TRANSLATIONS.put("കൈ കാണിക്കുക", "wave");

        // Malayalam to English - Point
        COMMAND_TRANSLATIONS.put("ചൂണ്ടുക", "point");
        COMMAND_TRANSLATIONS.put("വിരൽ ചൂണ്ടുക", "point");
        COMMAND_TRANSLATIONS.put("കാണിക്കുക", "point");
        COMMAND_TRANSLATIONS.put("ദിശ കാണിക്കുക", "point");

        // Malayalam to English - Rest
        COMMAND_TRANSLATIONS.put("വിശ്രമിക്കുക", "rest");
        COMMAND_TRANSLATIONS.put("വിശ്രമ സ്ഥാനം", "rest");
        COMMAND_TRANSLATIONS.put("സാധാരണ സ്ഥാനം", "rest");
        COMMAND_TRANSLATIONS.put("ആരാമിക്കുക", "rest");

        // Malayalam to English - Head commands
        COMMAND_TRANSLATIONS.put("തല മധ്യത്തിൽ", "center head");
        COMMAND_TRANSLATIONS.put("തല നേരെ", "center head");
        COMMAND_TRANSLATIONS.put("മധ്യത്തിൽ നോക്കുക", "center head");
        COMMAND_TRANSLATIONS.put("നേരെ നോക്കുക", "center head");
        COMMAND_TRANSLATIONS.put("മുകളിലേക്ക് നോക്കുക", "look up");
        COMMAND_TRANSLATIONS.put("തല മുകളിലേക്ക്", "look up");
        COMMAND_TRANSLATIONS.put("മുകളിൽ", "look up");
        COMMAND_TRANSLATIONS.put("മുകളിലേക്ക്", "look up");
        COMMAND_TRANSLATIONS.put("താഴേക്ക് നോക്കുക", "look down");
        COMMAND_TRANSLATIONS.put("തല താഴേക്ക്", "look down");
        COMMAND_TRANSLATIONS.put("താഴേ", "look down");
        COMMAND_TRANSLATIONS.put("താഴേക്ക്", "look down");

        // Hindi to English - Forward movement
        COMMAND_TRANSLATIONS.put("आगे बढ़ो", "move forward");
        COMMAND_TRANSLATIONS.put("आगे जाओ", "move forward");
        COMMAND_TRANSLATIONS.put("सामने जाओ", "move forward");
        COMMAND_TRANSLATIONS.put("आगे", "move forward");
        COMMAND_TRANSLATIONS.put("सामने", "move forward");
        COMMAND_TRANSLATIONS.put("जाओ", "move forward");
        COMMAND_TRANSLATIONS.put("चलो", "move forward");
        COMMAND_TRANSLATIONS.put("आगे चलो", "move forward");

        // Hindi to English - Backward movement
        COMMAND_TRANSLATIONS.put("पीछे जाओ", "move backward");
        COMMAND_TRANSLATIONS.put("पीछे चलो", "move backward");
        COMMAND_TRANSLATIONS.put("पीछे", "move backward");
        COMMAND_TRANSLATIONS.put("वापस जाओ", "move backward");
        COMMAND_TRANSLATIONS.put("पीछे हटो", "move backward");

        // Hindi to English - Left turn
        COMMAND_TRANSLATIONS.put("बाएं मुड़ो", "turn left");
        COMMAND_TRANSLATIONS.put("बाएं जाओ", "turn left");
        COMMAND_TRANSLATIONS.put("बाएं", "turn left");
        COMMAND_TRANSLATIONS.put("बाएं तरफ", "turn left");
        COMMAND_TRANSLATIONS.put("बाएं घूमो", "turn left");

        // Hindi to English - Right turn
        COMMAND_TRANSLATIONS.put("दाएं मुड़ो", "turn right");
        COMMAND_TRANSLATIONS.put("दाएं जाओ", "turn right");
        COMMAND_TRANSLATIONS.put("दाएं", "turn right");
        COMMAND_TRANSLATIONS.put("दाएं तरफ", "turn right");
        COMMAND_TRANSLATIONS.put("दाएं घूमो", "turn right");

        // Hindi to English - Stop
        COMMAND_TRANSLATIONS.put("रुको", "stop");
        COMMAND_TRANSLATIONS.put("रुक जाओ", "stop");
        COMMAND_TRANSLATIONS.put("ठहरो", "stop");
        COMMAND_TRANSLATIONS.put("बंद करो", "stop");
        COMMAND_TRANSLATIONS.put("स्टॉप", "stop");

        // Hindi to English - Wave
        COMMAND_TRANSLATIONS.put("हाथ हिलाओ", "wave");
        COMMAND_TRANSLATIONS.put("नमस्ते करो", "wave");
        COMMAND_TRANSLATIONS.put("अभिवादन करो", "wave");
        COMMAND_TRANSLATIONS.put("हैलो कहो", "wave");

        // Hindi to English - Point
        COMMAND_TRANSLATIONS.put("इशारा करो", "point");
        COMMAND_TRANSLATIONS.put("उंगली से दिखाओ", "point");
        COMMAND_TRANSLATIONS.put("दिशा दिखाओ", "point");
        COMMAND_TRANSLATIONS.put("पॉइंट करो", "point");

        // Hindi to English - Rest
        COMMAND_TRANSLATIONS.put("आराम करो", "rest");
        COMMAND_TRANSLATIONS.put("आराम की स्थिति", "rest");
        COMMAND_TRANSLATIONS.put("सामान्य स्थिति", "rest");
        COMMAND_TRANSLATIONS.put("रिलैक्स करो", "rest");

        // Hindi to English - Head commands
        COMMAND_TRANSLATIONS.put("सिर बीच में", "center head");
        COMMAND_TRANSLATIONS.put("सिर सीधा", "center head");
        COMMAND_TRANSLATIONS.put("बीच में देखो", "center head");
        COMMAND_TRANSLATIONS.put("सीधा देखो", "center head");
        COMMAND_TRANSLATIONS.put("ऊपर देखो", "look up");
        COMMAND_TRANSLATIONS.put("सिर ऊपर", "look up");
        COMMAND_TRANSLATIONS.put("ऊपर", "look up");
        COMMAND_TRANSLATIONS.put("ऊपर की तरफ", "look up");
        COMMAND_TRANSLATIONS.put("नीचे देखो", "look down");
        COMMAND_TRANSLATIONS.put("सिर नीचे", "look down");
        COMMAND_TRANSLATIONS.put("नीचे", "look down");
        COMMAND_TRANSLATIONS.put("नीचे की तरफ", "look down");

        // Arabic to English - Forward movement
        COMMAND_TRANSLATIONS.put("تحرك للأمام", "move forward");
        COMMAND_TRANSLATIONS.put("اذهب للأمام", "move forward");
        COMMAND_TRANSLATIONS.put("إلى الأمام", "move forward");
        COMMAND_TRANSLATIONS.put("أمام", "move forward");
        COMMAND_TRANSLATIONS.put("تقدم", "move forward");
        COMMAND_TRANSLATIONS.put("امشي", "move forward");
        COMMAND_TRANSLATIONS.put("تحرك", "move forward");

        // Arabic to English - Backward movement
        COMMAND_TRANSLATIONS.put("تحرك للخلف", "move backward");
        COMMAND_TRANSLATIONS.put("اذهب للخلف", "move backward");
        COMMAND_TRANSLATIONS.put("إلى الخلف", "move backward");
        COMMAND_TRANSLATIONS.put("خلف", "move backward");
        COMMAND_TRANSLATIONS.put("ارجع", "move backward");
        COMMAND_TRANSLATIONS.put("تراجع", "move backward");

        // Arabic to English - Left turn
        COMMAND_TRANSLATIONS.put("انعطف يسارا", "turn left");
        COMMAND_TRANSLATIONS.put("اذهب يسارا", "turn left");
        COMMAND_TRANSLATIONS.put("يسار", "turn left");
        COMMAND_TRANSLATIONS.put("إلى اليسار", "turn left");
        COMMAND_TRANSLATIONS.put("استدر يسارا", "turn left");

        // Arabic to English - Right turn
        COMMAND_TRANSLATIONS.put("انعطف يمينا", "turn right");
        COMMAND_TRANSLATIONS.put("اذهب يمينا", "turn right");
        COMMAND_TRANSLATIONS.put("يمين", "turn right");
        COMMAND_TRANSLATIONS.put("إلى اليمين", "turn right");
        COMMAND_TRANSLATIONS.put("استدر يمينا", "turn right");

        // Arabic to English - Stop
        COMMAND_TRANSLATIONS.put("توقف", "stop");
        COMMAND_TRANSLATIONS.put("قف", "stop");
        COMMAND_TRANSLATIONS.put("اتوقف", "stop");
        COMMAND_TRANSLATIONS.put("توقف الآن", "stop");
        COMMAND_TRANSLATIONS.put("قف الآن", "stop");

        // Arabic to English - Wave
        COMMAND_TRANSLATIONS.put("لوح بيدك", "wave");
        COMMAND_TRANSLATIONS.put("قل مرحبا", "wave");
        COMMAND_TRANSLATIONS.put("حيي", "wave");
        COMMAND_TRANSLATIONS.put("لوح", "wave");

        // Arabic to English - Point
        COMMAND_TRANSLATIONS.put("أشر", "point");
        COMMAND_TRANSLATIONS.put("أشر بإصبعك", "point");
        COMMAND_TRANSLATIONS.put("اشر إلى", "point");
        COMMAND_TRANSLATIONS.put("أظهر الاتجاه", "point");

        // Arabic to English - Rest
        COMMAND_TRANSLATIONS.put("استرح", "rest");
        COMMAND_TRANSLATIONS.put("وضعية الراحة", "rest");
        COMMAND_TRANSLATIONS.put("الوضع الطبيعي", "rest");
        COMMAND_TRANSLATIONS.put("ارتاح", "rest");

        // Arabic to English - Head commands
        COMMAND_TRANSLATIONS.put("وسط الرأس", "center head");
        COMMAND_TRANSLATIONS.put("الرأس في المنتصف", "center head");
        COMMAND_TRANSLATIONS.put("انظر للوسط", "center head");
        COMMAND_TRANSLATIONS.put("مستقيم", "center head");
        COMMAND_TRANSLATIONS.put("انظر لأعلى", "look up");
        COMMAND_TRANSLATIONS.put("الرأس لأعلى", "look up");
        COMMAND_TRANSLATIONS.put("أعلى", "look up");
        COMMAND_TRANSLATIONS.put("إلى أعلى", "look up");
        COMMAND_TRANSLATIONS.put("انظر لأسفل", "look down");
        COMMAND_TRANSLATIONS.put("الرأس لأسفل", "look down");
        COMMAND_TRANSLATIONS.put("أسفل", "look down");
        COMMAND_TRANSLATIONS.put("إلى أسفل", "look down");
    }
    
    private SharedPreferences preferences;
    private String currentLanguage;
    
    public LanguageManager(Context context) {
        preferences = PreferenceManager.getDefaultSharedPreferences(context);
        currentLanguage = preferences.getString(PREF_SELECTED_LANGUAGE, LANG_ENGLISH);
    }
    
    /**
     * Get list of supported languages for spinner
     */
    public List<String> getSupportedLanguages() {
        List<String> languages = new ArrayList<>();
        languages.add(LANGUAGE_NAMES.get(LANG_ENGLISH));
        languages.add(LANGUAGE_NAMES.get(LANG_MALAYALAM));
        languages.add(LANGUAGE_NAMES.get(LANG_HINDI));
        languages.add(LANGUAGE_NAMES.get(LANG_ARABIC));
        return languages;
    }
    
    /**
     * Get language codes for supported languages
     */
    public List<String> getSupportedLanguageCodes() {
        List<String> codes = new ArrayList<>();
        codes.add(LANG_ENGLISH);
        codes.add(LANG_MALAYALAM);
        codes.add(LANG_HINDI);
        codes.add(LANG_ARABIC);
        return codes;
    }
    
    /**
     * Set current language
     */
    public void setCurrentLanguage(String languageCode) {
        currentLanguage = languageCode;
        preferences.edit().putString(PREF_SELECTED_LANGUAGE, languageCode).apply();
    }
    
    /**
     * Get current language code
     */
    public String getCurrentLanguage() {
        return currentLanguage;
    }
    
    /**
     * Get current language display name
     */
    public String getCurrentLanguageName() {
        return LANGUAGE_NAMES.get(currentLanguage);
    }
    
    /**
     * Get speech recognition locale for current language
     */
    public Locale getSpeechLocale() {
        return SPEECH_LOCALES.get(currentLanguage);
    }
    
    /**
     * Get speech recognition locale for specific language
     */
    public Locale getSpeechLocale(String languageCode) {
        return SPEECH_LOCALES.get(languageCode);
    }
    
    /**
     * Check if input contains robot command in current language (Enhanced with multi-language support)
     */
    public boolean isRobotCommand(String input) {
        String lowerInput = input.toLowerCase();

        // Check commands in current language
        String[] commands = ROBOT_COMMANDS.get(currentLanguage);
        if (commands != null) {
            for (String command : commands) {
                if (lowerInput.contains(command.toLowerCase())) {
                    return true;
                }
            }
        }

        // Also check English commands for universal understanding
        if (!currentLanguage.equals(LANG_ENGLISH)) {
            String[] englishCommands = ROBOT_COMMANDS.get(LANG_ENGLISH);
            if (englishCommands != null) {
                for (String command : englishCommands) {
                    if (lowerInput.contains(command.toLowerCase())) {
                        return true;
                    }
                }
            }
        }

        return false;
    }
    
    /**
     * Translate robot command to English for ESP32 (Enhanced with comprehensive mapping)
     */
    public String translateCommandToEnglish(String input) {
        String lowerInput = input.toLowerCase();

        // If already English, check for command variations and normalize
        if (currentLanguage.equals(LANG_ENGLISH)) {
            return normalizeEnglishCommand(input);
        }

        // Check for exact command translations first
        for (Map.Entry<String, String> entry : COMMAND_TRANSLATIONS.entrySet()) {
            if (lowerInput.contains(entry.getKey().toLowerCase())) {
                return entry.getValue();
            }
        }

        // If no exact translation found, return normalized input
        return normalizeEnglishCommand(input);
    }
    
    /**
     * Get system prompt for AI in current language
     */
    public String getSystemPrompt() {
        switch (currentLanguage) {
            case LANG_MALAYALAM:
                return "എന്റെ പേര് ഗുരുജി ആണ്, STEM-Xpert കമ്പനി സൃഷ്ടിച്ച ഒരു AI അസിസ്റ്റന്റ്. ഞാൻ റോബോട്ടിക്സ്, " +
                       "പ്രോഗ്രാമിംഗ്, വിദ്യാഭ്യാസ ഉള്ളടക്കം എന്നിവയിൽ സഹായിക്കാൻ രൂപകൽപ്പന ചെയ്തിട്ടുള്ളതാണ്. " +
                       "ഞാൻ സാധാരണ ചോദ്യങ്ങൾക്ക് ഹ്രസ്വവും വ്യക്തവുമായ ഉത്തരങ്ങൾ നൽകുന്നു, " +
                       "പ്രത്യേക ചോദ്യങ്ങൾക്ക് വിശദമായ ഉത്തരങ്ങൾ നൽകുന്നു.";
                       
            case LANG_HINDI:
                return "मेरा नाम गुरुजी है, मैं STEM-Xpert कंपनी द्वारा बनाया गया एक AI असिस्टेंट हूं। " +
                       "मैं रोबोटिक्स, प्रोग्रामिंग और शैक्षिक सामग्री में मदद करने के लिए डिज़ाइन किया गया हूं। " +
                       "मैं सामान्य प्रश्नों के लिए संक्षिप्त और स्पष्ट उत्तर देता हूं, " +
                       "विशिष्ट प्रश्नों के लिए विस्तृत उत्तर देता हूं।";
                       
            case LANG_ARABIC:
                return "اسمي جوروجي، أنا مساعد ذكي تم إنشاؤه من قبل شركة STEM-Xpert. " +
                       "تم تصميمي لمساعدتك في الروبوتات والبرمجة والمحتوى التعليمي. " +
                       "أقدم إجابات قصيرة وواضحة للأسئلة العامة، " +
                       "وإجابات مفصلة للأسئلة المحددة.";
                       
            default: // English
                return "My name is Guruji, an AI assistant created by STEM-Xpert company. I am designed to help with robotics, " +
                       "programming, and educational content. I provide responses optimized for speech synthesis without emojis, " +
                       "brackets, or special characters. I keep responses short and crisp for general questions, but provide " +
                       "detailed moderate-sized answers when asked specific questions. I represent STEM-Xpert's commitment to " +
                       "quality STEM education and robotics innovation.";
        }
    }
    
    /**
     * Get language instruction for AI
     */
    public String getLanguageInstruction() {
        switch (currentLanguage) {
            case LANG_MALAYALAM:
                return "ദയവായി മലയാളത്തിൽ മറുപടി നൽകുക.";
            case LANG_HINDI:
                return "कृपया हिंदी में उत्तर दें।";
            case LANG_ARABIC:
                return "يرجى الرد باللغة العربية.";
            default:
                return "Please respond in English.";
        }
    }
    
    /**
     * Get wake word for current language (returns first wake word for backward compatibility)
     */
    public String getWakeWord() {
        String[] wakeWords = WAKE_WORDS.get(currentLanguage);
        if (wakeWords != null && wakeWords.length > 0) {
            return wakeWords[0];
        }
        return "hey guruji"; // fallback
    }

    /**
     * Get all wake words for current language
     */
    public String[] getAllWakeWords() {
        String[] wakeWords = WAKE_WORDS.get(currentLanguage);
        return wakeWords != null ? wakeWords : new String[]{"hey robot"};
    }

    /**
     * Get all wake words for all languages (for universal detection)
     */
    public String[] getAllWakeWordsAllLanguages() {
        List<String> allWakeWords = new ArrayList<>();
        for (String[] wakeWords : WAKE_WORDS.values()) {
            for (String wakeWord : wakeWords) {
                allWakeWords.add(wakeWord.toLowerCase());
            }
        }
        return allWakeWords.toArray(new String[0]);
    }

    /**
     * Check if input contains any wake word from any language
     */
    public boolean containsWakeWord(String input) {
        String lowerInput = input.toLowerCase();
        String[] allWakeWords = getAllWakeWordsAllLanguages();

        for (String wakeWord : allWakeWords) {
            if (lowerInput.contains(wakeWord)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Get LMS commands for current language
     */
    public String[] getLMSCommands() {
        String[] commands = LMS_COMMANDS.get(currentLanguage);
        return commands != null ? commands : new String[0];
    }

    /**
     * Check if input contains LMS command in current language
     */
    public boolean isLMSCommand(String input) {
        String lowerInput = input.toLowerCase();

        // Check LMS commands in current language
        String[] lmsCommands = LMS_COMMANDS.get(currentLanguage);
        if (lmsCommands != null) {
            for (String command : lmsCommands) {
                if (lowerInput.contains(command.toLowerCase())) {
                    // For introduction commands, check directly
                    if (isLMSIntroductionCommand(command)) {
                        return true;
                    }
                    // For class commands, check if there's a number
                    if (command.toLowerCase().contains("lms") && containsClassNumber(lowerInput)) {
                        return true;
                    }
                }
            }
        }

        // Also check English LMS commands for universal understanding
        if (!currentLanguage.equals(LANG_ENGLISH)) {
            String[] englishLMSCommands = LMS_COMMANDS.get(LANG_ENGLISH);
            if (englishLMSCommands != null) {
                for (String command : englishLMSCommands) {
                    if (lowerInput.contains(command.toLowerCase())) {
                        if (isLMSIntroductionCommand(command)) {
                            return true;
                        }
                        if (command.toLowerCase().contains("lms") && containsClassNumber(lowerInput)) {
                            return true;
                        }
                    }
                }
            }
        }

        return false;
    }

    /**
     * Check if command is an LMS introduction command
     */
    private boolean isLMSIntroductionCommand(String command) {
        String lowerCommand = command.toLowerCase();
        return lowerCommand.contains("introduce") || lowerCommand.contains("introduction") ||
               lowerCommand.contains("intro") || lowerCommand.contains("परिचय") ||
               lowerCommand.contains("പരിചയ") || lowerCommand.contains("تعريف") ||
               lowerCommand.contains("مقدمة") || lowerCommand.contains("ആമുഖം");
    }

    /**
     * Check if input contains class number in current language
     */
    private boolean containsClassNumber(String input) {
        String lowerInput = input.toLowerCase();

        // Check numeric digits 1-12
        for (int i = 1; i <= 12; i++) {
            if (lowerInput.contains(String.valueOf(i))) {
                return true;
            }
        }

        // Check number words in current language
        String[] numberWords = NUMBER_WORDS.get(currentLanguage);
        if (numberWords != null) {
            for (String numberWord : numberWords) {
                if (lowerInput.contains(numberWord.toLowerCase())) {
                    return true;
                }
            }
        }

        // Also check English number words for universal understanding
        if (!currentLanguage.equals(LANG_ENGLISH)) {
            String[] englishNumbers = NUMBER_WORDS.get(LANG_ENGLISH);
            if (englishNumbers != null) {
                for (String numberWord : englishNumbers) {
                    if (lowerInput.contains(numberWord.toLowerCase())) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /**
     * Extract class number from LMS command in current language
     */
    public int extractClassNumber(String input) {
        String lowerInput = input.toLowerCase();

        // Check numeric digits 1-12 first
        for (int i = 1; i <= 12; i++) {
            if (lowerInput.contains(String.valueOf(i))) {
                return i;
            }
        }

        // Check number words in current language
        String[] numberWords = NUMBER_WORDS.get(currentLanguage);
        if (numberWords != null) {
            for (int i = 0; i < numberWords.length && i < 36; i++) { // 36 = 12 numbers * 3 formats each
                if (lowerInput.contains(numberWords[i].toLowerCase())) {
                    return (i % 12) + 1; // Convert index back to class number (1-12)
                }
            }
        }

        // Also check English number words
        if (!currentLanguage.equals(LANG_ENGLISH)) {
            String[] englishNumbers = NUMBER_WORDS.get(LANG_ENGLISH);
            if (englishNumbers != null) {
                for (int i = 0; i < englishNumbers.length && i < 36; i++) {
                    if (lowerInput.contains(englishNumbers[i].toLowerCase())) {
                        return (i % 12) + 1;
                    }
                }
            }
        }

        return 0; // Not found
    }

    /**
     * Get LMS response message in current language
     */
    public String getLMSIntroResponse() {
        switch (currentLanguage) {
            case LANG_MALAYALAM:
                return "STEM-Xpert LMS പരിചയ വീഡിയോ ആരംഭിക്കുന്നു";
            case LANG_HINDI:
                return "STEM-Xpert LMS परिचय वीडियो शुरू कर रहा हूं";
            case LANG_ARABIC:
                return "بدء فيديو تعريف STEM-Xpert LMS";
            default:
                return "Starting STEM-Xpert LMS introduction video";
        }
    }

    /**
     * Get LMS class response message in current language
     */
    public String getLMSClassResponse(int classNumber) {
        switch (currentLanguage) {
            case LANG_MALAYALAM:
                return "ക്ലാസ് " + classNumber + " LMS വീഡിയോ ആരംഭിക്കുന്നു";
            case LANG_HINDI:
                return "कक्षा " + classNumber + " LMS वीडियो शुरू कर रहा हूं";
            case LANG_ARABIC:
                return "بدء فيديو LMS للصف " + classNumber;
            default:
                return "Starting class " + classNumber + " LMS video";
        }
    }

    /**
     * Get LMS error response message in current language
     */
    public String getLMSErrorResponse() {
        switch (currentLanguage) {
            case LANG_MALAYALAM:
                return "നിങ്ങൾക്ക് ഏത് LMS ഉള്ളടക്കം വേണമെന്ന് എനിക്ക് മനസ്സിലായില്ല. ദയവായി ക്ലാസ് നമ്പർ പറയുക അല്ലെങ്കിൽ LMS പരിചയം എന്ന് പറയുക.";
            case LANG_HINDI:
                return "मुझे समझ नहीं आया कि आप कौन सा LMS कंटेंट चाहते हैं। कृपया कक्षा संख्या बताएं या LMS परिचय कहें।";
            case LANG_ARABIC:
                return "لم أفهم أي محتوى LMS تريد. يرجى تحديد رقم الصف أو قول مقدمة LMS.";
            default:
                return "I didn't understand which LMS content you want. Please specify the class number or say LMS introduction.";
        }
    }

    /**
     * Normalize English commands to standard ESP32 commands
     */
    private String normalizeEnglishCommand(String input) {
        String lowerInput = input.toLowerCase();

        // Forward movement variations
        if (lowerInput.contains("forward") || lowerInput.contains("front") ||
            lowerInput.contains("go") || lowerInput.contains("move") ||
            lowerInput.contains("advance")) {
            return "move forward";
        }

        // Backward movement variations
        if (lowerInput.contains("backward") || lowerInput.contains("back") ||
            lowerInput.contains("reverse") || lowerInput.contains("retreat")) {
            return "move backward";
        }

        // Left turn variations
        if (lowerInput.contains("left") || lowerInput.contains("rotate left") ||
            lowerInput.contains("spin left")) {
            return "turn left";
        }

        // Right turn variations
        if (lowerInput.contains("right") || lowerInput.contains("rotate right") ||
            lowerInput.contains("spin right")) {
            return "turn right";
        }

        // Stop variations
        if (lowerInput.contains("stop") || lowerInput.contains("halt") ||
            lowerInput.contains("pause") || lowerInput.contains("freeze") ||
            lowerInput.contains("brake")) {
            return "stop";
        }

        // Wave variations
        if (lowerInput.contains("wave") || lowerInput.contains("hello") ||
            lowerInput.contains("greet")) {
            return "wave";
        }

        // Point variations
        if (lowerInput.contains("point") || lowerInput.contains("indicate") ||
            lowerInput.contains("show direction")) {
            return "point";
        }

        // Rest variations
        if (lowerInput.contains("rest") || lowerInput.contains("relax") ||
            lowerInput.contains("default position") || lowerInput.contains("home position")) {
            return "rest";
        }

        // Head center variations
        if (lowerInput.contains("center head") || lowerInput.contains("head center") ||
            lowerInput.contains("look center") || lowerInput.contains("head straight")) {
            return "center head";
        }

        // Look up variations
        if (lowerInput.contains("look up") || lowerInput.contains("head up") ||
            lowerInput.contains("up") || lowerInput.contains("tilt up")) {
            return "look up";
        }

        // Look down variations
        if (lowerInput.contains("look down") || lowerInput.contains("head down") ||
            lowerInput.contains("down") || lowerInput.contains("tilt down")) {
            return "look down";
        }

        return input; // Return original if no normalization found
    }
}
