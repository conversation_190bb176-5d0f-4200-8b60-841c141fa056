<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="@color/robot_background">

    <!-- Title -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="🎤 Voice Command Duration Test"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="@color/robot_primary"
        android:gravity="center"
        android:layout_marginBottom="24dp" />

    <!-- Current Settings Display -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/settings_section_background"
        android:padding="16dp"
        android:layout_marginBottom="24dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Current Duration Settings"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            android:layout_marginBottom="12dp" />

        <TextView
            android:id="@+id/forward_duration_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Forward/Backward Duration: 3.0 seconds"
            android:textSize="16sp"
            android:textColor="@color/text_secondary"
            android:layout_marginBottom="8dp" />

        <TextView
            android:id="@+id/side_duration_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Left/Right Duration: 2.0 seconds"
            android:textSize="16sp"
            android:textColor="@color/text_secondary"
            android:layout_marginBottom="12dp" />

        <Button
            android:id="@+id/refresh_settings_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="🔄 Refresh Settings"
            android:textColor="@color/white"
            android:background="@drawable/control_button_background"
            android:layout_gravity="center" />

    </LinearLayout>

    <!-- Test Commands Section -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Test Voice Commands"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@color/text_primary"
        android:layout_marginBottom="16dp" />

    <!-- Forward/Backward Commands -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="16dp">

        <Button
            android:id="@+id/test_forward_button"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="⬆️ Forward"
            android:textColor="@color/white"
            android:background="@drawable/control_button_background"
            android:layout_marginEnd="8dp" />

        <Button
            android:id="@+id/test_backward_button"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="⬇️ Backward"
            android:textColor="@color/white"
            android:background="@drawable/control_button_background"
            android:layout_marginStart="8dp" />

    </LinearLayout>

    <!-- Left/Right Commands -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="24dp">

        <Button
            android:id="@+id/test_left_button"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="⬅️ Turn Left"
            android:textColor="@color/white"
            android:background="@drawable/control_button_background"
            android:layout_marginEnd="8dp" />

        <Button
            android:id="@+id/test_right_button"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="➡️ Turn Right"
            android:textColor="@color/white"
            android:background="@drawable/control_button_background"
            android:layout_marginStart="8dp" />

    </LinearLayout>

    <!-- Instructions -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Instructions:\n• Adjust voice command durations in Settings\n• Use 'Refresh Settings' to update display\n• Test buttons simulate voice commands\n• Check logs for command execution details"
        android:textSize="14sp"
        android:textColor="@color/text_secondary"
        android:background="@drawable/settings_section_background"
        android:padding="12dp"
        android:lineSpacingExtra="4dp" />

</LinearLayout>
