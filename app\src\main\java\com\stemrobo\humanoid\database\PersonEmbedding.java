package com.stemrobo.humanoid.database;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.ForeignKey;
import androidx.room.Ignore;
import androidx.room.Index;
import androidx.room.PrimaryKey;

/**
 * PersonEmbedding entity for storing multiple face embeddings per person
 * This allows for better recognition accuracy by storing multiple samples
 */
@Entity(tableName = "person_embeddings",
        foreignKeys = @ForeignKey(entity = Person.class,
                                 parentColumns = "uid",
                                 childColumns = "person_id",
                                 onDelete = ForeignKey.CASCADE),
        indices = {@Index(value = {"person_id"})})
public class PersonEmbedding {
    
    @PrimaryKey(autoGenerate = true)
    public int id;

    @ColumnInfo(name = "person_id")
    public int personId;

    // Face embedding stored as JSON string (192-dimensional vector from FaceNet)
    @ColumnInfo(name = "embedding_json")
    public String embeddingJson;
    
    // Timestamp when this embedding was created
    @ColumnInfo(name = "created_timestamp")
    public long createdTimestamp;
    
    // Quality score of this embedding (0.0 to 1.0)
    @ColumnInfo(name = "quality_score")
    public float qualityScore;
    
    // Whether this is the primary/best embedding for the person
    @ColumnInfo(name = "is_primary")
    public boolean isPrimary;

    // Default constructor
    public PersonEmbedding() {
        this.createdTimestamp = System.currentTimeMillis();
        this.qualityScore = 0.0f;
        this.isPrimary = false;
    }

    // Constructor with person ID and embedding
    @Ignore
    public PersonEmbedding(int personId, String embeddingJson, float qualityScore) {
        this();
        this.personId = personId;
        this.embeddingJson = embeddingJson;
        this.qualityScore = qualityScore;
    }
    
    @Override
    public String toString() {
        return "PersonEmbedding{" +
                "id=" + id +
                ", personId=" + personId +
                ", qualityScore=" + qualityScore +
                ", isPrimary=" + isPrimary +
                '}';
    }
}