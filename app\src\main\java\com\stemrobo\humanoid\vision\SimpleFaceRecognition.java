package com.stemrobo.humanoid.vision;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Rect;
import android.speech.tts.TextToSpeech;
import android.util.Log;
import com.google.mlkit.vision.face.Face;
import com.stemrobo.humanoid.database.Person;
import com.stemrobo.humanoid.database.PersonDatabase;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * Simple Face Recognition System
 * Integrates with face detection and person database
 * Uses face tracking IDs for basic recognition
 */
public class SimpleFaceRecognition {
    
    private static final String TAG = "SimpleFaceRecognition";
    
    private final Context context;
    private final PersonDatabase database;
    private TextToSpeech textToSpeech;
    
    // Face tracking and recognition
    private final Map<Integer, String> trackedFaces = new HashMap<>(); // trackingId -> personName
    private final Map<Integer, Long> lastGreetingTime = new HashMap<>(); // trackingId -> timestamp
    private static final long GREETING_COOLDOWN = 10000; // 10 seconds
    
    // Recognition callback
    private RecognitionCallback callback;
    
    public interface RecognitionCallback {
        void onPersonRecognized(String personName, Face face);
        void onUnknownPersonDetected(Face face);
        void onPersonRegistered(String personName, boolean success);
    }
    
    public SimpleFaceRecognition(Context context) {
        this.context = context;
        this.database = PersonDatabase.getInstance(context);
        
        initializeTextToSpeech();
    }
    
    /**
     * Initialize Text-to-Speech for greetings
     */
    private void initializeTextToSpeech() {
        textToSpeech = new TextToSpeech(context, status -> {
            if (status == TextToSpeech.SUCCESS) {
                textToSpeech.setLanguage(Locale.US);
                Log.d(TAG, "Text-to-Speech initialized successfully");
            } else {
                Log.e(TAG, "Text-to-Speech initialization failed");
            }
        });
    }
    
    /**
     * Set recognition callback
     */
    public void setCallback(RecognitionCallback callback) {
        this.callback = callback;
    }
    
    /**
     * Process detected faces for recognition
     */
    public void processFaces(List<Face> faces) {
        for (Face face : faces) {
            processSingleFace(face);
        }
    }
    
    /**
     * Process a single detected face
     */
    private void processSingleFace(Face face) {
        Integer trackingId = face.getTrackingId();
        
        if (trackingId == null) {
            // No tracking ID, treat as unknown
            if (callback != null) {
                callback.onUnknownPersonDetected(face);
            }
            return;
        }
        
        // Check if we already know this tracked face
        if (trackedFaces.containsKey(trackingId)) {
            String personName = trackedFaces.get(trackingId);
            
            if (callback != null) {
                callback.onPersonRecognized(personName, face);
            }
            
            // Check if we should greet again
            if (shouldGreetPerson(trackingId)) {
                greetPerson(personName);
                lastGreetingTime.put(trackingId, System.currentTimeMillis());
            }
            
        } else {
            // New face, try to recognize from database
            String recognizedName = recognizeFaceFromDatabase(face);
            
            if (recognizedName != null) {
                // Known person
                trackedFaces.put(trackingId, recognizedName);
                
                if (callback != null) {
                    callback.onPersonRecognized(recognizedName, face);
                }
                
                // Greet the person
                greetPerson(recognizedName);
                lastGreetingTime.put(trackingId, System.currentTimeMillis());
                
            } else {
                // Unknown person
                if (callback != null) {
                    callback.onUnknownPersonDetected(face);
                }
            }
        }
    }
    
    /**
     * Simple face recognition from database
     * This is a placeholder - in a full implementation, this would use
     * face embeddings and similarity matching
     */
    private String recognizeFaceFromDatabase(Face face) {
        try {
            List<Person> persons = database.personDao().getAllPersons();
            
            if (persons.isEmpty()) {
                return null;
            }
            
            // For now, we'll use a simple approach:
            // If there's only one person in database, assume it's them
            // In a real implementation, this would use face embeddings
            if (persons.size() == 1) {
                Person person = persons.get(0);
                Log.d(TAG, "Simple recognition: assuming face is " + person.name);
                return person.name;
            }
            
            // Multiple persons - would need actual face recognition here
            // For now, return null (unknown)
            return null;
            
        } catch (Exception e) {
            Log.e(TAG, "Error recognizing face from database", e);
            return null;
        }
    }
    
    /**
     * Register a new person with their face
     * This is a simplified version - in a full implementation,
     * this would capture and store face embeddings
     */
    public void registerPerson(String name, Face face) {
        try {
            // Create person record
            Person person = new Person();
            person.name = name;
            person.embeddingJson = "{}"; // Placeholder - would store actual embeddings
            person.createdTimestamp = System.currentTimeMillis();
            person.recognitionCount = 0;
            person.lastSeenTimestamp = 0;
            person.confidenceThreshold = 0.6f;
            
            // Save to database
            long id = database.personDao().insertPerson(person);
            
            boolean success = id > 0;
            
            if (success) {
                // Associate with current tracking ID if available
                Integer trackingId = face.getTrackingId();
                if (trackingId != null) {
                    trackedFaces.put(trackingId, name);
                }
                
                Log.d(TAG, "Person registered successfully: " + name + " (ID: " + id + ")");
            } else {
                Log.e(TAG, "Failed to register person: " + name);
            }
            
            if (callback != null) {
                callback.onPersonRegistered(name, success);
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error registering person: " + name, e);
            if (callback != null) {
                callback.onPersonRegistered(name, false);
            }
        }
    }
    
    /**
     * Check if we should greet a person (cooldown check)
     */
    private boolean shouldGreetPerson(Integer trackingId) {
        Long lastGreeting = lastGreetingTime.get(trackingId);
        if (lastGreeting == null) {
            return true;
        }
        
        return System.currentTimeMillis() - lastGreeting > GREETING_COOLDOWN;
    }
    
    /**
     * Greet a recognized person
     */
    private void greetPerson(String personName) {
        String greeting = "Hi " + personName + ", how are you? I am STEM-Xpert robot.";
        
        if (textToSpeech != null) {
            textToSpeech.speak(greeting, TextToSpeech.QUEUE_FLUSH, null, null);
            Log.d(TAG, "Greeting: " + greeting);
        }
    }
    
    /**
     * Get all registered persons
     */
    public List<Person> getAllPersons() {
        try {
            return database.personDao().getAllPersons();
        } catch (Exception e) {
            Log.e(TAG, "Error getting all persons", e);
            return null;
        }
    }
    
    /**
     * Delete a person from database
     */
    public boolean deletePerson(int personId) {
        try {
            database.personDao().deletePersonById(personId);
            
            // Remove from tracked faces if present
            trackedFaces.entrySet().removeIf(entry -> {
                // This is a simplified approach - in a real implementation,
                // we'd need to match by person ID
                return false;
            });
            
            Log.d(TAG, "Person deleted: " + personId);
            return true;
            
        } catch (Exception e) {
            Log.e(TAG, "Error deleting person: " + personId, e);
            return false;
        }
    }
    
    /**
     * Clear all tracked faces (useful when switching cameras)
     */
    public void clearTrackedFaces() {
        trackedFaces.clear();
        lastGreetingTime.clear();
        Log.d(TAG, "Cleared all tracked faces");
    }
    
    /**
     * Get number of currently tracked faces
     */
    public int getTrackedFaceCount() {
        return trackedFaces.size();
    }
    
    /**
     * Cleanup resources
     */
    public void cleanup() {
        clearTrackedFaces();
        
        if (textToSpeech != null) {
            textToSpeech.stop();
            textToSpeech.shutdown();
        }
        
        Log.d(TAG, "Face recognition cleaned up");
    }
}
