package com.stemrobo.humanoid.database;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import java.util.List;

/**
 * Data Access Object for ChatHistory operations
 */
@Dao
public interface ChatHistoryDao {
    
    /**
     * Get all messages for a conversation ordered by sequence number
     */
    @Query("SELECT * FROM chat_history WHERE conversation_id = :conversationId ORDER BY sequence_number ASC")
    List<ChatHistory> getMessagesForConversation(String conversationId);
    
    /**
     * Get recent messages for a conversation (last N messages)
     */
    @Query("SELECT * FROM chat_history WHERE conversation_id = :conversationId ORDER BY sequence_number DESC LIMIT :limit")
    List<ChatHistory> getRecentMessagesForConversation(String conversationId, int limit);
    
    /**
     * Get last N Q&A pairs for conversation memory (for AI context)
     * Returns pairs of USER followed by AI messages
     */
    @Query("SELECT * FROM chat_history WHERE conversation_id = :conversationId AND message_type IN (0, 1) ORDER BY sequence_number DESC LIMIT :limit")
    List<ChatHistory> getLastQAPairsForConversation(String conversationId, int limit);
    
    /**
     * Get message by ID
     */
    @Query("SELECT * FROM chat_history WHERE id = :messageId")
    ChatHistory getMessageById(long messageId);
    
    /**
     * Get next sequence number for conversation
     */
    @Query("SELECT COALESCE(MAX(sequence_number), 0) + 1 FROM chat_history WHERE conversation_id = :conversationId")
    int getNextSequenceNumber(String conversationId);
    
    /**
     * Get message count for conversation
     */
    @Query("SELECT COUNT(*) FROM chat_history WHERE conversation_id = :conversationId")
    int getMessageCountForConversation(String conversationId);
    
    /**
     * Get user messages for conversation
     */
    @Query("SELECT * FROM chat_history WHERE conversation_id = :conversationId AND message_type = 0 ORDER BY sequence_number ASC")
    List<ChatHistory> getUserMessagesForConversation(String conversationId);
    
    /**
     * Get AI messages for conversation
     */
    @Query("SELECT * FROM chat_history WHERE conversation_id = :conversationId AND message_type = 1 ORDER BY sequence_number ASC")
    List<ChatHistory> getAIMessagesForConversation(String conversationId);
    
    /**
     * Get messages by type across all conversations
     */
    @Query("SELECT * FROM chat_history WHERE message_type = :messageType ORDER BY timestamp DESC LIMIT :limit")
    List<ChatHistory> getMessagesByType(int messageType, int limit);
    
    /**
     * Get messages within time range
     */
    @Query("SELECT * FROM chat_history WHERE conversation_id = :conversationId AND timestamp BETWEEN :startTime AND :endTime ORDER BY sequence_number ASC")
    List<ChatHistory> getMessagesInTimeRange(String conversationId, long startTime, long endTime);
    
    /**
     * Search messages by text content
     */
    @Query("SELECT * FROM chat_history WHERE conversation_id = :conversationId AND message_text LIKE '%' || :searchQuery || '%' ORDER BY sequence_number ASC")
    List<ChatHistory> searchMessagesInConversation(String conversationId, String searchQuery);
    
    /**
     * Get average response time for AI messages in conversation
     */
    @Query("SELECT AVG(response_time_ms) FROM chat_history WHERE conversation_id = :conversationId AND message_type = 1 AND response_time_ms > 0")
    double getAverageResponseTime(String conversationId);
    
    /**
     * Insert new message
     */
    @Insert
    long insertMessage(ChatHistory message);
    
    /**
     * Insert multiple messages
     */
    @Insert
    void insertMessages(List<ChatHistory> messages);
    
    /**
     * Update existing message
     */
    @Update
    void updateMessage(ChatHistory message);
    
    /**
     * Delete message
     */
    @Delete
    void deleteMessage(ChatHistory message);
    
    /**
     * Delete all messages for conversation
     */
    @Query("DELETE FROM chat_history WHERE conversation_id = :conversationId")
    void deleteMessagesForConversation(String conversationId);
    
    /**
     * Delete old messages (keep only recent ones per conversation)
     */
    @Query("DELETE FROM chat_history WHERE conversation_id = :conversationId AND sequence_number NOT IN (SELECT sequence_number FROM chat_history WHERE conversation_id = :conversationId ORDER BY sequence_number DESC LIMIT :keepCount)")
    void deleteOldMessagesForConversation(String conversationId, int keepCount);
    
    /**
     * Delete messages older than timestamp
     */
    @Query("DELETE FROM chat_history WHERE timestamp < :cutoffTimestamp")
    void deleteOldMessages(long cutoffTimestamp);
    
    /**
     * Clear all chat history (for testing/reset)
     */
    @Query("DELETE FROM chat_history")
    void clearAllHistory();
    
    /**
     * Get conversation summary data (first and last messages)
     */
    @Query("SELECT * FROM chat_history WHERE conversation_id = :conversationId AND (sequence_number = (SELECT MIN(sequence_number) FROM chat_history WHERE conversation_id = :conversationId) OR sequence_number = (SELECT MAX(sequence_number) FROM chat_history WHERE conversation_id = :conversationId)) ORDER BY sequence_number ASC")
    List<ChatHistory> getConversationSummaryMessages(String conversationId);
}
