<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ResponsiveVoice TTS</title>
    <script src="https://code.responsivevoice.org/responsivevoice.js?key=ouA0bAh2"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: transparent;
            font-family: Arial, sans-serif;
        }
        .container {
            display: none; /* Hidden WebView for TTS only */
            padding: 20px;
            text-align: center;
        }
        .status {
            color: #333;
            font-size: 14px;
            margin: 10px 0;
        }
        .debug {
            color: #666;
            font-size: 12px;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="status" id="status">ResponsiveVoice Ready</div>
        <div class="debug" id="debug"></div>
    </div>

    <script>
        // Global variables
        let isReady = false;
        let currentVoice = 'UK English Female';
        let speechRate = 1.0;
        let speechPitch = 1.0;

        // Voice mappings for male and female voices
        const VOICE_MAPPINGS = {
            male: {
                english: 'UK English Male',
                fallback: 'US English Male'
            },
            female: {
                english: 'UK English Female',
                fallback: 'US English Female'
            }
        };

        // Initialize ResponsiveVoice with enhanced audio support
        function initializeResponsiveVoice() {
            try {
                console.log('Initializing ResponsiveVoice...');

                if (typeof responsiveVoice !== 'undefined') {
                    // Initialize ResponsiveVoice
                    responsiveVoice.init();

                    // CRITICAL: Enable audio context for mobile browsers
                    enableAudioContext();

                    isReady = true;
                    updateStatus('ResponsiveVoice initialized successfully');

                    // Test voice availability
                    const voices = responsiveVoice.getVoices();
                    updateDebug('Available voices: ' + voices.length);
                    console.log('ResponsiveVoice voices available:', voices.length);

                    // Log available voices for debugging
                    voices.forEach((voice, index) => {
                        console.log(`Voice ${index}: ${voice.name}`);
                    });

                    // Notify Android that initialization is complete
                    if (typeof Android !== 'undefined') {
                        Android.onResponsiveVoiceReady();
                    }
                } else {
                    throw new Error('ResponsiveVoice not loaded');
                }
            } catch (error) {
                console.error('ResponsiveVoice initialization error:', error);
                updateStatus('Error: ' + error.message);
                updateDebug('ResponsiveVoice initialization failed');

                // Notify Android of initialization failure
                if (typeof Android !== 'undefined') {
                    Android.onResponsiveVoiceError(error.message);
                }
            }
        }

        // CRITICAL: Enable audio context for mobile browsers
        function enableAudioContext() {
            try {
                // Create and resume audio context if needed
                if (typeof AudioContext !== 'undefined' || typeof webkitAudioContext !== 'undefined') {
                    const AudioContextClass = window.AudioContext || window.webkitAudioContext;
                    if (!window.audioContext) {
                        window.audioContext = new AudioContextClass();
                    }

                    if (window.audioContext.state === 'suspended') {
                        window.audioContext.resume().then(() => {
                            console.log('Audio context resumed successfully');
                        }).catch(err => {
                            console.error('Failed to resume audio context:', err);
                        });
                    }
                }

                // Simulate user interaction to unlock audio (CRITICAL for mobile)
                simulateUserInteraction();

            } catch (error) {
                console.error('Error enabling audio context:', error);
            }
        }

        // Simulate user interaction to unlock audio playback
        function simulateUserInteraction() {
            try {
                // Create a silent audio element and play it to unlock audio
                const audio = new Audio();
                audio.src = 'data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT';
                audio.volume = 0.01; // Very low volume
                audio.play().then(() => {
                    console.log('Audio unlocked successfully');
                }).catch(err => {
                    console.log('Audio unlock attempt (this is normal):', err.message);
                });
            } catch (error) {
                console.log('Audio unlock simulation (this is normal):', error.message);
            }
        }

        // Speak text using ResponsiveVoice with enhanced debugging
        function speak(text, gender, rate, pitch) {
            console.log('=== ResponsiveVoice Speak Called ===');
            console.log('Text:', text);
            console.log('Gender:', gender);
            console.log('Rate:', rate);
            console.log('Pitch:', pitch);
            console.log('IsReady:', isReady);

            try {
                if (!isReady) {
                    throw new Error('ResponsiveVoice not ready');
                }

                // Set voice based on gender
                const voiceConfig = VOICE_MAPPINGS[gender] || VOICE_MAPPINGS.female;
                currentVoice = voiceConfig.english;

                console.log('Selected voice config:', voiceConfig);
                console.log('Current voice:', currentVoice);

                // Check if preferred voice is available, fallback if needed
                const availableVoices = responsiveVoice.getVoices();
                console.log('Available voices count:', availableVoices.length);

                const voiceExists = availableVoices.some(voice => voice.name === currentVoice);
                console.log('Voice exists:', voiceExists);

                if (!voiceExists) {
                    currentVoice = voiceConfig.fallback;
                    updateDebug('Using fallback voice: ' + currentVoice);
                    console.log('Using fallback voice:', currentVoice);
                }

                // Set speech parameters
                speechRate = parseFloat(rate) || 1.0;
                speechPitch = parseFloat(pitch) || 1.0;

                updateStatus('Speaking: ' + text.substring(0, 50) + '...');
                updateDebug('Voice: ' + currentVoice + ', Rate: ' + speechRate + ', Pitch: ' + speechPitch);

                console.log('Final speech parameters:');
                console.log('- Voice:', currentVoice);
                console.log('- Rate:', speechRate);
                console.log('- Pitch:', speechPitch);
                console.log('- Volume: 1.0');

                // Speak with ResponsiveVoice
                console.log('Calling responsiveVoice.speak...');
                responsiveVoice.speak(text, currentVoice, {
                    rate: speechRate,
                    pitch: speechPitch,
                    volume: 1.0,
                    onstart: function() {
                        console.log('ResponsiveVoice onstart callback triggered');
                        updateStatus('Speech started');
                        if (typeof Android !== 'undefined') {
                            Android.onSpeechStart();
                        }
                    },
                    onend: function() {
                        console.log('ResponsiveVoice onend callback triggered');
                        updateStatus('Speech completed');
                        if (typeof Android !== 'undefined') {
                            Android.onSpeechEnd();
                        }
                    },
                    onerror: function(error) {
                        console.error('ResponsiveVoice onerror callback triggered:', error);
                        updateStatus('Speech error: ' + error);
                        if (typeof Android !== 'undefined') {
                            Android.onSpeechError(error.toString());
                        }
                    }
                });

                console.log('ResponsiveVoice.speak() called successfully');

            } catch (error) {
                console.error('Exception in speak function:', error);
                updateStatus('Error speaking: ' + error.message);
                updateDebug('Speech failed: ' + error.toString());

                if (typeof Android !== 'undefined') {
                    Android.onSpeechError(error.message);
                }
            }
        }

        // Stop current speech
        function stopSpeech() {
            try {
                if (isReady && responsiveVoice.isPlaying()) {
                    responsiveVoice.cancel();
                    updateStatus('Speech stopped');
                    
                    if (typeof Android !== 'undefined') {
                        Android.onSpeechEnd();
                    }
                }
            } catch (error) {
                updateStatus('Error stopping speech: ' + error.message);
            }
        }

        // Check if currently speaking
        function isSpeaking() {
            try {
                return isReady && responsiveVoice.isPlaying();
            } catch (error) {
                return false;
            }
        }

        // Get available voices
        function getAvailableVoices() {
            try {
                if (isReady) {
                    const voices = responsiveVoice.getVoices();
                    return JSON.stringify(voices.map(voice => ({
                        name: voice.name,
                        lang: voice.lang
                    })));
                }
                return '[]';
            } catch (error) {
                return '[]';
            }
        }

        // Update status display
        function updateStatus(message) {
            const statusElement = document.getElementById('status');
            if (statusElement) {
                statusElement.textContent = message;
            }
            console.log('ResponsiveVoice Status: ' + message);
        }

        // Update debug display
        function updateDebug(message) {
            const debugElement = document.getElementById('debug');
            if (debugElement) {
                debugElement.textContent = message;
            }
            console.log('ResponsiveVoice Debug: ' + message);
        }

        // Initialize when page loads with multiple retry attempts
        window.addEventListener('load', function() {
            updateStatus('Loading ResponsiveVoice...');
            console.log('Page loaded, initializing ResponsiveVoice...');

            // Try multiple times with increasing delays
            let attempts = 0;
            const maxAttempts = 5;

            function tryInitialize() {
                attempts++;
                console.log('ResponsiveVoice initialization attempt: ' + attempts);

                if (typeof responsiveVoice !== 'undefined') {
                    console.log('ResponsiveVoice found, initializing...');
                    initializeResponsiveVoice();
                } else if (attempts < maxAttempts) {
                    console.log('ResponsiveVoice not ready, retrying in ' + (attempts * 1000) + 'ms...');
                    setTimeout(tryInitialize, attempts * 1000);
                } else {
                    console.error('ResponsiveVoice failed to load after ' + maxAttempts + ' attempts');
                    updateStatus('ResponsiveVoice failed to load');
                    if (typeof Android !== 'undefined') {
                        Android.onResponsiveVoiceError('Failed to load after ' + maxAttempts + ' attempts');
                    }
                }
            }

            // Start first attempt after a short delay
            setTimeout(tryInitialize, 500);
        });

        // Handle page visibility changes
        document.addEventListener('visibilitychange', function() {
            if (document.hidden && isSpeaking()) {
                // Don't stop speech when page becomes hidden
                updateDebug('Page hidden, continuing speech');
            }
        });

        // Test function for debugging
        function testResponsiveVoice() {
            console.log('=== ResponsiveVoice Test Function ===');
            console.log('ResponsiveVoice available:', typeof responsiveVoice !== 'undefined');
            console.log('IsReady:', isReady);

            if (typeof responsiveVoice !== 'undefined') {
                console.log('ResponsiveVoice version:', responsiveVoice.version || 'unknown');
                const voices = responsiveVoice.getVoices();
                console.log('Available voices:', voices.length);
                voices.forEach((voice, index) => {
                    console.log(`Voice ${index}: ${voice.name} (${voice.lang})`);
                });

                // Test with a simple phrase
                console.log('Testing with simple phrase...');
                responsiveVoice.speak('ResponsiveVoice test successful', 'UK English Male', {
                    rate: 1.0,
                    pitch: 1.0,
                    volume: 1.0,
                    onstart: function() {
                        console.log('Test speech started');
                        updateStatus('Test speech started');
                    },
                    onend: function() {
                        console.log('Test speech completed');
                        updateStatus('Test speech completed');
                    },
                    onerror: function(error) {
                        console.error('Test speech error:', error);
                        updateStatus('Test speech error: ' + error);
                    }
                });
            } else {
                console.error('ResponsiveVoice not available');
                updateStatus('ResponsiveVoice not available');
            }
        }

        // Expose functions to Android WebView
        window.speakText = speak;
        window.stopSpeech = stopSpeech;
        window.isSpeaking = isSpeaking;
        window.getAvailableVoices = getAvailableVoices;
        window.testResponsiveVoice = testResponsiveVoice;

        // Debug logging
        console.log('ResponsiveVoice HTML template loaded');
    </script>
</body>
</html>
