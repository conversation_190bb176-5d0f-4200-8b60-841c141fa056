package com.stemrobo.humanoid.communication;

import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.hardware.usb.UsbDevice;
import android.hardware.usb.UsbDeviceConnection;
import android.hardware.usb.UsbManager;
import android.util.Log;

import com.hoho.android.usbserial.driver.UsbSerialDriver;
import com.hoho.android.usbserial.driver.UsbSerialPort;
import com.hoho.android.usbserial.driver.UsbSerialProber;
import com.hoho.android.usbserial.util.SerialInputOutputManager;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class USBSerialManager implements SerialInputOutputManager.Listener {
    private static final String TAG = "USBSerialManager";
    private static final String ACTION_USB_PERMISSION = "com.stemrobo.humanoid.USB_PERMISSION";
    private static final int WRITE_WAIT_MILLIS = 200;

    // Configurable serial parameters
    private int baudRate = 115200;
    private int dataBits = 8;
    private int stopBits = UsbSerialPort.STOPBITS_1;
    private int parity = UsbSerialPort.PARITY_NONE;
    private int portNumber = 0; // Default to first port

    private static USBSerialManager instance;
    private Context context;
    private UsbManager usbManager;
    private UsbSerialPort serialPort;
    private UsbDeviceConnection connection;
    private UsbDevice esp32Device;
    private SerialInputOutputManager ioManager;
    private boolean isConnected = false;
    private ExecutorService executorService;
    
    public interface USBConnectionListener {
        void onUSBConnected();
        void onUSBDisconnected();
        void onUSBPermissionGranted();
        void onUSBPermissionDenied();
        void onCommandSent(String command, boolean success);
        void onDataReceived(String data);
        void onError(String error);
    }
    
    private USBConnectionListener listener;
    
    private USBSerialManager(Context context) {
        this.context = context.getApplicationContext();
        this.usbManager = (UsbManager) context.getSystemService(Context.USB_SERVICE);
        this.executorService = Executors.newSingleThreadExecutor();
        registerUSBReceiver();
    }
    
    public static synchronized USBSerialManager getInstance(Context context) {
        if (instance == null) {
            instance = new USBSerialManager(context);
        }
        return instance;
    }

    // Serial port configuration methods
    public void setSerialParameters(int baudRate, int dataBits, int stopBits, int parity) {
        this.baudRate = baudRate;
        this.dataBits = dataBits;
        this.stopBits = stopBits;
        this.parity = parity;
        Log.d(TAG, "Serial parameters updated: " + baudRate + " baud, " + dataBits + " data bits");

        // If already connected, update the connection with new parameters
        if (isConnected && serialPort != null) {
            try {
                serialPort.setParameters(baudRate, dataBits, stopBits, parity);
                Log.d(TAG, "Applied new serial parameters to active connection");
            } catch (Exception e) {
                Log.e(TAG, "Failed to update serial parameters: " + e.getMessage());
            }
        }
    }

    public void setBaudRate(int baudRate) {
        this.baudRate = baudRate;
        Log.d(TAG, "Baud rate set to: " + baudRate);
    }

    public void setPortNumber(int portNumber) {
        this.portNumber = portNumber;
        Log.d(TAG, "Port number set to: " + portNumber);
    }

    public int getBaudRate() { return baudRate; }
    public int getDataBits() { return dataBits; }
    public int getStopBits() { return stopBits; }
    public int getParity() { return parity; }
    public int getPortNumber() { return portNumber; }
    
    public void setUSBConnectionListener(USBConnectionListener listener) {
        this.listener = listener;
    }
    
    // USB Permission Broadcast Receiver
    private final BroadcastReceiver usbReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            Log.d(TAG, "USB Broadcast received: " + action);
            
            if (ACTION_USB_PERMISSION.equals(action)) {
                synchronized (this) {
                    UsbDevice device = intent.getParcelableExtra(UsbManager.EXTRA_DEVICE);
                    if (intent.getBooleanExtra(UsbManager.EXTRA_PERMISSION_GRANTED, false)) {
                        if (device != null) {
                            Log.d(TAG, "USB permission granted for device: " + device.getDeviceName());
                            esp32Device = device;
                            connectToDevice();
                            if (listener != null) {
                                listener.onUSBPermissionGranted();
                            }
                        }
                    } else {
                        Log.w(TAG, "USB permission denied for device: " + (device != null ? device.getDeviceName() : "null"));
                        if (listener != null) {
                            listener.onUSBPermissionDenied();
                        }
                    }
                }
            } else if (UsbManager.ACTION_USB_DEVICE_ATTACHED.equals(action)) {
                UsbDevice device = intent.getParcelableExtra(UsbManager.EXTRA_DEVICE);
                Log.d(TAG, "USB device attached: " + (device != null ? device.getDeviceName() : "null"));
                if (device != null) {
                    checkAndRequestPermission(device);
                }
            } else if (UsbManager.ACTION_USB_DEVICE_DETACHED.equals(action)) {
                UsbDevice device = intent.getParcelableExtra(UsbManager.EXTRA_DEVICE);
                Log.d(TAG, "USB device detached: " + (device != null ? device.getDeviceName() : "null"));
                if (device != null && device.equals(esp32Device)) {
                    disconnect();
                    if (listener != null) {
                        listener.onUSBDisconnected();
                    }
                }
            }
        }
    };
    
    private void registerUSBReceiver() {
        IntentFilter filter = new IntentFilter();
        filter.addAction(ACTION_USB_PERMISSION);
        filter.addAction(UsbManager.ACTION_USB_DEVICE_ATTACHED);
        filter.addAction(UsbManager.ACTION_USB_DEVICE_DETACHED);
        context.registerReceiver(usbReceiver, filter);
        Log.d(TAG, "USB receiver registered");
    }
    
    public boolean scanForESP32Device() {
        Log.d(TAG, "Scanning for ESP32 USB devices...");
        
        List<UsbSerialDriver> availableDrivers = UsbSerialProber.getDefaultProber().findAllDrivers(usbManager);
        
        if (availableDrivers.isEmpty()) {
            Log.d(TAG, "No USB serial devices found");
            return false;
        }
        
        for (UsbSerialDriver driver : availableDrivers) {
            UsbDevice device = driver.getDevice();
            int vendorId = device.getVendorId();
            int productId = device.getProductId();
            
            Log.d(TAG, "Found USB device - Vendor ID: 0x" + Integer.toHexString(vendorId) + 
                       ", Product ID: 0x" + Integer.toHexString(productId) + 
                       ", Name: " + device.getDeviceName());
            
            // Check for ESP32 compatible devices
            if (isESP32Device(vendorId, productId)) {
                Log.d(TAG, "ESP32 compatible device found!");
                esp32Device = device;
                checkAndRequestPermission(device);
                return true;
            }
        }
        
        Log.d(TAG, "No ESP32 compatible devices found");
        return false;
    }
    
    private boolean isESP32Device(int vendorId, int productId) {
        // ESP32 USB CDC devices and common USB-to-Serial chips
        return vendorId == 0x303A ||  // Espressif Systems
               vendorId == 0x10C4 ||  // Silicon Labs CP210x
               vendorId == 0x1A86 ||  // QinHeng Electronics CH340
               vendorId == 0x0403 ||  // FTDI
               vendorId == 0x067B ||  // Prolific PL2303
               vendorId == 0x2341;    // Arduino
    }
    
    private void checkAndRequestPermission(UsbDevice device) {
        if (usbManager.hasPermission(device)) {
            Log.d(TAG, "USB permission already granted");
            esp32Device = device;
            connectToDevice();
            if (listener != null) {
                listener.onUSBPermissionGranted();
            }
        } else {
            Log.d(TAG, "Requesting USB permission...");
            PendingIntent permissionIntent = PendingIntent.getBroadcast(
                context, 0, new Intent(ACTION_USB_PERMISSION), 
                PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
            );
            usbManager.requestPermission(device, permissionIntent);
        }
    }
    
    private void connectToDevice() {
        if (esp32Device == null) {
            Log.e(TAG, "No ESP32 device to connect to");
            return;
        }

        executorService.execute(() -> {
            try {
                List<UsbSerialDriver> drivers = UsbSerialProber.getDefaultProber().findAllDrivers(usbManager);
                UsbSerialDriver driver = null;

                for (UsbSerialDriver d : drivers) {
                    if (d.getDevice().equals(esp32Device)) {
                        driver = d;
                        break;
                    }
                }

                if (driver == null) {
                    Log.e(TAG, "No driver found for ESP32 device");
                    if (listener != null) {
                        listener.onError("No driver found for ESP32 device");
                    }
                    return;
                }

                connection = usbManager.openDevice(esp32Device);
                if (connection == null) {
                    Log.e(TAG, "Failed to open USB device connection");
                    if (listener != null) {
                        listener.onError("Failed to open USB device connection");
                    }
                    return;
                }

                // Use configurable port number
                List<UsbSerialPort> ports = driver.getPorts();
                if (portNumber >= ports.size()) {
                    Log.w(TAG, "Port " + portNumber + " not available, using port 0");
                    portNumber = 0;
                }

                serialPort = ports.get(portNumber);
                serialPort.open(connection);
                serialPort.setParameters(baudRate, dataBits, stopBits, parity);

                // Set DTR and RTS for proper ESP32 communication
                try {
                    serialPort.setDTR(true);
                    serialPort.setRTS(true);
                } catch (UnsupportedOperationException e) {
                    Log.d(TAG, "Failed to set initial DTR/RTS", e);
                }

                // Start I/O manager for automatic read/write handling
                ioManager = new SerialInputOutputManager(serialPort, this);
                ioManager.start();

                isConnected = true;
                Log.d(TAG, "USB Serial connection established successfully - Port: " + portNumber + ", Baud: " + baudRate);

                if (listener != null) {
                    listener.onUSBConnected();
                }

            } catch (IOException e) {
                Log.e(TAG, "Error connecting to USB device: " + e.getMessage());
                isConnected = false;
                if (listener != null) {
                    listener.onError("USB connection failed: " + e.getMessage());
                }
            }
        });
    }
    
    public void sendCommand(String command) {
        if (!isConnected || serialPort == null) {
            Log.w(TAG, "USB not connected, cannot send command: " + command);
            if (listener != null) {
                listener.onCommandSent(command, false);
            }
            return;
        }
        
        executorService.execute(() -> {
            try {
                String commandWithNewline = command + "\n";
                byte[] data = commandWithNewline.getBytes();
                
                Log.d(TAG, "Sending USB command: " + command);
                serialPort.write(data, 1000);

                boolean success = true; // Assume success if no exception thrown
                Log.d(TAG, "USB command sent: " + command + ", data length: " + data.length);
                
                if (listener != null) {
                    listener.onCommandSent(command, success);
                }
                
            } catch (IOException e) {
                Log.e(TAG, "Error sending USB command: " + e.getMessage());
                if (listener != null) {
                    listener.onCommandSent(command, false);
                    listener.onError("USB send failed: " + e.getMessage());
                }
            }
        });
    }
    
    public boolean isConnected() {
        return isConnected && serialPort != null;
    }
    
    public void disconnect() {
        executorService.execute(() -> {
            try {
                // Stop I/O manager first
                if (ioManager != null) {
                    ioManager.stop();
                    ioManager = null;
                }

                // Close serial port
                if (serialPort != null) {
                    serialPort.close();
                    serialPort = null;
                }

                // Close USB connection
                if (connection != null) {
                    connection.close();
                    connection = null;
                }

                isConnected = false;
                esp32Device = null;
                Log.d(TAG, "USB Serial disconnected");

                if (listener != null) {
                    listener.onUSBDisconnected();
                }

            } catch (IOException e) {
                Log.e(TAG, "Error disconnecting USB: " + e.getMessage());
            }
        });
    }
    
    public void cleanup() {
        try {
            context.unregisterReceiver(usbReceiver);
        } catch (Exception e) {
            Log.e(TAG, "Error unregistering USB receiver: " + e.getMessage());
        }
        
        disconnect();
        
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
        
        Log.d(TAG, "USB Serial Manager cleaned up");
    }

    // SerialInputOutputManager.Listener implementation
    @Override
    public void onNewData(byte[] data) {
        String receivedData = new String(data);
        Log.d(TAG, "USB data received: " + receivedData.trim());

        if (listener != null) {
            listener.onDataReceived(receivedData);
        }
    }

    @Override
    public void onRunError(Exception e) {
        Log.e(TAG, "USB I/O error: " + e.getMessage());
        isConnected = false;

        if (listener != null) {
            listener.onError("USB I/O error: " + e.getMessage());
            listener.onUSBDisconnected();
        }
    }
}
