package com.stemrobo.humanoid.vision;

import android.content.Context;
import android.graphics.Bitmap;
import android.util.Log;

import com.google.mlkit.vision.face.Face;
import com.stemrobo.humanoid.database.PersonDatabase;

import java.util.List;

/**
 * Vision System Tester - Helper class to test face recognition and object detection fixes
 */
public class VisionSystemTester {
    
    private static final String TAG = "VisionSystemTester";
    
    private final Context context;
    private final FaceNetRecognition faceRecognition;
    private final ObjectDetectionManager objectDetection;
    
    public VisionSystemTester(Context context) {
        this.context = context;
        
        // Initialize components
        PersonDatabase database = PersonDatabase.getInstance(context);
        this.faceRecognition = new FaceNetRecognition(context);
        this.objectDetection = new ObjectDetectionManager(context);
    }
    
    /**
     * Test face recognition system
     */
    public void testFaceRecognition() {
        Log.d(TAG, "=== FACE RECOGNITION TEST ===");
        
        // Get current stats
        String stats = faceRecognition.getRecognitionStats();
        Log.d(TAG, "Current stats: " + stats);
        
        // Test cache clearing
        Log.d(TAG, "Testing cache clearing...");
        faceRecognition.forceReRecognition();
        
        // Test stale face clearing
        Log.d(TAG, "Testing stale face clearing...");
        faceRecognition.clearStaleTrackedFaces();
        
        Log.d(TAG, "Face recognition test completed");
    }
    
    /**
     * Test object detection system
     */
    public void testObjectDetection() {
        Log.d(TAG, "=== OBJECT DETECTION TEST ===");
        
        // Get current model status
        String status = objectDetection.getModelStatus();
        Log.d(TAG, "Current model status: " + status);
        
        // Test model reloading
        Log.d(TAG, "Testing model reload...");
        objectDetection.reloadModel();
        
        // Check status after reload
        String newStatus = objectDetection.getModelStatus();
        Log.d(TAG, "Status after reload: " + newStatus);
        
        Log.d(TAG, "Object detection test completed");
    }
    
    /**
     * Test both systems together
     */
    public void runFullTest() {
        Log.d(TAG, "=== FULL VISION SYSTEM TEST ===");
        
        testFaceRecognition();
        testObjectDetection();
        
        Log.d(TAG, "=== TEST COMPLETED ===");
    }
    
    /**
     * Simulate face recognition with debugging
     */
    public void simulateFaceRecognition(List<Face> faces, Bitmap bitmap) {
        Log.d(TAG, "Simulating face recognition with " + faces.size() + " faces");
        
        // Get stats before processing
        String statsBefore = faceRecognition.getRecognitionStats();
        Log.d(TAG, "Stats before: " + statsBefore);
        
        // Process faces
        faceRecognition.processFaces(faces, bitmap);
        
        // Get stats after processing
        String statsAfter = faceRecognition.getRecognitionStats();
        Log.d(TAG, "Stats after: " + statsAfter);
    }
    
    /**
     * Simulate object detection with debugging
     */
    public void simulateObjectDetection(Bitmap bitmap) {
        Log.d(TAG, "Simulating object detection");
        
        // Get status before detection
        String statusBefore = objectDetection.getModelStatus();
        Log.d(TAG, "Status before: " + statusBefore);
        
        // Set up callback to capture results
        objectDetection.setCallback(new ObjectDetectionManager.DetectionCallback() {
            @Override
            public void onObjectsDetected(List<ObjectDetectionManager.Detection> detections, Bitmap annotatedBitmap) {
                Log.d(TAG, "Detected " + detections.size() + " objects:");
                for (ObjectDetectionManager.Detection detection : detections) {
                    Log.d(TAG, "  - " + detection.toString());
                }
            }
            
            @Override
            public void onDetectionError(Exception error) {
                Log.e(TAG, "Detection error: " + error.getMessage());
            }
        });
        
        // Run detection
        objectDetection.detectObjects(bitmap);
    }
    
    /**
     * Reset all systems to clean state
     */
    public void resetSystems() {
        Log.d(TAG, "Resetting vision systems...");
        
        // Reset face recognition
        faceRecognition.forceReRecognition();
        faceRecognition.clearStaleTrackedFaces();
        
        // Reset object detection
        objectDetection.reloadModel();
        
        Log.d(TAG, "Systems reset completed");
    }
    
    /**
     * Get comprehensive system status
     */
    public String getSystemStatus() {
        StringBuilder status = new StringBuilder();
        status.append("=== VISION SYSTEM STATUS ===\n");
        status.append("Face Recognition: ").append(faceRecognition.getRecognitionStats()).append("\n");
        status.append("Object Detection: ").append(objectDetection.getModelStatus()).append("\n");
        status.append("===========================");
        return status.toString();
    }
    
    /**
     * Cleanup resources
     */
    public void cleanup() {
        if (faceRecognition != null) {
            faceRecognition.cleanup();
        }
        if (objectDetection != null) {
            objectDetection.cleanup();
        }
    }
}