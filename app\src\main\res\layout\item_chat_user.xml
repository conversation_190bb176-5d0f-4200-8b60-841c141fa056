<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="4dp"
    android:gravity="end">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="48dp"
        android:orientation="vertical"
        android:background="@drawable/user_message_background"
        android:padding="12dp">

        <TextView
            android:id="@+id/message_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="User message"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:lineSpacingExtra="2dp" />

        <TextView
            android:id="@+id/time_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_marginTop="4dp"
            android:text="12:34"
            android:textColor="@color/white"
            android:textSize="10sp"
            android:alpha="0.7" />

    </LinearLayout>

</LinearLayout>
