package com.stemrobo.humanoid.models;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Represents a single step in a preset sequence that can contain multiple simultaneous actions.
 * Each step has a start time and duration, and can execute multiple actions in parallel.
 */
public class PresetStep {
    private long id;
    private int startTimeMs;        // When this step starts (relative to preset start)
    private int durationMs;         // How long this step lasts
    private String name;            // Optional name for the step
    private List<PresetAction> actions;  // List of actions to execute simultaneously
    
    public PresetStep() {
        this.actions = new ArrayList<>();
    }
    
    public PresetStep(int startTimeMs, int durationMs) {
        this();
        this.startTimeMs = startTimeMs;
        this.durationMs = durationMs;
    }
    
    public PresetStep(int startTimeMs, int durationMs, String name) {
        this(startTimeMs, durationMs);
        this.name = name;
    }

    // Copy constructor for deep copying
    public PresetStep(PresetStep other) {
        this.id = other.id;
        this.startTimeMs = other.startTimeMs;
        this.durationMs = other.durationMs;
        this.name = other.name;
        this.actions = new ArrayList<>();
        if (other.actions != null) {
            for (PresetAction action : other.actions) {
                this.actions.add(new PresetAction(action)); // Deep copy actions
            }
        }
    }
    
    // Getters and Setters
    public long getId() { return id; }
    public void setId(long id) { this.id = id; }
    
    public int getStartTimeMs() { return startTimeMs; }
    public void setStartTimeMs(int startTimeMs) { this.startTimeMs = startTimeMs; }
    
    public int getDurationMs() { return durationMs; }
    public void setDurationMs(int durationMs) { this.durationMs = durationMs; }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public List<PresetAction> getActions() { return actions; }
    public void setActions(List<PresetAction> actions) { this.actions = actions; }
    
    // Utility methods
    public void addAction(PresetAction action) {
        this.actions.add(action);
    }
    
    public void removeAction(PresetAction action) {
        this.actions.remove(action);
    }
    
    public void removeAction(int index) {
        if (index >= 0 && index < actions.size()) {
            this.actions.remove(index);
        }
    }
    
    public boolean hasActions() {
        return actions != null && !actions.isEmpty();
    }
    
    public int getActionCount() {
        return actions != null ? actions.size() : 0;
    }
    
    public int getEndTimeMs() {
        return startTimeMs + durationMs;
    }
    
    public boolean isActiveAtTime(int timeMs) {
        return timeMs >= startTimeMs && timeMs < getEndTimeMs();
    }
    
    public boolean overlapsWithTime(int startTime, int endTime) {
        return !(getEndTimeMs() <= startTime || startTimeMs >= endTime);
    }
    
    public List<PresetAction> getActionsByType(PresetAction.ActionType type) {
        List<PresetAction> filteredActions = new ArrayList<>();
        for (PresetAction action : actions) {
            if (action.getType() == type) {
                filteredActions.add(action);
            }
        }
        return filteredActions;
    }
    
    public boolean hasActionType(PresetAction.ActionType type) {
        for (PresetAction action : actions) {
            if (action.getType() == type) {
                return true;
            }
        }
        return false;
    }
    
    public String getFormattedStartTime() {
        int seconds = startTimeMs / 1000;
        int milliseconds = startTimeMs % 1000;
        return String.format("%d.%03ds", seconds, milliseconds);
    }
    
    public String getFormattedDuration() {
        int seconds = durationMs / 1000;
        int milliseconds = durationMs % 1000;
        return String.format("%d.%03ds", seconds, milliseconds);
    }
    
    public String getFormattedTimeRange() {
        return getFormattedStartTime() + " - " + 
               String.format("%d.%03ds", getEndTimeMs() / 1000, getEndTimeMs() % 1000);
    }
    
    public PresetStep copy() {
        PresetStep copy = new PresetStep();
        copy.startTimeMs = this.startTimeMs;
        copy.durationMs = this.durationMs;
        copy.name = this.name;
        
        for (PresetAction action : this.actions) {
            copy.addAction(action.copy());
        }
        
        return copy;
    }
    
    // Create common step types
    public static PresetStep createMovementStep(int startTimeMs, int durationMs, String direction) {
        PresetStep step = new PresetStep(startTimeMs, durationMs, "Move " + direction);
        step.addAction(PresetAction.createMovementAction(direction));
        return step;
    }
    
    public static PresetStep createGestureStep(int startTimeMs, int durationMs, String gesture) {
        PresetStep step = new PresetStep(startTimeMs, durationMs, "Gesture: " + gesture);
        step.addAction(PresetAction.createGestureAction(gesture));
        return step;
    }
    
    public static PresetStep createHeadMovementStep(int startTimeMs, int durationMs, int panAngle, int tiltAngle) {
        PresetStep step = new PresetStep(startTimeMs, durationMs, "Head Movement");
        step.addAction(PresetAction.createHeadMovementAction(panAngle, tiltAngle));
        return step;
    }
    
    public static PresetStep createServoStep(int startTimeMs, int durationMs, String servoName, int angle) {
        PresetStep step = new PresetStep(startTimeMs, durationMs, "Servo: " + servoName);
        step.addAction(PresetAction.createServoAction(servoName, angle));
        return step;
    }
    
    public static PresetStep createDelayStep(int startTimeMs, int durationMs) {
        PresetStep step = new PresetStep(startTimeMs, durationMs, "Delay");
        step.addAction(PresetAction.createDelayAction(durationMs));
        return step;
    }
    
    public static PresetStep createSpeechStep(int startTimeMs, String text) {
        PresetStep step = new PresetStep(startTimeMs, 2000, "Speech"); // Default 2s for speech
        step.addAction(PresetAction.createSpeechAction(text));
        return step;
    }
    
    // Create complex multi-action steps
    public static PresetStep createComplexStep(int startTimeMs, int durationMs, String name) {
        return new PresetStep(startTimeMs, durationMs, name);
    }
    
    @Override
    public String toString() {
        return "PresetStep{" +
                "name='" + name + '\'' +
                ", startTime=" + getFormattedStartTime() +
                ", duration=" + getFormattedDuration() +
                ", actions=" + actions.size() +
                '}';
    }
}
