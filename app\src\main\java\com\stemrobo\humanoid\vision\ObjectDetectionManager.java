package com.stemrobo.humanoid.vision;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.RectF;
import android.os.Build;
import android.util.Log;

import org.tensorflow.lite.Interpreter;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.MappedByteBuffer;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.PriorityQueue;
import java.util.Vector;

import static com.stemrobo.humanoid.vision.Utils.expit;

/**
 * Production-ready YOLO Object Detection Manager
 * Based on proven TensorFlow Lite YOLO implementation
 */
public class ObjectDetectionManager {
    
    private static final String TAG = "ObjectDetectionManager";
    
    // Model configuration - Try multiple models for best compatibility
    private static final String[] MODEL_FILES = {
        "models/yolov4-416-fp32.tflite",   // YOLOv4 in models folder (best quality)
        "models/object_detection.tflite",  // Generic model in models folder
        "yolov4-416-fp32.tflite",         // Fallback YOLOv4 in root
        "object_detection.tflite"          // Fallback to root assets
    };
    private static final String[] LABELS_FILES = {
        "models/coco.txt",  // Try models folder first
        "coco.txt"          // Fallback to root assets
    };
    private static final int INPUT_SIZE = 300; // Start with smaller size for compatibility
    private static final float CONFIDENCE_THRESHOLD = 0.25f; // Even lower threshold for better detection
    private static final float NMS_THRESHOLD = 0.4f;
    
    // Model output configuration (will be determined at runtime)
    private static final int NUM_THREADS = 4;
    private static final boolean USE_NNAPI = false;
    private static final boolean USE_GPU = false; // Disable GPU for better compatibility
    
    // Runtime model info
    private String currentModelFile = null;
    private int currentInputSize = INPUT_SIZE;
    
    // Context and interpreter
    private Context context;
    private Interpreter interpreter;
    private Vector<String> labels = new Vector<>();
    private boolean isModelLoaded = false;
    private DetectionCallback callback;
    
    // Paint for drawing
    private Paint boxPaint;
    private Paint textPaint;
    
    /**
     * Detection result class
     */
    public static class Detection {
        private String id;
        private String title;
        private float confidence;
        private RectF location;
        private int detectedClass;
        
        public Detection(String id, String title, float confidence, RectF location, int detectedClass) {
            this.id = id;
            this.title = title;
            this.confidence = confidence;
            this.location = location;
            this.detectedClass = detectedClass;
        }
        
        // Getters
        public String getId() { return id; }
        public String getTitle() { return title; }
        public float getConfidence() { return confidence; }
        public RectF getLocation() { return new RectF(location); }
        public int getDetectedClass() { return detectedClass; }
        
        @Override
        public String toString() {
            return String.format("[%s] %s (%.1f%%) %s", 
                id, title, confidence * 100.0f, location.toString());
        }
    }
    
    /**
     * Callback interface for detection results
     */
    public interface DetectionCallback {
        void onObjectsDetected(List<Detection> detections, Bitmap annotatedBitmap);
        void onDetectionError(Exception error);
    }
    
    public ObjectDetectionManager(Context context) {
        this.context = context;
        initializePaints();
        initializeModel();
    }
    
    private void initializePaints() {
        // Box paint
        boxPaint = new Paint();
        boxPaint.setColor(Color.RED);
        boxPaint.setStyle(Paint.Style.STROKE);
        boxPaint.setStrokeWidth(4.0f);
        
        // Text paint
        textPaint = new Paint();
        textPaint.setColor(Color.WHITE);
        textPaint.setTextSize(24.0f);
        textPaint.setStyle(Paint.Style.FILL);
    }
    
    /**
     * Initialize object detection model and labels
     */
    private void initializeModel() {
        Log.d(TAG, "Starting model initialization...");
        try {
            // Load labels
            loadLabels();
            Log.d(TAG, "Labels loaded: " + labels.size());

            // Try loading models in order of preference
            for (String modelFile : MODEL_FILES) {
                try {
                    Log.d(TAG, "Attempting to load model: " + modelFile);
                    MappedByteBuffer modelBuffer = Utils.loadModelFile(context.getAssets(), modelFile);
                    Log.d(TAG, "Model buffer loaded, size: " + modelBuffer.capacity());

                    // Configure interpreter
                    Interpreter.Options options = new Interpreter.Options();
                    options.setNumThreads(NUM_THREADS);
                    options.setUseNNAPI(USE_NNAPI);
                    if (USE_GPU) {
                        // GPU delegate would be added here if needed
                    }

                    interpreter = new Interpreter(modelBuffer, options);
                    currentModelFile = modelFile;
                    
                    // Adjust input size based on model and validate
                    if (modelFile.contains("yolov4")) {
                        currentInputSize = 416;
                    } else {
                        currentInputSize = 300;
                    }
                    
                    // Validate model by checking input/output shapes
                    try {
                        int[] inputShape = interpreter.getInputTensor(0).shape();
                        int[] outputShape = interpreter.getOutputTensor(0).shape();
                        Log.d(TAG, "Model validation - Input shape: " + java.util.Arrays.toString(inputShape));
                        Log.d(TAG, "Model validation - Output shape: " + java.util.Arrays.toString(outputShape));
                        
                        // Adjust input size based on actual model input
                        if (inputShape.length >= 3) {
                            currentInputSize = inputShape[1]; // Assuming NHWC format
                            Log.d(TAG, "Adjusted input size to: " + currentInputSize);
                        }
                    } catch (Exception e) {
                        Log.w(TAG, "Could not validate model shapes, using default size", e);
                    }
                    
                    isModelLoaded = true;
                    Log.d(TAG, "Successfully loaded model: " + modelFile + " with input size: " + currentInputSize);
                    break;
                    
                } catch (Exception e) {
                    Log.w(TAG, "Failed to load model: " + modelFile + ", trying next...", e);
                    if (interpreter != null) {
                        interpreter.close();
                        interpreter = null;
                    }
                }
            }
            
            if (!isModelLoaded) {
                Log.e(TAG, "Failed to load any object detection model");
                // Try to use a simpler fallback approach
                trySimpleModelLoad();
            }

        } catch (Exception e) {
            Log.e(TAG, "Error during model initialization", e);
            e.printStackTrace();
            isModelLoaded = false;
        }
    }
    
    /**
     * Try a simpler model loading approach as fallback
     */
    private void trySimpleModelLoad() {
        try {
            Log.d(TAG, "Attempting simple model load...");
            
            // Try the most basic model first
            String simpleModel = "models/object_detection.tflite";
            MappedByteBuffer modelBuffer = Utils.loadModelFile(context.getAssets(), simpleModel);
            
            // Use minimal options
            Interpreter.Options options = new Interpreter.Options();
            options.setNumThreads(2); // Reduce threads
            
            interpreter = new Interpreter(modelBuffer, options);
            currentModelFile = simpleModel;
            currentInputSize = 300; // Use standard size
            isModelLoaded = true;
            
            Log.d(TAG, "Simple model load successful: " + simpleModel);
            
        } catch (Exception e) {
            Log.e(TAG, "Simple model load also failed", e);
            isModelLoaded = false;
        }
    }
    
    /**
     * Force reload the model (useful for debugging)
     */
    public void reloadModel() {
        Log.d(TAG, "Force reloading model...");
        if (interpreter != null) {
            interpreter.close();
            interpreter = null;
        }
        isModelLoaded = false;
        initializeModel();
    }
    
    /**
     * Get current model status
     */
    
    /**
     * Load class labels from assets
     */
    private void loadLabels() {
        for (String labelsFile : LABELS_FILES) {
            try {
                Log.d(TAG, "Attempting to load labels from: " + labelsFile);
                InputStream inputStream = context.getAssets().open(labelsFile);
                BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
                String line;
                labels.clear(); // Clear any previous labels
                while ((line = reader.readLine()) != null) {
                    labels.add(line.trim());
                }
                reader.close();
                Log.d(TAG, "Successfully loaded " + labels.size() + " class labels from: " + labelsFile);
                return; // Success, exit the loop
            } catch (IOException e) {
                Log.w(TAG, "Failed to load labels from: " + labelsFile + ", trying next...", e);
            }
        }
        
        // If all label files failed, create default COCO labels
        Log.w(TAG, "Failed to load any label files, using default COCO labels");
        createDefaultCocoLabels();
    }
    
    /**
     * Create default COCO labels if label files are not found
     */
    private void createDefaultCocoLabels() {
        labels.clear();
        String[] defaultLabels = {
            "person", "bicycle", "car", "motorcycle", "airplane", "bus", "train", "truck", "boat",
            "traffic light", "fire hydrant", "stop sign", "parking meter", "bench", "bird", "cat",
            "dog", "horse", "sheep", "cow", "elephant", "bear", "zebra", "giraffe", "backpack",
            "umbrella", "handbag", "tie", "suitcase", "frisbee", "skis", "snowboard", "sports ball",
            "kite", "baseball bat", "baseball glove", "skateboard", "surfboard", "tennis racket",
            "bottle", "wine glass", "cup", "fork", "knife", "spoon", "bowl", "banana", "apple",
            "sandwich", "orange", "broccoli", "carrot", "hot dog", "pizza", "donut", "cake",
            "chair", "couch", "potted plant", "bed", "dining table", "toilet", "tv", "laptop",
            "mouse", "remote", "keyboard", "cell phone", "microwave", "oven", "toaster", "sink",
            "refrigerator", "book", "clock", "vase", "scissors", "teddy bear", "hair drier", "toothbrush"
        };
        
        for (String label : defaultLabels) {
            labels.add(label);
        }
        
        Log.d(TAG, "Created " + labels.size() + " default COCO labels");
    }
    
    /**
     * Set detection callback
     */
    public void setCallback(DetectionCallback callback) {
        this.callback = callback;
    }
    
    /**
     * Detect objects in bitmap
     */
    public void detectObjects(Bitmap bitmap) {
        if (!isModelLoaded || interpreter == null) {
            Log.w(TAG, "Model not loaded (isModelLoaded=" + isModelLoaded + ", interpreter=" + (interpreter != null) + "), using demo detection");
            List<Detection> demoDetections = createDemoDetections(bitmap);
            Bitmap annotatedBitmap = drawDetections(bitmap, demoDetections);
            if (callback != null) {
                callback.onObjectsDetected(demoDetections, annotatedBitmap);
            }
            return;
        }
        
        try {
            // Preprocess image using current model's input size
            Bitmap resizedBitmap = Bitmap.createScaledBitmap(bitmap, currentInputSize, currentInputSize, true);
            ByteBuffer inputBuffer = convertBitmapToByteBuffer(resizedBitmap);
            
            // Run inference
            List<Detection> detections = runInference(inputBuffer, bitmap);
            
            // Apply Non-Maximum Suppression
            List<Detection> filteredDetections = applyNMS(detections);
            
            // Create annotated bitmap
            Bitmap annotatedBitmap = drawDetections(bitmap, filteredDetections);
            
            // Callback with results
            if (callback != null) {
                callback.onObjectsDetected(filteredDetections, annotatedBitmap);
            }
            
            Log.d(TAG, "Detected " + filteredDetections.size() + " objects");
            
        } catch (Exception e) {
            Log.e(TAG, "Error during object detection", e);
            if (callback != null) {
                callback.onDetectionError(e);
            }
        }
    }
    

    
    /**
     * Convert bitmap to ByteBuffer for model input
     */
    private ByteBuffer convertBitmapToByteBuffer(Bitmap bitmap) {
        int inputSize = currentInputSize;
        ByteBuffer byteBuffer = ByteBuffer.allocateDirect(4 * 1 * inputSize * inputSize * 3);
        byteBuffer.order(ByteOrder.nativeOrder());
        int[] intValues = new int[inputSize * inputSize];
        bitmap.getPixels(intValues, 0, bitmap.getWidth(), 0, 0, bitmap.getWidth(), bitmap.getHeight());
        int pixel = 0;
        for (int i = 0; i < inputSize; ++i) {
            for (int j = 0; j < inputSize; ++j) {
                final int val = intValues[pixel++];
                byteBuffer.putFloat(((val >> 16) & 0xFF) / 255.0f);
                byteBuffer.putFloat(((val >> 8) & 0xFF) / 255.0f);
                byteBuffer.putFloat((val & 0xFF) / 255.0f);
            }
        }
        return byteBuffer;
    }

    /**
     * Run object detection inference with adaptive model handling
     */
    private List<Detection> runInference(ByteBuffer inputBuffer, Bitmap originalBitmap) {
        List<Detection> detections = new ArrayList<>();
        
        try {
            Log.d(TAG, "Running inference with model: " + currentModelFile);
            
            // Try to determine output shape dynamically
            int[] outputShape = interpreter.getOutputTensor(0).shape();
            Log.d(TAG, "Output tensor shape: " + java.util.Arrays.toString(outputShape));
            
            // Adaptive inference based on output shape and model type
            if (currentModelFile != null && currentModelFile.contains("yolov4")) {
                Log.d(TAG, "Using YOLOv4 inference");
                detections = runYOLOv4Inference(inputBuffer, originalBitmap);
            } else if (outputShape.length == 3 && outputShape[2] > 80) {
                Log.d(TAG, "Detected YOLO-like output format, using YOLO inference");
                detections = runYOLOv4Inference(inputBuffer, originalBitmap);
            } else {
                Log.d(TAG, "Using standard inference");
                detections = runStandardInference(inputBuffer, originalBitmap);
            }
            
            Log.d(TAG, "Inference completed, found " + detections.size() + " raw detections");
            
            // If no detections found, try alternative inference method
            if (detections.isEmpty()) {
                Log.d(TAG, "No detections found, trying alternative inference method");
                if (currentModelFile != null && currentModelFile.contains("yolov4")) {
                    detections = runStandardInference(inputBuffer, originalBitmap);
                } else {
                    detections = runYOLOv4Inference(inputBuffer, originalBitmap);
                }
                Log.d(TAG, "Alternative inference found " + detections.size() + " detections");
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error during inference with model: " + currentModelFile, e);
            e.printStackTrace();
            // Fall back to demo detection
            return createDemoDetections(originalBitmap);
        }
        
        return detections;
    }
    
    /**
     * Run inference for standard object detection models
     */
    private ArrayList<Detection> runStandardInference(ByteBuffer inputBuffer, Bitmap originalBitmap) {
        ArrayList<Detection> detections = new ArrayList<>();
        
        try {
            // Get actual output shape to determine format
            int[] outputShape = interpreter.getOutputTensor(0).shape();
            Log.d(TAG, "Standard inference - output shape: " + java.util.Arrays.toString(outputShape));
            
            if (interpreter.getOutputTensorCount() > 1) {
                // Multiple outputs - try TensorFlow Object Detection API format
                Log.d(TAG, "Multiple outputs detected, using TF Object Detection API format");
                detections = runTensorFlowObjectDetectionInference(inputBuffer, originalBitmap);
            } else {
                // Single output - try different formats based on shape
                if (outputShape.length == 3) {
                    // 3D output - could be [1][N][features]
                    Log.d(TAG, "3D output detected, trying YOLO-style processing");
                    detections = runYOLOv4Inference(inputBuffer, originalBitmap);
                } else if (outputShape.length == 2) {
                    // 2D output - could be [1][features]
                    Log.d(TAG, "2D output detected, trying classification-style processing");
                    detections = runClassificationInference(inputBuffer, originalBitmap);
                } else {
                    Log.w(TAG, "Unknown output format, creating demo detections");
                    detections = createDemoDetections(originalBitmap);
                }
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Standard inference failed", e);
            e.printStackTrace();
        }
        
        return detections;
    }
    
    /**
     * Run inference for TensorFlow Object Detection API models
     */
    private ArrayList<Detection> runTensorFlowObjectDetectionInference(ByteBuffer inputBuffer, Bitmap originalBitmap) {
        ArrayList<Detection> detections = new ArrayList<>();
        
        try {
            // TF Object Detection API typically has 4 outputs:
            // 0: detection_boxes [1, max_detections, 4]
            // 1: detection_classes [1, max_detections]
            // 2: detection_scores [1, max_detections]
            // 3: num_detections [1]
            
            float[][][] outputBoxes = new float[1][100][4];      // boxes
            float[][] outputClasses = new float[1][100];        // classes
            float[][] outputScores = new float[1][100];         // scores
            float[] numDetections = new float[1];               // number of detections
            
            Map<Integer, Object> outputMap = new HashMap<>();
            outputMap.put(0, outputBoxes);
            outputMap.put(1, outputClasses);
            outputMap.put(2, outputScores);
            outputMap.put(3, numDetections);
            
            Object[] inputArray = {inputBuffer};
            interpreter.runForMultipleInputsOutputs(inputArray, outputMap);
            
            int numValidDetections = Math.min((int) numDetections[0], 100);
            Log.d(TAG, "TF Object Detection found " + numValidDetections + " detections");
            
            // Process detections
            for (int i = 0; i < numValidDetections; i++) {
                final float score = outputScores[0][i];
                if (score > CONFIDENCE_THRESHOLD) {
                    final int detectedClass = (int) outputClasses[0][i];
                    
                    // Extract bounding box coordinates (normalized)
                    final float yMin = outputBoxes[0][i][0] * originalBitmap.getHeight();
                    final float xMin = outputBoxes[0][i][1] * originalBitmap.getWidth();
                    final float yMax = outputBoxes[0][i][2] * originalBitmap.getHeight();
                    final float xMax = outputBoxes[0][i][3] * originalBitmap.getWidth();
                    
                    final RectF rectF = new RectF(
                            Math.max(0, xMin),
                            Math.max(0, yMin),
                            Math.min(originalBitmap.getWidth() - 1, xMax),
                            Math.min(originalBitmap.getHeight() - 1, yMax));
                    
                    if (detectedClass >= 0 && detectedClass < labels.size()) {
                        detections.add(new Detection("" + i, labels.get(detectedClass), score, rectF, detectedClass));
                    }
                }
            }
            
        } catch (Exception e) {
            Log.e(TAG, "TensorFlow Object Detection inference failed", e);
        }
        
        return detections;
    }
    
    /**
     * Run inference for classification-style models
     */
    private ArrayList<Detection> runClassificationInference(ByteBuffer inputBuffer, Bitmap originalBitmap) {
        ArrayList<Detection> detections = new ArrayList<>();
        
        try {
            float[][] output = new float[1][labels.size()];
            interpreter.run(inputBuffer, output);
            
            // Find top classes
            for (int i = 0; i < output[0].length && i < labels.size(); i++) {
                if (output[0][i] > CONFIDENCE_THRESHOLD) {
                    // For classification, create a detection covering the whole image
                    RectF rectF = new RectF(0, 0, originalBitmap.getWidth(), originalBitmap.getHeight());
                    detections.add(new Detection("" + i, labels.get(i), output[0][i], rectF, i));
                }
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Classification inference failed", e);
        }
        
        return detections;
    }
    
    /**
     * Run inference for YOLOv4 models
     */
    private ArrayList<Detection> runYOLOv4Inference(ByteBuffer inputBuffer, Bitmap originalBitmap) {
        ArrayList<Detection> detections = new ArrayList<>();
        
        try {
            // YOLOv4 output format: [1][10647][85] or similar
            int[] outputShape = interpreter.getOutputTensor(0).shape();
            int numDetections = outputShape[1];
            int numFeatures = outputShape[2];
            
            float[][][] output = new float[1][numDetections][numFeatures];
            interpreter.run(inputBuffer, output);
            
            // Process YOLOv4 output
            for (int i = 0; i < numDetections; i++) {
                float[] detection = output[0][i];
                
                if (detection.length >= 85) { // Standard YOLOv4 format
                    // Extract objectness score
                    float objectness = expit(detection[4]);
                    
                    if (objectness > CONFIDENCE_THRESHOLD) {
                        // Extract bounding box (center_x, center_y, width, height)
                        float centerX = expit(detection[0]);
                        float centerY = expit(detection[1]);
                        float width = (float) Math.exp(detection[2]);
                        float height = (float) Math.exp(detection[3]);
                        
                        // Convert to corner coordinates
                        float left = (centerX - width / 2) * originalBitmap.getWidth();
                        float top = (centerY - height / 2) * originalBitmap.getHeight();
                        float right = (centerX + width / 2) * originalBitmap.getWidth();
                        float bottom = (centerY + height / 2) * originalBitmap.getHeight();
                        
                        // Find best class
                        float maxClassScore = 0;
                        int bestClass = -1;
                        
                        for (int c = 0; c < Math.min(80, labels.size()); c++) { // 80 COCO classes
                            if (5 + c < detection.length) {
                                float classScore = expit(detection[5 + c]);
                                if (classScore > maxClassScore) {
                                    maxClassScore = classScore;
                                    bestClass = c;
                                }
                            }
                        }
                        
                        float finalScore = objectness * maxClassScore;
                        
                        if (finalScore > CONFIDENCE_THRESHOLD && bestClass >= 0 && bestClass < labels.size()) {
                            RectF rectF = new RectF(
                                Math.max(0, left),
                                Math.max(0, top),
                                Math.min(originalBitmap.getWidth() - 1, right),
                                Math.min(originalBitmap.getHeight() - 1, bottom)
                            );
                            
                            detections.add(new Detection(
                                "" + i, 
                                labels.get(bestClass), 
                                finalScore, 
                                rectF, 
                                bestClass
                            ));
                        }
                    }
                }
            }
            
        } catch (Exception e) {
            Log.e(TAG, "YOLOv4 inference failed", e);
        }
        
        return detections;
    }
    
    /**
     * Create demo detections for testing when models fail to load
     */
    private ArrayList<Detection> createDemoDetections(Bitmap bitmap) {
        ArrayList<Detection> detections = new ArrayList<>();
        
        int width = bitmap.getWidth();
        int height = bitmap.getHeight();
        
        Log.d(TAG, "Creating demo detections for bitmap: " + width + "x" + height);
        
        // Add multiple demo detections with actual pixel coordinates
        // Demo "person" detection in center
        RectF personRect = new RectF(
            width * 0.3f, height * 0.2f, 
            width * 0.7f, height * 0.8f
        );
        detections.add(new Detection("demo_1", "person", 0.85f, personRect, 0));
        
        // Demo "phone" detection in top-left
        RectF phoneRect = new RectF(
            width * 0.1f, height * 0.1f, 
            width * 0.3f, height * 0.4f
        );
        detections.add(new Detection("demo_2", "cell phone", 0.75f, phoneRect, 67)); // COCO class for cell phone
        
        // Demo "bottle" detection in top-right
        RectF bottleRect = new RectF(
            width * 0.7f, height * 0.1f, 
            width * 0.9f, height * 0.5f
        );
        detections.add(new Detection("demo_3", "bottle", 0.65f, bottleRect, 39)); // COCO class for bottle
        
        Log.d(TAG, "Created " + detections.size() + " demo detections (model failed to load)");
        return detections;
    }

    /**
     * Apply Non-Maximum Suppression
     */
    private List<Detection> applyNMS(List<Detection> detections) {
        ArrayList<Detection> nmsList = new ArrayList<>();

        for (int k = 0; k < labels.size(); k++) {
            // Find max confidence per class
            PriorityQueue<Detection> pq = new PriorityQueue<>(50, new Comparator<Detection>() {
                @Override
                public int compare(final Detection lhs, final Detection rhs) {
                    // Intentionally reversed to put high confidence at the head of the queue.
                    return Float.compare(rhs.getConfidence(), lhs.getConfidence());
                }
            });

            for (int i = 0; i < detections.size(); ++i) {
                if (detections.get(i).getDetectedClass() == k) {
                    pq.add(detections.get(i));
                }
            }

            // Do non maximum suppression
            while (pq.size() > 0) {
                // Insert detection with max confidence
                Detection[] a = new Detection[pq.size()];
                Detection[] detectionsArray = pq.toArray(a);
                Detection max = detectionsArray[0];
                nmsList.add(max);
                pq.clear();

                for (int j = 1; j < detectionsArray.length; j++) {
                    Detection detection = detectionsArray[j];
                    RectF b = detection.getLocation();
                    if (boxIOU(max.getLocation(), b) < NMS_THRESHOLD) {
                        pq.add(detection);
                    }
                }
            }
        }
        return nmsList;
    }

    /**
     * Calculate Intersection over Union (IoU) for two bounding boxes
     */
    private float boxIOU(RectF a, RectF b) {
        return boxIntersection(a, b) / boxUnion(a, b);
    }

    private float boxIntersection(RectF a, RectF b) {
        float w = overlap((a.left + a.right) / 2, a.right - a.left,
                (b.left + b.right) / 2, b.right - b.left);
        float h = overlap((a.top + a.bottom) / 2, a.bottom - a.top,
                (b.top + b.bottom) / 2, b.bottom - b.top);
        if (w < 0 || h < 0) return 0;
        return w * h;
    }

    private float boxUnion(RectF a, RectF b) {
        float i = boxIntersection(a, b);
        float u = (a.right - a.left) * (a.bottom - a.top) + (b.right - b.left) * (b.bottom - b.top) - i;
        return u;
    }

    private float overlap(float x1, float w1, float x2, float w2) {
        float l1 = x1 - w1 / 2;
        float l2 = x2 - w2 / 2;
        float left = l1 > l2 ? l1 : l2;
        float r1 = x1 + w1 / 2;
        float r2 = x2 + w2 / 2;
        float right = r1 < r2 ? r1 : r2;
        return right - left;
    }

    /**
     * Draw detection boxes and labels on bitmap
     */
    private Bitmap drawDetections(Bitmap bitmap, List<Detection> detections) {
        Bitmap mutableBitmap = bitmap.copy(Bitmap.Config.ARGB_8888, true);
        Canvas canvas = new Canvas(mutableBitmap);

        for (Detection detection : detections) {
            RectF box = detection.getLocation();

            // Draw bounding box
            canvas.drawRect(box, boxPaint);

            // Draw label
            String label = String.format("%s %.1f%%",
                detection.getTitle(), detection.getConfidence() * 100);
            canvas.drawText(label, box.left, box.top - 10, textPaint);
        }

        return mutableBitmap;
    }

    /**
     * Get detection statistics as formatted string
     */
    public String getDetectionStats(List<Detection> detections) {
        if (detections.isEmpty()) {
            return "No objects detected";
        }

        StringBuilder stats = new StringBuilder();
        Map<String, Integer> classCounts = new HashMap<>();

        // Count objects by class
        for (Detection detection : detections) {
            String className = detection.getTitle();
            classCounts.put(className, classCounts.getOrDefault(className, 0) + 1);
        }

        // Format results
        stats.append("Detected ").append(detections.size()).append(" objects: ");
        boolean first = true;
        for (Map.Entry<String, Integer> entry : classCounts.entrySet()) {
            if (!first) stats.append(", ");
            stats.append(entry.getValue()).append(" ").append(entry.getKey());
            if (entry.getValue() > 1) stats.append("s");
            first = false;
        }

        return stats.toString();
    }
    
    /**
     * Get model status for debugging
     */
    public String getModelStatus() {
        StringBuilder status = new StringBuilder();
        status.append("Object Detection Status:\n");
        status.append("Model loaded: ").append(isModelLoaded).append("\n");
        if (isModelLoaded && currentModelFile != null) {
            status.append("Current model: ").append(currentModelFile).append("\n");
            status.append("Input size: ").append(currentInputSize).append("\n");
            status.append("Labels loaded: ").append(labels.size()).append("\n");
            status.append("Confidence threshold: ").append(CONFIDENCE_THRESHOLD).append("\n");
        } else {
            status.append("Using demo detections (model failed to load)\n");
        }
        return status.toString();
    }
    
    /**
     * Check if model is properly loaded
     */
    public boolean isModelLoaded() {
        return isModelLoaded && interpreter != null;
    }

    /**
     * Get current model information for debugging
     */
    public String getModelInfo() {
        if (isModelLoaded && currentModelFile != null) {
            return String.format("Model: %s, Input: %dx%d, Labels: %d",
                currentModelFile, currentInputSize, currentInputSize, labels.size());
        } else {
            return "No model loaded";
        }
    }
    
    /**
     * Cleanup resources
     */
    public void cleanup() {
        if (interpreter != null) {
            interpreter.close();
            interpreter = null;
        }
        isModelLoaded = false;
        currentModelFile = null;
        Log.d(TAG, "ObjectDetectionManager cleaned up");
    }
}
