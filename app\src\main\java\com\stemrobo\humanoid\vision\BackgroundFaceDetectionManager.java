package com.stemrobo.humanoid.vision;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.ImageFormat;
import android.graphics.Rect;
import android.graphics.YuvImage;
import android.media.Image;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.camera.core.CameraSelector;
import androidx.camera.core.ImageAnalysis;
import androidx.camera.core.ImageProxy;
import androidx.camera.lifecycle.ProcessCameraProvider;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.LifecycleOwner;

import com.google.mlkit.vision.common.InputImage;
import com.google.mlkit.vision.face.Face;
import com.google.mlkit.vision.face.FaceDetection;
import com.google.mlkit.vision.face.FaceDetector;
import com.google.mlkit.vision.face.FaceDetectorOptions;

import java.io.ByteArrayOutputStream;
import java.nio.ByteBuffer;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Background Face Detection Manager
 * Performs face detection without requiring a preview view
 * Optimized for background operation and Smart Greeting functionality
 */
public class BackgroundFaceDetectionManager {
    private static final String TAG = "BackgroundFaceDetection";
    
    // Singleton instance
    private static BackgroundFaceDetectionManager instance;
    
    // Core components
    private Context context;
    private LifecycleOwner lifecycleOwner;
    private ProcessCameraProvider cameraProvider;
    private FaceDetector faceDetector;
    private ExecutorService cameraExecutor;
    
    // Camera components
    private ImageAnalysis imageAnalysis;
    private CameraSelector cameraSelector;
    
    // State
    private boolean isDetectionEnabled = false;
    private boolean isBackCamera = false;
    private FaceDetectionCallback callback;
    private int currentFaceCount = 0;
    
    // Callback interface
    public interface FaceDetectionCallback {
        void onFacesDetected(List<Face> faces, Bitmap cameraBitmap);
        void onNoFacesDetected();
        void onDetectionError(Exception error);
    }
    
    public BackgroundFaceDetectionManager(Context context, LifecycleOwner lifecycleOwner) {
        this.context = context;
        this.lifecycleOwner = lifecycleOwner;
        this.cameraExecutor = Executors.newSingleThreadExecutor();
        
        initializeFaceDetector();
    }
    
    /**
     * Get singleton instance
     */
    public static BackgroundFaceDetectionManager getInstance() {
        return instance;
    }
    
    /**
     * Set singleton instance
     */
    public static void setInstance(BackgroundFaceDetectionManager manager) {
        instance = manager;
    }
    
    /**
     * Initialize ML Kit face detector
     */
    private void initializeFaceDetector() {
        FaceDetectorOptions options = new FaceDetectorOptions.Builder()
            .setPerformanceMode(FaceDetectorOptions.PERFORMANCE_MODE_FAST)
            .setLandmarkMode(FaceDetectorOptions.LANDMARK_MODE_NONE)
            .setClassificationMode(FaceDetectorOptions.CLASSIFICATION_MODE_ALL)
            .setMinFaceSize(0.1f)
            .enableTracking()
            .build();
        
        faceDetector = FaceDetection.getClient(options);
        Log.d(TAG, "Face detector initialized");
    }
    
    /**
     * Set face detection callback
     */
    public void setCallback(FaceDetectionCallback callback) {
        this.callback = callback;
    }
    
    /**
     * Enable or disable face detection
     */
    public void setDetectionEnabled(boolean enabled) {
        this.isDetectionEnabled = enabled;
        Log.d(TAG, "Face detection " + (enabled ? "enabled" : "disabled"));
    }
    
    /**
     * Start camera for background face detection
     */
    public void startCamera(ProcessCameraProvider cameraProvider) {
        this.cameraProvider = cameraProvider;
        
        // Select front camera for face detection
        cameraSelector = CameraSelector.DEFAULT_FRONT_CAMERA;
        
        bindImageAnalysis();
        Log.d(TAG, "Background camera started for face detection");
    }
    
    /**
     * Bind image analysis for face detection (no preview needed)
     */
    private void bindImageAnalysis() {
        if (cameraProvider == null) {
            Log.e(TAG, "Camera provider not available");
            return;
        }
        
        try {
            // Unbind all use cases before rebinding
            cameraProvider.unbindAll();
            
            // Set up image analysis for face detection
            imageAnalysis = new ImageAnalysis.Builder()
                .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                .build();
            
            imageAnalysis.setAnalyzer(cameraExecutor, this::processImageProxy);
            
            // Bind only image analysis (no preview)
            cameraProvider.bindToLifecycle(lifecycleOwner, cameraSelector, imageAnalysis);
            
            Log.d(TAG, "Image analysis bound successfully for background face detection");
            
        } catch (Exception e) {
            Log.e(TAG, "Error binding image analysis", e);
            if (callback != null) {
                callback.onDetectionError(e);
            }
        }
    }
    
    /**
     * Process camera frames for face detection
     */
    private void processImageProxy(ImageProxy imageProxy) {
        try {
            if (imageProxy.getImage() == null) {
                imageProxy.close();
                return;
            }
            
            // Only process if detection is enabled
            if (!isDetectionEnabled) {
                imageProxy.close();
                return;
            }
            
            InputImage inputImage = InputImage.fromMediaImage(
                imageProxy.getImage(),
                imageProxy.getImageInfo().getRotationDegrees()
            );
            
            faceDetector.process(inputImage)
                .addOnSuccessListener(faces -> {
                    handleFaceDetectionSuccess(faces, imageProxy);
                })
                .addOnFailureListener(e -> {
                    Log.e(TAG, "Face detection failed", e);
                    if (callback != null) {
                        callback.onDetectionError(e);
                    }
                })
                .addOnCompleteListener(task -> {
                    imageProxy.close();
                });
                
        } catch (Exception e) {
            Log.e(TAG, "Error processing image proxy", e);
            imageProxy.close();
        }
    }
    
    /**
     * Handle successful face detection
     */
    private void handleFaceDetectionSuccess(List<Face> faces, ImageProxy imageProxy) {
        currentFaceCount = faces.size();
        
        // Convert image to bitmap for callback
        Bitmap cameraBitmap = null;
        try {
            cameraBitmap = convertImageProxyToBitmap(imageProxy);
        } catch (Exception e) {
            Log.w(TAG, "Could not convert image to bitmap: " + e.getMessage());
        }
        
        if (callback != null) {
            if (faces.isEmpty()) {
                callback.onNoFacesDetected();
            } else {
                callback.onFacesDetected(faces, cameraBitmap);
            }
        }
        
        Log.d(TAG, "Face detection result: " + faces.size() + " faces detected");
    }
    
    /**
     * Convert ImageProxy to Bitmap
     */
    private Bitmap convertImageProxyToBitmap(ImageProxy imageProxy) {
        try {
            Image image = imageProxy.getImage();
            if (image == null) return null;
            
            // Convert YUV_420_888 to Bitmap
            Image.Plane[] planes = image.getPlanes();
            ByteBuffer yBuffer = planes[0].getBuffer();
            ByteBuffer uBuffer = planes[1].getBuffer();
            ByteBuffer vBuffer = planes[2].getBuffer();
            
            int ySize = yBuffer.remaining();
            int uSize = uBuffer.remaining();
            int vSize = vBuffer.remaining();
            
            byte[] nv21 = new byte[ySize + uSize + vSize];
            yBuffer.get(nv21, 0, ySize);
            vBuffer.get(nv21, ySize, vSize);
            uBuffer.get(nv21, ySize + vSize, uSize);
            
            YuvImage yuvImage = new YuvImage(nv21, ImageFormat.NV21, 
                image.getWidth(), image.getHeight(), null);
            
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            yuvImage.compressToJpeg(new Rect(0, 0, image.getWidth(), image.getHeight()), 80, out);
            
            byte[] imageBytes = out.toByteArray();
            return android.graphics.BitmapFactory.decodeByteArray(imageBytes, 0, imageBytes.length);
            
        } catch (Exception e) {
            Log.e(TAG, "Error converting ImageProxy to Bitmap", e);
            return null;
        }
    }
    
    /**
     * Get current face count
     */
    public int getCurrentFaceCount() {
        return currentFaceCount;
    }
    
    /**
     * Stop camera and cleanup
     */
    public void stopCamera() {
        if (cameraProvider != null) {
            cameraProvider.unbindAll();
        }
        
        isDetectionEnabled = false;
        currentFaceCount = 0;
        
        Log.d(TAG, "Background camera stopped");
    }
    
    /**
     * Cleanup resources
     */
    public void cleanup() {
        stopCamera();
        
        if (cameraExecutor != null && !cameraExecutor.isShutdown()) {
            cameraExecutor.shutdown();
        }
        
        Log.d(TAG, "Background face detection manager cleaned up");
    }
}
