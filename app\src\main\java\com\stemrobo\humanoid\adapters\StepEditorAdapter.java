package com.stemrobo.humanoid.adapters;

import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.PopupMenu;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.stemrobo.humanoid.R;
import com.stemrobo.humanoid.models.PresetAction;
import com.stemrobo.humanoid.models.PresetStep;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Adapter for editing preset steps in the preset editor.
 * Handles step configuration, timing, and action management.
 */
public class StepEditorAdapter extends RecyclerView.Adapter<StepEditorAdapter.StepViewHolder> {
    
    private List<PresetStep> steps;
    private OnStepActionListener listener;
    
    public interface OnStepActionListener {
        void onStepUpdated(int position, PresetStep step);
        void onStepDeleted(int position);
        void onStepMoved(int fromPosition, int toPosition);
        void onStepDuplicated(int position);
    }
    
    public StepEditorAdapter(List<PresetStep> steps, OnStepActionListener listener) {
        this.steps = steps;
        this.listener = listener;
    }
    
    @NonNull
    @Override
    public StepViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_step_editor, parent, false);
        return new StepViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull StepViewHolder holder, int position) {
        PresetStep step = steps.get(position);
        holder.bind(step, position);
    }
    
    @Override
    public int getItemCount() {
        return steps.size();
    }
    
    class StepViewHolder extends RecyclerView.ViewHolder {
        private TextView textStepNumber;
        private EditText editStepName, editStartTime, editDuration;
        private Button btnStepMenu, btnAddAction;
        private RecyclerView recyclerActions;
        private LinearLayout layoutEmptyActions;
        
        private ActionEditorAdapter actionAdapter;
        private List<PresetAction> actions;
        
        public StepViewHolder(@NonNull View itemView) {
            super(itemView);
            
            textStepNumber = itemView.findViewById(R.id.text_step_number);
            editStepName = itemView.findViewById(R.id.edit_step_name);
            editStartTime = itemView.findViewById(R.id.edit_start_time);
            editDuration = itemView.findViewById(R.id.edit_duration);
            btnStepMenu = itemView.findViewById(R.id.btn_step_menu);
            btnAddAction = itemView.findViewById(R.id.btn_add_action);
            recyclerActions = itemView.findViewById(R.id.recycler_actions);
            layoutEmptyActions = itemView.findViewById(R.id.layout_empty_actions);
            
            setupActionRecyclerView();
            setupListeners();
        }
        
        private void setupActionRecyclerView() {
            actions = new ArrayList<>();
            actionAdapter = new ActionEditorAdapter(actions, new ActionEditorAdapter.OnActionChangeListener() {
                @Override
                public void onActionUpdated(int position, PresetAction action) {
                    if (position >= 0 && position < actions.size()) {
                        actions.set(position, action);
                        updateStep();
                        updateEmptyState();
                    }
                }
                
                @Override
                public void onActionDeleted(int position) {
                    if (position >= 0 && position < actions.size()) {
                        actions.remove(position);
                        actionAdapter.notifyItemRemoved(position);
                        actionAdapter.notifyItemRangeChanged(position, actions.size());
                        updateStep();
                        updateEmptyState();
                    }
                }
            });
            
            recyclerActions.setLayoutManager(new LinearLayoutManager(itemView.getContext()));
            recyclerActions.setAdapter(actionAdapter);
        }
        
        private void setupListeners() {
            // Step name change listener
            editStepName.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence s, int start, int count, int after) {}
                
                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {}
                
                @Override
                public void afterTextChanged(Editable s) {
                    updateStep();
                }
            });
            
            // Start time change listener
            editStartTime.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence s, int start, int count, int after) {}
                
                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {}
                
                @Override
                public void afterTextChanged(Editable s) {
                    updateStep();
                }
            });
            
            // Duration change listener
            editDuration.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence s, int start, int count, int after) {}
                
                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {}
                
                @Override
                public void afterTextChanged(Editable s) {
                    updateStep();
                }
            });
            
            // Step menu button
            btnStepMenu.setOnClickListener(v -> showStepMenu());
            
            // Add action button
            btnAddAction.setOnClickListener(v -> addNewAction());
        }
        
        public void bind(PresetStep step, int position) {
            textStepNumber.setText(String.valueOf(position + 1));
            editStepName.setText(step.getName());
            editStartTime.setText(String.valueOf(step.getStartTimeMs() / 1000f));
            editDuration.setText(String.valueOf(step.getDurationMs() / 1000f));

            // Load actions with deep copy to ensure proper data isolation
            actions.clear();
            if (step.getActions() != null && !step.getActions().isEmpty()) {
                for (PresetAction originalAction : step.getActions()) {
                    actions.add(new PresetAction(originalAction)); // Deep copy each action
                }
            }
            actionAdapter.notifyDataSetChanged();
            updateEmptyState();
        }
        
        private void updateStep() {
            int position = getAdapterPosition();
            if (position == RecyclerView.NO_POSITION || listener == null) {
                return;
            }
            
            PresetStep step = steps.get(position);
            step.setName(editStepName.getText().toString().trim());
            
            try {
                float startTime = Float.parseFloat(editStartTime.getText().toString());
                step.setStartTimeMs((int) (startTime * 1000));
            } catch (NumberFormatException e) {
                step.setStartTimeMs(0);
            }

            try {
                float duration = Float.parseFloat(editDuration.getText().toString());
                step.setDurationMs((int) (duration * 1000));
            } catch (NumberFormatException e) {
                step.setDurationMs(1000);
            }
            
            step.setActions(new ArrayList<>(actions));
            listener.onStepUpdated(position, step);
        }
        
        private void updateEmptyState() {
            boolean hasActions = !actions.isEmpty();
            layoutEmptyActions.setVisibility(hasActions ? View.GONE : View.VISIBLE);
            recyclerActions.setVisibility(hasActions ? View.VISIBLE : View.GONE);
        }
        
        private void showStepMenu() {
            PopupMenu popup = new PopupMenu(itemView.getContext(), btnStepMenu);
            popup.getMenuInflater().inflate(R.menu.step_menu, popup.getMenu());
            
            popup.setOnMenuItemClickListener(item -> {
                int position = getAdapterPosition();
                if (position == RecyclerView.NO_POSITION || listener == null) {
                    return false;
                }
                
                int itemId = item.getItemId();
                if (itemId == R.id.action_duplicate_step) {
                    listener.onStepDuplicated(position);
                    return true;
                } else if (itemId == R.id.action_delete_step) {
                    listener.onStepDeleted(position);
                    return true;
                }
                return false;
            });
            
            popup.show();
        }
        
        private void addNewAction() {
            PresetAction newAction = new PresetAction();
            newAction.setType(PresetAction.ActionType.DELAY);
            newAction.setDescription("New action");
            newAction.setParameters(new HashMap<>());
            
            actions.add(newAction);
            actionAdapter.notifyItemInserted(actions.size() - 1);
            updateStep();
            updateEmptyState();
        }
    }
}
