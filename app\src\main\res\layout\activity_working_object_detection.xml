<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#000000">

    <!-- Camera Preview -->
    <androidx.camera.view.PreviewView
        android:id="@+id/camera_preview"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerInParent="true" />

    <!-- Face Detection Overlay -->
    <com.stemrobo.humanoid.vision.FaceBoxOverlay
        android:id="@+id/face_box_overlay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_alignTop="@id/camera_preview"
        android:layout_alignBottom="@id/camera_preview"
        android:layout_alignStart="@id/camera_preview"
        android:layout_alignEnd="@id/camera_preview" />



    <!-- Title Bar -->
    <LinearLayout
        android:id="@+id/title_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:background="#80000000"
        android:orientation="horizontal"
        android:padding="16dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Face Detection &amp; Expression Analysis"
            android:textColor="#FFFFFF"
            android:textSize="18sp"
            android:textStyle="bold" />

        <ImageButton
            android:id="@+id/fullscreen_toggle_button"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginEnd="8dp"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:contentDescription="Toggle Full Screen"
            android:src="@android:drawable/ic_menu_crop"
            android:tint="#FFFFFF" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="LIVE"
            android:textColor="#00FF00"
            android:textSize="12sp"
            android:textStyle="bold"
            android:background="#80FF0000"
            android:padding="4dp" />

    </LinearLayout>

    <!-- Bottom Panel -->
    <LinearLayout
        android:id="@+id/bottom_panel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:orientation="vertical">

        <!-- Status Text View -->
        <TextView
            android:id="@+id/result_text_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:background="#80000000"
            android:padding="16dp"
            android:text="Initializing face detection with expression analysis..."
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            android:gravity="center"
            android:maxLines="3" />

    </LinearLayout>

    <!-- Exit Full Screen Button (Initially Hidden) -->
    <ImageButton
        android:id="@+id/exit_fullscreen_button"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:layout_margin="20dp"
        android:background="@android:drawable/btn_dialog"
        android:contentDescription="Exit Full Screen"
        android:src="@android:drawable/ic_menu_close_clear_cancel"
        android:tint="#FFFFFF"
        android:visibility="gone" />

</RelativeLayout>
