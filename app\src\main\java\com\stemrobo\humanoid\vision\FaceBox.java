package com.stemrobo.humanoid.vision;

import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.RectF;
import com.google.mlkit.vision.face.Face;

/**
 * Face detection box for drawing detected faces on overlay
 * Based on working ML Kit vision reference implementation
 */
public class FaceBox extends FaceBoxOverlay.FaceBox {
    
    private static final String TAG = "FaceBox";
    
    private final Face face;
    private final Rect imageRect;
    private final Paint boxPaint;
    private final Paint textPaint;
    private final Paint backgroundPaint;
    
    // Recognition state
    private String personName = null;
    private boolean isRecognized = false;
    private float confidence = 0.0f;

    // Expression analysis state
    private String detectedExpression = null;
    private boolean isClosestFace = false;
    
    public FaceBox(FaceBoxOverlay overlay, Face face, Rect imageRect) {
        super(overlay);
        this.face = face;
        this.imageRect = imageRect;
        
        // Initialize paints for drawing
        boxPaint = new Paint();
        boxPaint.setColor(Color.RED);
        boxPaint.setStyle(Paint.Style.STROKE);
        boxPaint.setStrokeWidth(6.0f);
        boxPaint.setAntiAlias(true);
        
        textPaint = new Paint();
        textPaint.setColor(Color.WHITE);
        textPaint.setTextSize(48.0f);
        textPaint.setAntiAlias(true);
        textPaint.setFakeBoldText(true);
        
        backgroundPaint = new Paint();
        backgroundPaint.setColor(Color.BLACK);
        backgroundPaint.setAlpha(128);
        backgroundPaint.setStyle(Paint.Style.FILL);
    }
    
    /**
     * Set recognition result for this face
     */
    public void setRecognitionResult(String personName, float confidence) {
        this.personName = personName;
        this.confidence = confidence;
        this.isRecognized = personName != null;
        
        // Update box color based on recognition
        if (isRecognized) {
            boxPaint.setColor(Color.GREEN);
        } else {
            boxPaint.setColor(Color.RED);
        }
    }
    
    /**
     * Mark as unknown person
     */
    public void setUnknownPerson() {
        this.personName = "Unknown";
        this.confidence = 0.0f;
        this.isRecognized = false;
        boxPaint.setColor(Color.YELLOW);
    }

    /**
     * Set facial expression for this face
     */
    public void setExpression(String expression) {
        this.detectedExpression = expression;
    }

    /**
     * Mark this face as the closest face for expression analysis
     */
    public void setClosestFace(boolean isClosest) {
        this.isClosestFace = isClosest;
    }

    /**
     * Analyze facial expression using ML Kit classification
     */
    public String analyzeExpression() {
        if (face.getSmilingProbability() != null) {
            float smilingProb = face.getSmilingProbability();

            if (smilingProb > 0.7f) {
                return "Happy";
            } else if (smilingProb < 0.2f) {
                // Check for other expressions using eye probabilities
                if (face.getRightEyeOpenProbability() != null && face.getLeftEyeOpenProbability() != null) {
                    float rightEyeOpen = face.getRightEyeOpenProbability();
                    float leftEyeOpen = face.getLeftEyeOpenProbability();

                    // Simple heuristic for different expressions
                    if (rightEyeOpen < 0.3f && leftEyeOpen < 0.3f) {
                        return "Sleepy"; // Eyes mostly closed
                    } else if (rightEyeOpen > 0.9f && leftEyeOpen > 0.9f && smilingProb < 0.1f) {
                        return "Surprised"; // Wide eyes, no smile
                    }
                }
                return "Sad"; // Low smile probability
            } else {
                return "Neutral"; // Medium smile probability
            }
        }
        return "Neutral";
    }

    /**
     * Get expression with confidence score
     */
    public String getExpressionWithConfidence() {
        String expression = analyzeExpression();
        if (face.getSmilingProbability() != null) {
            float confidence = face.getSmilingProbability();
            return expression + " (" + String.format("%.1f%%", confidence * 100) + ")";
        }
        return expression;
    }


    
    @Override
    public void draw(Canvas canvas) {
        if (canvas == null) return;
        
        try {
            // Calculate face bounding box
            RectF rect = getBoxRect(
                imageRect.width(),
                imageRect.height(),
                face.getBoundingBox()
            );
            
            // Draw face bounding box
            canvas.drawRect(rect, boxPaint);
            
            // Draw person name if recognized
            if (personName != null && !personName.isEmpty()) {
                drawPersonLabel(canvas, rect, personName);
            }
            
            // Draw confidence if available
            if (isRecognized && confidence > 0) {
                String confidenceText = String.format("%.1f%%", confidence * 100);
                drawConfidenceLabel(canvas, rect, confidenceText);
            }

            // Draw expression for ALL faces (not just closest)
            String expression = analyzeExpression();
            drawExpressionLabel(canvas, rect, expression);
            
        } catch (Exception e) {
            android.util.Log.e(TAG, "Error drawing face box", e);
        }
    }
    
    /**
     * Draw person name label above the face box
     */
    private void drawPersonLabel(Canvas canvas, RectF faceRect, String name) {
        // Calculate text position
        float textX = faceRect.left;
        float textY = faceRect.top - 20;
        
        // Ensure text is within canvas bounds
        if (textY < textPaint.getTextSize()) {
            textY = faceRect.bottom + textPaint.getTextSize() + 10;
        }
        
        // Measure text for background
        Rect textBounds = new Rect();
        textPaint.getTextBounds(name, 0, name.length(), textBounds);
        
        // Draw background rectangle
        float backgroundLeft = textX - 10;
        float backgroundTop = textY - textBounds.height() - 10;
        float backgroundRight = textX + textBounds.width() + 10;
        float backgroundBottom = textY + 10;
        
        canvas.drawRect(backgroundLeft, backgroundTop, backgroundRight, backgroundBottom, backgroundPaint);
        
        // Draw text
        canvas.drawText(name, textX, textY, textPaint);
    }
    
    /**
     * Draw confidence label below the person name
     */
    private void drawConfidenceLabel(Canvas canvas, RectF faceRect, String confidenceText) {
        // Smaller text for confidence
        Paint confidencePaint = new Paint(textPaint);
        confidencePaint.setTextSize(32.0f);
        confidencePaint.setColor(Color.CYAN);
        
        float textX = faceRect.left;
        float textY = faceRect.top - 70; // Above the name
        
        // Ensure text is within canvas bounds
        if (textY < confidencePaint.getTextSize()) {
            textY = faceRect.bottom + confidencePaint.getTextSize() + 50;
        }
        
        canvas.drawText(confidenceText, textX, textY, confidencePaint);
    }

    /**
     * Draw expression label above the face box
     */
    private void drawExpressionLabel(Canvas canvas, RectF faceRect, String expression) {
        // Expression text paint - reduced size for subtlety
        Paint expressionPaint = new Paint(textPaint);
        expressionPaint.setTextSize(28.0f); // Reduced from 40.0f to make it more subtle
        expressionPaint.setColor(Color.CYAN); // Changed to cyan for better visibility
        expressionPaint.setFakeBoldText(true);

        // Position expression text above the face box
        float textWidth = expressionPaint.measureText(expression);
        float textX = faceRect.centerX() - (textWidth / 2);
        float textY = faceRect.top - 15; // Position above the face box

        // Ensure text stays within canvas bounds
        if (textY < expressionPaint.getTextSize()) {
            textY = faceRect.bottom + expressionPaint.getTextSize() + 15; // Move below if no space above
        }

        // Draw background for better visibility
        Paint backgroundPaint = new Paint();
        backgroundPaint.setColor(Color.BLACK);
        backgroundPaint.setAlpha(160); // Slightly more transparent

        float padding = 8; // Reduced padding
        float textHeight = expressionPaint.getTextSize();

        canvas.drawRect(
            textX - padding,
            textY - textHeight - padding,
            textX + textWidth + padding,
            textY + padding,
            backgroundPaint
        );

        // Draw expression text
        canvas.drawText(expression, textX, textY, expressionPaint);
    }
    
    /**
     * Get the detected face
     */
    public Face getFace() {
        return face;
    }
    
    /**
     * Get person name if recognized
     */
    public String getPersonName() {
        return personName;
    }
    
    /**
     * Check if face is recognized
     */
    public boolean isRecognized() {
        return isRecognized;
    }
    
    /**
     * Get recognition confidence
     */
    public float getConfidence() {
        return confidence;
    }
    
    /**
     * Get face bounding box in image coordinates
     */
    public Rect getFaceBoundingBox() {
        return face.getBoundingBox();
    }
    
    /**
     * Get tracking ID if available
     */
    public Integer getTrackingId() {
        return face.getTrackingId();
    }

    /**
     * Get detected expression
     */
    public String getDetectedExpression() {
        return detectedExpression;
    }

    /**
     * Check if this is the closest face
     */
    public boolean isClosestFace() {
        return isClosestFace;
    }

    /**
     * Get face area for distance calculation
     */
    public float getFaceArea() {
        Rect boundingBox = face.getBoundingBox();
        return boundingBox.width() * boundingBox.height();
    }
}
