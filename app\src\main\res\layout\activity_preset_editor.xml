<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_primary">

    <!-- Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/robot_primary"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        app:title="Preset Editor"
        app:titleTextColor="@color/white"
        app:navigationIcon="@drawable/ic_arrow_back" />

    <!-- Main Content -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Preset Basic Info -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardBackgroundColor="@color/control_button_background"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Preset Information"
                        android:textColor="@color/text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="12dp" />

                    <!-- Preset Name -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        app:boxBackgroundColor="@color/background_secondary"
                        app:boxStrokeColor="@color/robot_primary">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edit_preset_name"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="Preset Name"
                            android:textColor="@color/text_primary"
                            android:textColorHint="@color/text_secondary" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Preset Description -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        app:boxBackgroundColor="@color/background_secondary"
                        app:boxStrokeColor="@color/robot_primary">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edit_preset_description"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="Description (optional)"
                            android:textColor="@color/text_primary"
                            android:textColorHint="@color/text_secondary"
                            android:maxLines="3" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Category Selection -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Category:"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp"
                        android:layout_marginBottom="8dp" />

                    <Spinner
                        android:id="@+id/spinner_preset_category"
                        android:layout_width="match_parent"
                        android:layout_height="48dp"
                        android:background="@drawable/spinner_background"
                        android:layout_marginBottom="12dp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Timeline and Steps -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardBackgroundColor="@color/control_button_background"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <!-- Timeline Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="12dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Timeline &amp; Steps"
                            android:textColor="@color/text_primary"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/text_total_duration"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Total: 0.0s"
                            android:textColor="@color/robot_accent"
                            android:textSize="12sp"
                            android:textStyle="bold"
                            android:background="@drawable/category_badge_background"
                            android:padding="6dp" />

                    </LinearLayout>

                    <!-- Timeline Visualization -->
                    <HorizontalScrollView
                        android:layout_width="match_parent"
                        android:layout_height="60dp"
                        android:layout_marginBottom="12dp">

                        <LinearLayout
                            android:id="@+id/timeline_container"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:orientation="horizontal"
                            android:background="@color/background_secondary"
                            android:padding="8dp"
                            android:minWidth="300dp" />

                    </HorizontalScrollView>

                    <!-- Steps List -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Steps"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <Button
                            android:id="@+id/btn_add_step"
                            android:layout_width="wrap_content"
                            android:layout_height="36dp"
                            android:text="+ Add Step"
                            android:textSize="12sp"
                            android:backgroundTint="@color/status_success"
                            android:textColor="@color/white" />

                    </LinearLayout>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recycler_steps"
                        android:layout_width="match_parent"
                        android:layout_height="400dp"
                        android:nestedScrollingEnabled="true"
                        android:scrollbars="vertical" />

                    <!-- Empty State -->
                    <LinearLayout
                        android:id="@+id/layout_empty_steps"
                        android:layout_width="match_parent"
                        android:layout_height="120dp"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:visibility="visible">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Steps"
                            android:textSize="24sp"
                            android:textStyle="bold"
                            android:textColor="@color/text_secondary"
                            android:layout_marginBottom="8dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="No steps added yet"
                            android:textColor="@color/text_secondary"
                            android:textSize="14sp"
                            android:layout_marginBottom="8dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Tap 'Add Step' to create your first action sequence"
                            android:textColor="@color/text_secondary"
                            android:textSize="12sp"
                            android:gravity="center" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Preview and Test -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardBackgroundColor="@color/control_button_background"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Preview &amp; Test"
                        android:textColor="@color/text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="12dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center">

                        <Button
                            android:id="@+id/btn_preview_preset"
                            android:layout_width="0dp"
                            android:layout_height="48dp"
                            android:layout_weight="1"
                            android:text="Preview"
                            android:backgroundTint="@color/robot_accent"
                            android:textColor="@color/white"
                            android:layout_marginEnd="8dp" />

                        <Button
                            android:id="@+id/btn_test_preset"
                            android:layout_width="0dp"
                            android:layout_height="48dp"
                            android:layout_weight="1"
                            android:text="Test Run"
                            android:backgroundTint="@color/status_warning"
                            android:textColor="@color/white"
                            android:layout_marginStart="8dp" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </ScrollView>

    <!-- Bottom Action Bar -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:background="@color/robot_primary"
        android:padding="12dp"
        android:elevation="8dp">

        <Button
            android:id="@+id/btn_cancel"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:text="Cancel"
            android:backgroundTint="@color/status_error"
            android:textColor="@color/white"
            android:layout_marginEnd="8dp" />

        <Button
            android:id="@+id/btn_save_preset"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:text="Save Preset"
            android:backgroundTint="@color/status_success"
            android:textColor="@color/white"
            android:layout_marginStart="8dp" />

    </LinearLayout>

</LinearLayout>
