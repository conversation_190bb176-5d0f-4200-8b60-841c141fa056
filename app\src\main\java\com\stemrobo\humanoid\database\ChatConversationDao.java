package com.stemrobo.humanoid.database;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;

import java.util.List;

/**
 * Data Access Object for ChatConversation operations
 */
@Dao
public interface ChatConversationDao {
    
    /**
     * Get all conversations ordered by last updated (most recent first)
     */
    @Query("SELECT * FROM chat_conversations ORDER BY last_updated_timestamp DESC")
    List<ChatConversation> getAllConversations();
    
    /**
     * Get conversation by ID
     */
    @Query("SELECT * FROM chat_conversations WHERE conversation_id = :conversationId")
    ChatConversation getConversationById(String conversationId);
    
    /**
     * Get the currently active conversation
     */
    @Query("SELECT * FROM chat_conversations WHERE is_active = 1 LIMIT 1")
    ChatConversation getActiveConversation();
    
    /**
     * Get recent conversations (last 30 days)
     */
    @Query("SELECT * FROM chat_conversations WHERE last_updated_timestamp > :timestamp ORDER BY last_updated_timestamp DESC")
    List<ChatConversation> getRecentConversations(long timestamp);
    
    /**
     * Get conversations by language
     */
    @Query("SELECT * FROM chat_conversations WHERE language_code = :languageCode ORDER BY last_updated_timestamp DESC")
    List<ChatConversation> getConversationsByLanguage(String languageCode);
    
    /**
     * Search conversations by title
     */
    @Query("SELECT * FROM chat_conversations WHERE title LIKE '%' || :searchQuery || '%' ORDER BY last_updated_timestamp DESC")
    List<ChatConversation> searchConversationsByTitle(String searchQuery);
    
    /**
     * Get conversation count
     */
    @Query("SELECT COUNT(*) FROM chat_conversations")
    int getConversationCount();
    
    /**
     * Insert new conversation
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertConversation(ChatConversation conversation);
    
    /**
     * Update existing conversation
     */
    @Update
    void updateConversation(ChatConversation conversation);
    
    /**
     * Delete conversation
     */
    @Delete
    void deleteConversation(ChatConversation conversation);
    
    /**
     * Delete conversation by ID
     */
    @Query("DELETE FROM chat_conversations WHERE conversation_id = :conversationId")
    void deleteConversationById(String conversationId);
    
    /**
     * Set all conversations as inactive
     */
    @Query("UPDATE chat_conversations SET is_active = 0")
    void deactivateAllConversations();
    
    /**
     * Set specific conversation as active
     */
    @Query("UPDATE chat_conversations SET is_active = 1 WHERE conversation_id = :conversationId")
    void setConversationActive(String conversationId);
    
    /**
     * Update conversation message count and timestamp
     */
    @Query("UPDATE chat_conversations SET message_count = message_count + 1, last_updated_timestamp = :timestamp WHERE conversation_id = :conversationId")
    void incrementMessageCount(String conversationId, long timestamp);
    
    /**
     * Update conversation title
     */
    @Query("UPDATE chat_conversations SET title = :title WHERE conversation_id = :conversationId")
    void updateConversationTitle(String conversationId, String title);
    
    /**
     * Delete old conversations (keep only recent ones)
     */
    @Query("DELETE FROM chat_conversations WHERE last_updated_timestamp < :cutoffTimestamp")
    void deleteOldConversations(long cutoffTimestamp);
    
    /**
     * Get conversations with message count greater than specified
     */
    @Query("SELECT * FROM chat_conversations WHERE message_count > :minMessageCount ORDER BY last_updated_timestamp DESC")
    List<ChatConversation> getConversationsWithMinMessages(int minMessageCount);
    
    /**
     * Clear all conversations (for testing/reset)
     */
    @Query("DELETE FROM chat_conversations")
    void clearAllConversations();
}
