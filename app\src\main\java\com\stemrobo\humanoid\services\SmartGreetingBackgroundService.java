package com.stemrobo.humanoid.services;

import android.app.Service;
import android.content.Intent;
import android.os.IBinder;
import android.os.Handler;
import android.os.Looper;
import android.os.Binder;
import android.graphics.Bitmap;
import java.util.List;

/**
 * Background service for Smart Greeting functionality
 * Provides continuous face detection and ultrasonic monitoring
 * when Smart Greeting is enabled, regardless of which app screen is active
 */
public class SmartGreetingBackgroundService extends Service {
    private static final String TAG = "SmartGreetingService";
    
    // Service state
    private boolean isServiceRunning = false;
    private boolean isSmartGreetingEnabled = false;
    
    // Background monitoring
    private Handler backgroundHandler;
    private Runnable faceDetectionRunnable;
    private Runnable distanceMonitoringRunnable;
    
    // Monitoring intervals
    private static final int FACE_DETECTION_INTERVAL = 500; // 500ms for face detection
    private static final int DISTANCE_MONITORING_INTERVAL = 200; // 200ms for distance updates
    
    // Current status
    private int currentFaceCount = 0;
    private float currentDistance = 999.0f;
    private boolean greetingInProgress = false;
    private long lastGreetingTime = 0;
    private static final long GREETING_COOLDOWN = 15000; // 15 seconds
    
    // Smart Greeting settings
    private float greetingDistanceThreshold = 30.0f; // 30cm for handshake range
    
    // Callback interface for status updates
    public interface SmartGreetingCallback {
        void onFaceCountUpdated(int faceCount);
        void onDistanceUpdated(float distance);
        void onGreetingTriggered(String greetingType);
        void onServiceStatusChanged(boolean isRunning);
    }
    
    private SmartGreetingCallback callback;
    
    @Override
    public void onCreate() {
        super.onCreate();
        System.out.println(TAG + ": Background service created");
        
        // Initialize background handler
        backgroundHandler = new Handler(Looper.getMainLooper());
        
        // Initialize monitoring runnables
        initializeMonitoringRunnables();
    }
    
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        if (intent != null) {
            String action = intent.getStringExtra("action");
            if ("START_SMART_GREETING".equals(action)) {
                startSmartGreetingMonitoring();
            } else if ("STOP_SMART_GREETING".equals(action)) {
                stopSmartGreetingMonitoring();
            }
        }
        
        // Return START_STICKY to restart service if killed
        return START_STICKY;
    }
    
    @Override
    public IBinder onBind(Intent intent) {
        return new SmartGreetingBinder();
    }
    
    @Override
    public void onDestroy() {
        super.onDestroy();
        stopSmartGreetingMonitoring();
        System.out.println(TAG + ": Background service destroyed");
    }
    
    /**
     * Start Smart Greeting background monitoring
     */
    public void startSmartGreetingMonitoring() {
        if (isServiceRunning) {
            System.out.println(TAG + ": Smart Greeting monitoring already running");
            return;
        }
        
        isServiceRunning = true;
        isSmartGreetingEnabled = true;
        
        System.out.println(TAG + ": Starting Smart Greeting background monitoring");
        
        // Start continuous face detection
        startBackgroundFaceDetection();
        
        // Start continuous distance monitoring
        startBackgroundDistanceMonitoring();
        
        // Notify callback
        if (callback != null) {
            callback.onServiceStatusChanged(true);
        }
        
        System.out.println(TAG + ": Smart Greeting monitoring started successfully");
    }
    
    /**
     * Stop Smart Greeting background monitoring
     */
    public void stopSmartGreetingMonitoring() {
        if (!isServiceRunning) {
            return;
        }
        
        isServiceRunning = false;
        isSmartGreetingEnabled = false;
        
        System.out.println(TAG + ": Stopping Smart Greeting background monitoring");
        
        // Stop monitoring runnables
        if (backgroundHandler != null) {
            backgroundHandler.removeCallbacks(faceDetectionRunnable);
            backgroundHandler.removeCallbacks(distanceMonitoringRunnable);
        }
        
        // Reset status
        currentFaceCount = 0;
        currentDistance = 999.0f;
        greetingInProgress = false;
        
        // Notify callback
        if (callback != null) {
            callback.onFaceCountUpdated(0);
            callback.onDistanceUpdated(999.0f);
            callback.onServiceStatusChanged(false);
        }
        
        System.out.println(TAG + ": Smart Greeting monitoring stopped");
    }
    
    /**
     * Initialize monitoring runnables
     */
    private void initializeMonitoringRunnables() {
        // Face detection runnable
        faceDetectionRunnable = new Runnable() {
            @Override
            public void run() {
                if (isServiceRunning && isSmartGreetingEnabled) {
                    performBackgroundFaceDetection();
                    
                    // Schedule next face detection
                    backgroundHandler.postDelayed(this, FACE_DETECTION_INTERVAL);
                }
            }
        };
        
        // Distance monitoring runnable
        distanceMonitoringRunnable = new Runnable() {
            @Override
            public void run() {
                if (isServiceRunning && isSmartGreetingEnabled) {
                    performBackgroundDistanceMonitoring();
                    
                    // Schedule next distance check
                    backgroundHandler.postDelayed(this, DISTANCE_MONITORING_INTERVAL);
                }
            }
        };
    }
    
    /**
     * Start background face detection
     */
    private void startBackgroundFaceDetection() {
        if (backgroundHandler != null && faceDetectionRunnable != null) {
            backgroundHandler.post(faceDetectionRunnable);
            System.out.println(TAG + ": Background face detection started");
        }
    }
    
    /**
     * Start background distance monitoring
     */
    private void startBackgroundDistanceMonitoring() {
        if (backgroundHandler != null && distanceMonitoringRunnable != null) {
            backgroundHandler.post(distanceMonitoringRunnable);
            System.out.println(TAG + ": Background distance monitoring started");
        }
    }
    
    /**
     * Perform background face detection using existing infrastructure
     */
    private void performBackgroundFaceDetection() {
        try {
            // Use existing face detection infrastructure
            int detectedFaces = getExistingFaceCount();

            if (detectedFaces != currentFaceCount) {
                currentFaceCount = detectedFaces;

                // Notify callback of face count update
                if (callback != null) {
                    callback.onFaceCountUpdated(currentFaceCount);
                }

                System.out.println(TAG + ": Face count updated: " + currentFaceCount);

                // Check if greeting should be triggered
                checkGreetingTrigger();
            }

        } catch (Exception e) {
            System.out.println(TAG + ": Error in background face detection: " + e.getMessage());
        }
    }

    /**
     * Get face count from existing face detection infrastructure
     */
    private int getExistingFaceCount() {
        try {
            // Try to get face count from VisionFragment or FaceDetectionManager
            com.stemrobo.humanoid.vision.FaceDetectionManager faceManager =
                com.stemrobo.humanoid.vision.FaceDetectionManager.getInstance();

            if (faceManager != null) {
                return faceManager.getCurrentFaceCount();
            }

            // Fallback: return 0 if no face detection available
            return 0;

        } catch (Exception e) {
            System.out.println(TAG + ": Error getting existing face count: " + e.getMessage());
            return 0;
        }
    }
    
    /**
     * Perform background distance monitoring using ESP32
     */
    private void performBackgroundDistanceMonitoring() {
        try {
            // Get real distance from ESP32CommunicationManager
            float distance = getRealDistanceReading();

            if (Math.abs(distance - currentDistance) > 1.0f) { // Only update if significant change
                currentDistance = distance;

                // Notify callback of distance update
                if (callback != null) {
                    callback.onDistanceUpdated(currentDistance);
                }

                System.out.println(TAG + ": Distance updated: " + currentDistance + "cm");

                // Check if greeting should be triggered
                checkGreetingTrigger();
            }

        } catch (Exception e) {
            System.out.println(TAG + ": Error in background distance monitoring: " + e.getMessage());
        }
    }

    /**
     * Get real distance reading from ESP32
     */
    private float getRealDistanceReading() {
        try {
            // Get ESP32 communication manager instance
            com.stemrobo.humanoid.communication.ESP32CommunicationManager commManager =
                com.stemrobo.humanoid.communication.ESP32CommunicationManager.getInstance();

            if (commManager != null) {
                // Get current distance reading
                return commManager.getCurrentDistance();
            }

            // Fallback: return large distance if no communication available
            return 999.0f;

        } catch (Exception e) {
            System.out.println(TAG + ": Error getting real distance reading: " + e.getMessage());
            return 999.0f;
        }
    }
    
    /**
     * Check if Smart Greeting should be triggered
     */
    private void checkGreetingTrigger() {
        if (!isSmartGreetingEnabled || greetingInProgress) {
            return;
        }
        
        // Check cooldown period
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastGreetingTime < GREETING_COOLDOWN) {
            return;
        }
        
        // Check greeting conditions: faces detected AND within handshake range
        if (currentFaceCount > 0 && currentDistance <= greetingDistanceThreshold) {
            triggerSmartGreeting();
        }
    }
    
    /**
     * Trigger Smart Greeting
     */
    private void triggerSmartGreeting() {
        greetingInProgress = true;
        lastGreetingTime = System.currentTimeMillis();
        
        String greetingType = "Handshake + Hi";
        
        System.out.println(TAG + ": Smart Greeting triggered! Type: " + greetingType + 
                          " (Faces: " + currentFaceCount + ", Distance: " + currentDistance + "cm)");
        
        // Notify callback
        if (callback != null) {
            callback.onGreetingTriggered(greetingType);
        }

        // Send handshake command to ESP32
        sendHandshakeCommand();

        // Reset greeting flag after delay
        backgroundHandler.postDelayed(() -> {
            greetingInProgress = false;
            System.out.println(TAG + ": Smart Greeting completed");
        }, 3000); // 3 second greeting duration
    }
    
    /**
     * Send handshake command to ESP32 and speak greeting
     */
    private void sendHandshakeCommand() {
        try {
            // Send handshake gesture to ESP32
            com.stemrobo.humanoid.communication.ESP32CommunicationManager commManager =
                com.stemrobo.humanoid.communication.ESP32CommunicationManager.getInstance();

            if (commManager != null) {
                // Send handshake greeting command
                commManager.sendHandshakeGreeting();
                System.out.println(TAG + ": Handshake command sent to ESP32");
            } else {
                System.out.println(TAG + ": ESP32 communication manager not available");
            }

            // Speak greeting using VoiceRecognitionService TTS
            speakGreeting();

        } catch (Exception e) {
            System.out.println(TAG + ": Error sending handshake command: " + e.getMessage());
        }
    }

    /**
     * Speak greeting using existing TTS infrastructure
     */
    private void speakGreeting() {
        try {
            // Send broadcast to trigger TTS greeting
            Intent greetingIntent = new Intent("com.stemrobo.humanoid.ACTION_SPEAK_GREETING");
            greetingIntent.putExtra("greeting_text", "Hi there! Nice to meet you!");

            // Send broadcast to the system
            if (getApplicationContext() != null) {
                getApplicationContext().sendBroadcast(greetingIntent);
                System.out.println(TAG + ": Greeting speech broadcast sent");
            }

        } catch (Exception e) {
            System.out.println(TAG + ": Error speaking greeting: " + e.getMessage());
        }
    }
    
    /**
     * Set callback for status updates
     */
    public void setCallback(SmartGreetingCallback callback) {
        this.callback = callback;
    }
    
    /**
     * Get current service status
     */
    public boolean isRunning() {
        return isServiceRunning;
    }
    
    /**
     * Get current face count
     */
    public int getCurrentFaceCount() {
        return currentFaceCount;
    }
    
    /**
     * Get current distance
     */
    public float getCurrentDistance() {
        return currentDistance;
    }
    
    /**
     * Update greeting distance threshold
     */
    public void setGreetingDistanceThreshold(float threshold) {
        this.greetingDistanceThreshold = threshold;
        System.out.println(TAG + ": Greeting distance threshold updated to " + threshold + "cm");
    }
    
    /**
     * Binder class for service binding
     */
    public class SmartGreetingBinder extends android.os.Binder {
        public SmartGreetingBackgroundService getService() {
            return SmartGreetingBackgroundService.this;
        }
    }
}
