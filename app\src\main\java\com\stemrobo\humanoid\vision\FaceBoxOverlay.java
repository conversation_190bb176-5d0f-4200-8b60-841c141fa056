package com.stemrobo.humanoid.vision;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.util.Log;
import android.view.View;
import java.util.ArrayList;
import java.util.List;

/**
 * Custom overlay view for drawing face detection boxes
 * Based on working ML Kit vision reference implementation
 */
public class FaceBoxOverlay extends View {
    
    private static final String TAG = "FaceBoxOverlay";
    
    private final Object lock = new Object();
    private final List<FaceBox> faceBoxes = new ArrayList<>();

    public Float mScale = null;
    public Float mOffsetX = null;
    public Float mOffsetY = null;

    // Camera information for coordinate transformation
    private boolean isFrontCamera = true;
    private int cameraImageWidth = 0;
    private int cameraImageHeight = 0;

    // Face count display
    private Paint faceCountPaint;
    private Paint faceCountBackgroundPaint;
    
    public FaceBoxOverlay(Context context, AttributeSet attrs) {
        super(context, attrs);
        initializePaints();
    }

    /**
     * Initialize paints for face count display
     */
    private void initializePaints() {
        faceCountPaint = new Paint();
        faceCountPaint.setColor(Color.WHITE);
        faceCountPaint.setTextSize(36.0f); // Reduced from 60.0f to make it less prominent
        faceCountPaint.setAntiAlias(true);
        faceCountPaint.setFakeBoldText(true);

        faceCountBackgroundPaint = new Paint();
        faceCountBackgroundPaint.setColor(Color.BLACK);
        faceCountBackgroundPaint.setAlpha(180);
        faceCountBackgroundPaint.setStyle(Paint.Style.FILL);
    }
    
    /**
     * Abstract base class for face detection graphics
     */
    public abstract static class FaceBox {
        private final FaceBoxOverlay overlay;
        
        public FaceBox(FaceBoxOverlay overlay) {
            this.overlay = overlay;
        }
        
        /**
         * Draw the face box on canvas
         */
        public abstract void draw(Canvas canvas);
        
        /**
         * Calculate the correct bounding box for face detection overlay
         * This handles coordinate transformation from camera to overlay
         * Fixed for proper alignment in both portrait and landscape orientations
         */
        protected RectF getBoxRect(float imageRectWidth, float imageRectHeight, Rect faceBoundingBox) {
            // Get overlay dimensions
            float overlayWidth = overlay.getWidth();
            float overlayHeight = overlay.getHeight();

            if (overlayWidth == 0 || overlayHeight == 0) {
                // Return a default rect if overlay not ready
                return new RectF(0, 0, 100, 100);
            }

            // Calculate scale factors - use correct width/height mapping
            float scaleX = overlayWidth / imageRectWidth;
            float scaleY = overlayHeight / imageRectHeight;

            // Use uniform scaling to maintain aspect ratio
            float scale = Math.min(scaleX, scaleY);
            overlay.mScale = scale;

            // Calculate offsets to center the scaled image
            float scaledImageWidth = imageRectWidth * scale;
            float scaledImageHeight = imageRectHeight * scale;
            float offsetX = (overlayWidth - scaledImageWidth) / 2.0f;
            float offsetY = (overlayHeight - scaledImageHeight) / 2.0f;

            overlay.mOffsetX = offsetX;
            overlay.mOffsetY = offsetY;

            // Transform face bounding box coordinates
            RectF mappedBox = new RectF();

            // Direct coordinate mapping without swapping
            mappedBox.left = faceBoundingBox.left * scale + offsetX;
            mappedBox.top = faceBoundingBox.top * scale + offsetY;
            mappedBox.right = faceBoundingBox.right * scale + offsetX;
            mappedBox.bottom = faceBoundingBox.bottom * scale + offsetY;

            // Apply mirroring for front camera (horizontal flip)
            // Only mirror if using front camera
            if (shouldMirrorCoordinates()) {
                float centerX = overlayWidth / 2.0f;
                float mirroredLeft = centerX - (mappedBox.right - centerX);
                float mirroredRight = centerX - (mappedBox.left - centerX);

                mappedBox.left = mirroredLeft;
                mappedBox.right = mirroredRight;
            }

            // Ensure coordinates are within bounds
            mappedBox.left = Math.max(0, Math.min(mappedBox.left, overlayWidth));
            mappedBox.right = Math.max(0, Math.min(mappedBox.right, overlayWidth));
            mappedBox.top = Math.max(0, Math.min(mappedBox.top, overlayHeight));
            mappedBox.bottom = Math.max(0, Math.min(mappedBox.bottom, overlayHeight));

            return mappedBox;
        }

        /**
         * Determine if coordinates should be mirrored (for front camera)
         */
        private boolean shouldMirrorCoordinates() {
            // Mirror coordinates for front camera
            return overlay.isFrontCamera;
        }
        
        protected FaceBoxOverlay getOverlay() {
            return overlay;
        }
    }
    
    /**
     * Clear all face boxes from overlay
     */
    public void clear() {
        synchronized (lock) {
            faceBoxes.clear();
        }
        postInvalidate();
    }
    
    /**
     * Add a face box to the overlay
     */
    public void add(FaceBox faceBox) {
        synchronized (lock) {
            faceBoxes.add(faceBox);
        }
        postInvalidate();
    }
    
    /**
     * Remove a specific face box
     */
    public void remove(FaceBox faceBox) {
        synchronized (lock) {
            faceBoxes.remove(faceBox);
        }
        postInvalidate();
    }
    
    /**
     * Get current number of face boxes
     */
    public int getFaceBoxCount() {
        synchronized (lock) {
            return faceBoxes.size();
        }
    }

    /**
     * Set camera information for better coordinate transformation
     */
    public void setCameraInfo(boolean isFrontCamera, int imageWidth, int imageHeight) {
        this.isFrontCamera = isFrontCamera;
        this.cameraImageWidth = imageWidth;
        this.cameraImageHeight = imageHeight;
        Log.d(TAG, "Camera info updated: " + (isFrontCamera ? "front" : "back") +
              " camera, " + imageWidth + "x" + imageHeight);
    }
    
    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        synchronized (lock) {
            // Draw all face boxes
            for (FaceBox faceBox : faceBoxes) {
                faceBox.draw(canvas);
            }

            // Draw face count
            drawFaceCount(canvas);
        }
    }

    // Face count for display
    private int currentFaceCount = 0;

    /**
     * Set face count for display
     */
    public void setFaceCount(int count) {
        this.currentFaceCount = count;
        invalidate(); // Trigger redraw
    }

    /**
     * Draw face count display in top-left corner
     */
    private void drawFaceCount(Canvas canvas) {
        if (canvas == null || faceCountPaint == null || faceCountBackgroundPaint == null) {
            return;
        }

        int faceCount = currentFaceCount > 0 ? currentFaceCount : faceBoxes.size();
        String countText = "Faces: " + faceCount;

        // Position in top-left corner with padding
        float padding = 20;
        float textX = padding;
        float textY = padding + faceCountPaint.getTextSize();

        // Measure text for background
        float textWidth = faceCountPaint.measureText(countText);
        float textHeight = faceCountPaint.getTextSize();

        // Draw background rectangle
        canvas.drawRect(
            textX - padding/2,
            textY - textHeight - padding/2,
            textX + textWidth + padding/2,
            textY + padding/2,
            faceCountBackgroundPaint
        );

        // Draw face count text
        canvas.drawText(countText, textX, textY, faceCountPaint);
    }
    
    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        super.onLayout(changed, left, top, right, bottom);
        if (changed) {
            android.util.Log.d(TAG, "Overlay layout changed: " + getWidth() + "x" + getHeight());
        }
    }
}
