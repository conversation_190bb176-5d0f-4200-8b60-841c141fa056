package com.stemrobo.humanoid.models;

import java.util.Map;

public class RobotCommand {
    private String controller;
    private String action;
    private Map<String, Object> parameters;
    private long timestamp;
    private String commandId;
    
    public RobotCommand() {
        this.timestamp = System.currentTimeMillis();
        this.commandId = generateCommandId();
    }
    
    public RobotCommand(String controller, String action, Map<String, Object> parameters) {
        this();
        this.controller = controller;
        this.action = action;
        this.parameters = parameters;
    }
    
    private String generateCommandId() {
        return "cmd_" + System.currentTimeMillis() + "_" + (int)(Math.random() * 1000);
    }
    
    // Getters and setters
    public String getController() {
        return controller;
    }
    
    public void setController(String controller) {
        this.controller = controller;
    }
    
    public String getAction() {
        return action;
    }
    
    public void setAction(String action) {
        this.action = action;
    }
    
    public Map<String, Object> getParameters() {
        return parameters;
    }
    
    public void setParameters(Map<String, Object> parameters) {
        this.parameters = parameters;
    }
    
    public long getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }
    
    public String getCommandId() {
        return commandId;
    }
    
    public void setCommandId(String commandId) {
        this.commandId = commandId;
    }
    
    @Override
    public String toString() {
        return "RobotCommand{" +
                "controller='" + controller + '\'' +
                ", action='" + action + '\'' +
                ", parameters=" + parameters +
                ", timestamp=" + timestamp +
                ", commandId='" + commandId + '\'' +
                '}';
    }
}
