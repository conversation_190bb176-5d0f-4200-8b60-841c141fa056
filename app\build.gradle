plugins {
    id 'com.android.application'
}

android {
    namespace 'com.stemrobo.humanoid'
    compileSdk 35

    defaultConfig {
        applicationId "com.stemrobo.humanoid"
        minSdk 24
        targetSdk 35
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    aaptOptions {
        noCompress "tflite"
    }
    
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    buildFeatures {
        viewBinding true
    }

    lint {
        disable "ExifInterface"
        abortOnError false
    }
}

dependencies {
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.10.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0'
    implementation 'androidx.navigation:navigation-fragment:2.7.5'
    implementation 'androidx.navigation:navigation-ui:2.7.5'
    
    // Network communication
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    implementation 'com.google.code.gson:gson:2.10.1'

    // Android support libraries
    implementation 'androidx.localbroadcastmanager:localbroadcastmanager:1.1.0'
    implementation 'androidx.core:core:1.12.0'

    // Location services
    implementation 'com.google.android.gms:play-services-location:21.0.1'

    // Core Android dependencies
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'androidx.fragment:fragment:1.6.2'
    implementation 'com.google.android.material:material:1.11.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.preference:preference:1.2.1'

    // JSON parsing (Gson)
    implementation 'com.google.code.gson:gson:2.10.1'

    // HTTP client (OkHttp)
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'

    // Additional Android core libraries
    implementation 'androidx.annotation:annotation:1.7.1'
    implementation 'androidx.lifecycle:lifecycle-common:2.7.0'

    // Room database
    implementation 'androidx.room:room-runtime:2.6.1'
    annotationProcessor 'androidx.room:room-compiler:2.6.1'

    // Voice recognition and TTS (built into Android)
    
    // Camera and ML
    implementation 'androidx.camera:camera-core:1.3.1'
    implementation 'androidx.camera:camera-camera2:1.3.1'
    implementation 'androidx.camera:camera-lifecycle:1.3.1'
    implementation 'androidx.camera:camera-view:1.3.1'
    implementation 'org.tensorflow:tensorflow-lite:2.14.0'
    implementation 'org.tensorflow:tensorflow-lite-support:0.4.4'
    implementation 'org.tensorflow:tensorflow-lite-gpu:2.14.0'
    implementation 'org.tensorflow:tensorflow-lite-task-vision:0.4.4'

    // ML Kit for Face Detection
    implementation 'com.google.mlkit:face-detection:16.1.6'

    // MediaPipe for advanced detection
    implementation 'com.google.mediapipe:tasks-vision:0.10.8'

    // Room Database for person storage
    def room_version = "2.6.1"
    implementation "androidx.room:room-runtime:$room_version"
    implementation "androidx.room:room-ktx:$room_version"
    annotationProcessor "androidx.room:room-compiler:$room_version"

    // JSON processing for embeddings
    implementation 'com.google.code.gson:gson:2.10.1'

    // Real-time data APIs
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'

    // Gemini AI (will be added in Phase 3)
    // implementation 'com.google.ai.client.generativeai:generativeai:0.1.2'
    
    // UI Components
    implementation 'com.airbnb.android:lottie:6.2.0'
    implementation 'androidx.cardview:cardview:1.0.0'

    // Video Player for LMS
    implementation 'com.google.android.exoplayer:exoplayer:2.19.1'
    implementation 'com.google.android.exoplayer:exoplayer-ui:2.19.1'

    // USB Serial communication for ESP32 - Updated to working version
    implementation 'com.github.mik3y:usb-serial-for-android:3.8.0'

    // Testing
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
}
