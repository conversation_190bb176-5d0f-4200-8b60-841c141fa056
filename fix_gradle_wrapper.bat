@echo off
echo ========================================
echo Fixing Gradle Wrapper
echo ========================================

echo Downloading gradle wrapper jar...
powershell -Command "Invoke-WebRequest -Uri 'https://github.com/gradle/gradle/raw/v8.12.0/gradle/wrapper/gradle-wrapper.jar' -OutFile 'gradle\wrapper\gradle-wrapper.jar'"

if exist "gradle\wrapper\gradle-wrapper.jar" (
    echo Gradle wrapper jar downloaded successfully!
    echo.
    echo Testing build...
    gradlew.bat assembleDebug
) else (
    echo Failed to download gradle wrapper jar
    echo Please check your internet connection
)

pause
