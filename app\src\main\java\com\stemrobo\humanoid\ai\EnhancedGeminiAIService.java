package com.stemrobo.humanoid.ai;

import android.content.Context;
import android.util.Log;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * Enhanced Gemini AI Service with real-time data integration
 * Extends the existing GeminiAIService with real-time information capabilities
 */
public class EnhancedGeminiAIService extends GeminiAIService {
    private static final String TAG = "EnhancedGeminiAI";
    
    private final SimpleRealTimeDataService realTimeDataService;
    private final Context context;
    private final ExecutorService executor;
    
    // Enhanced system prompt with real-time capabilities
    private static final String ENHANCED_SYSTEM_PROMPT =
        "My name is Guruji, an AI assistant created by STEM-Xpert company. I am designed to help with robotics, " +
        "programming, and educational content. I have access to real-time information including current time, " +
        "weather data, and news headlines. I provide responses optimized for speech synthesis without emojis, " +
        "brackets, or special characters. I keep responses short and crisp for general questions, but provide " +
        "detailed moderate-sized answers when asked specific questions. When users ask about current information " +
        "like time, weather, or news, I use my real-time data access to provide accurate, up-to-date responses. " +
        "I represent STEM-Xpert's commitment to quality STEM education and robotics innovation.";
    
    public EnhancedGeminiAIService(Context context) {
        super(); // Initialize parent GeminiAIService
        this.context = context;
        this.realTimeDataService = new SimpleRealTimeDataService(context);
        this.executor = Executors.newCachedThreadPool();
    }
    
    /**
     * Enhanced AI response with real-time data integration
     */
    public void getEnhancedAIResponse(String userInput, String languageCode, String conversationContext, AIResponseCallback callback) {
        executor.execute(() -> {
            try {
                long startTime = System.currentTimeMillis();
                
                // Check if real-time data is needed
                if (realTimeDataService.isRealTimeDataNeeded(userInput)) {
                    // Get real-time context first
                    getRealTimeContextAndRespond(userInput, languageCode, conversationContext, callback, startTime);
                } else {
                    // Use regular AI response without real-time data
                    getAIResponseWithContext(userInput, languageCode, conversationContext, new EnhancedAIResponseCallback() {
                        @Override
                        public void onSuccessWithTiming(String response, long responseTimeMs, String contextUsed) {
                            callback.onSuccess(response);
                        }
                        
                        @Override
                        public void onSuccess(String response) {
                            callback.onSuccess(response);
                        }
                        
                        @Override
                        public void onError(String error) {
                            callback.onError(error);
                        }
                    });
                }
                
            } catch (Exception e) {
                Log.e(TAG, "Error in enhanced AI response", e);
                callback.onError("I'm having trouble accessing information right now. Please try again.");
            }
        });
    }
    
    /**
     * Get real-time context and generate AI response
     */
    private void getRealTimeContextAndRespond(String userInput, String languageCode, String conversationContext, 
                                            AIResponseCallback callback, long startTime) {
        
        realTimeDataService.getRealTimeContextWithQuery(userInput, new SimpleRealTimeDataService.RealTimeDataCallback() {
            @Override
            public void onSuccess(String realTimeData) {
                // Combine conversation context with real-time data
                String enhancedContext = buildEnhancedContext(conversationContext, realTimeData);

                // Generate AI response with enhanced context
                callEnhancedGeminiAPI(userInput, languageCode, enhancedContext, new AIResponseCallback() {
                    @Override
                    public void onSuccess(String response) {
                        long responseTime = System.currentTimeMillis() - startTime;
                        Log.d(TAG, "Enhanced AI response generated in " + responseTime + "ms with real-time data");
                        callback.onSuccess(response);
                    }

                    @Override
                    public void onError(String error) {
                        // Fallback to regular AI response if enhanced fails
                        Log.w(TAG, "Enhanced AI failed, falling back to regular response: " + error);
                        getAIResponseWithContext(userInput, languageCode, conversationContext, callback);
                    }
                });
            }

            @Override
            public void onError(String error) {
                // Fallback to regular AI response if real-time data fails
                Log.w(TAG, "Real-time data failed, using regular AI response: " + error);
                getAIResponseWithContext(userInput, languageCode, conversationContext, callback);
            }
        });
    }
    
    /**
     * Build enhanced context combining conversation history and real-time data
     */
    private String buildEnhancedContext(String conversationContext, String realTimeData) {
        StringBuilder enhancedBuilder = new StringBuilder();
        
        // Add real-time data first
        enhancedBuilder.append("CURRENT REAL-TIME INFORMATION:\n");
        enhancedBuilder.append(realTimeData).append("\n\n");
        
        // Add conversation context if available
        if (conversationContext != null && !conversationContext.trim().isEmpty()) {
            enhancedBuilder.append(conversationContext).append("\n");
        }
        
        // Add instruction for using real-time data
        enhancedBuilder.append("\nIMPORTANT: Use the current real-time information above when relevant to the user's question. ");
        enhancedBuilder.append("Always provide accurate, up-to-date information when asked about time, weather, or current events.");
        
        return enhancedBuilder.toString();
    }
    
    /**
     * Enhanced Gemini API call with real-time context
     */
    private void callEnhancedGeminiAPI(String userInput, String languageCode, String enhancedContext, AIResponseCallback callback) {
        try {
            JSONObject requestJson = new JSONObject();
            JSONArray contents = new JSONArray();
            
            // Add enhanced system prompt
            JSONObject systemContent = new JSONObject();
            JSONArray systemParts = new JSONArray();
            JSONObject systemPart = new JSONObject();
            
            String systemPrompt = ENHANCED_SYSTEM_PROMPT;
            if (languageCode != null) {
                try {
                    com.stemrobo.humanoid.language.LanguageManager tempLangManager =
                        new com.stemrobo.humanoid.language.LanguageManager(null);
                    tempLangManager.setCurrentLanguage(languageCode);
                    systemPrompt = tempLangManager.getSystemPrompt() + " " + tempLangManager.getLanguageInstruction() +
                                 " I also have access to real-time information for current data.";
                } catch (Exception e) {
                    systemPrompt = ENHANCED_SYSTEM_PROMPT;
                }
            }
            
            systemPart.put("text", systemPrompt);
            systemParts.put(systemPart);
            systemContent.put("parts", systemParts);
            systemContent.put("role", "user");
            contents.put(systemContent);
            
            // Add enhanced context if available
            if (enhancedContext != null && !enhancedContext.trim().isEmpty()) {
                JSONObject contextContent = new JSONObject();
                JSONArray contextParts = new JSONArray();
                JSONObject contextPart = new JSONObject();
                contextPart.put("text", enhancedContext);
                contextParts.put(contextPart);
                contextContent.put("parts", contextParts);
                contextContent.put("role", "user");
                contents.put(contextContent);
            }
            
            // Add user input
            JSONObject userContent = new JSONObject();
            JSONArray userParts = new JSONArray();
            JSONObject userPart = new JSONObject();
            userPart.put("text", userInput);
            userParts.put(userPart);
            userContent.put("parts", userParts);
            userContent.put("role", "user");
            contents.put(userContent);
            
            requestJson.put("contents", contents);
            
            // Add generation config for better responses
            JSONObject generationConfig = new JSONObject();
            generationConfig.put("temperature", 0.7);
            generationConfig.put("topK", 40);
            generationConfig.put("topP", 0.95);
            generationConfig.put("maxOutputTokens", 1024);
            requestJson.put("generationConfig", generationConfig);
            
            // Use the parent class's API call method
            makeAPICall(requestJson, callback);
            
        } catch (JSONException e) {
            Log.e(TAG, "Error building enhanced API request", e);
            callback.onError("Error processing request with real-time data");
        }
    }
    
    /**
     * Make the actual API call using parent class infrastructure
     */
    private void makeAPICall(JSONObject requestJson, AIResponseCallback callback) {
        try {
            // Use the existing API infrastructure from parent class
            String enhancedPrompt = extractPromptFromRequest(requestJson);
            super.getAIResponse(enhancedPrompt, callback);
            
        } catch (Exception e) {
            Log.e(TAG, "Error making enhanced API call", e);
            callback.onError("Error communicating with AI service");
        }
    }
    
    /**
     * Extract prompt from JSON request for compatibility with parent class
     */
    private String extractPromptFromRequest(JSONObject requestJson) {
        try {
            StringBuilder promptBuilder = new StringBuilder();
            JSONArray contents = requestJson.getJSONArray("contents");
            
            for (int i = 0; i < contents.length(); i++) {
                JSONObject content = contents.getJSONObject(i);
                JSONArray parts = content.getJSONArray("parts");
                
                for (int j = 0; j < parts.length(); j++) {
                    JSONObject part = parts.getJSONObject(j);
                    if (part.has("text")) {
                        promptBuilder.append(part.getString("text")).append("\n");
                    }
                }
            }
            
            return promptBuilder.toString();
            
        } catch (JSONException e) {
            Log.e(TAG, "Error extracting prompt from request", e);
            return "Error processing request";
        }
    }
    
    /**
     * Get real-time data service for external access
     */
    public SimpleRealTimeDataService getRealTimeDataService() {
        return realTimeDataService;
    }

    /**
     * Check if enhanced features are available
     */
    public boolean isEnhancedModeAvailable() {
        return realTimeDataService.isConfigured();
    }

    /**
     * Get status of real-time services
     */
    public String getEnhancedModeStatus() {
        return realTimeDataService.getConfigurationStatus();
    }
}
