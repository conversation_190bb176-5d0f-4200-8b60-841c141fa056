package com.stemrobo.humanoid.models;

import java.util.HashMap;
import java.util.Map;

/**
 * Represents a single action within a preset step.
 * Actions can be movements, gestures, servo commands, delays, etc.
 */
public class PresetAction {
    private long id;
    private ActionType type;
    private Map<String, String> parameters;
    private String description;
    
    // Action types that can be executed
    public enum ActionType {
        MOVEMENT("Movement"),           // All directional movements including mecanum
        HAND_CONTROL("Hand Control"),   // Left/Right hand servo positioning
        HEAD_MOVEMENT("Head Movement"), // Head rotation and positioning
        GESTURE("Gesture"),            // Predefined gesture sequences
        SPEECH("Speech"),              // Text-to-speech with voice options
        DELAY("Delay"),                // Wait/pause with precise timing
        STOP("Stop"),                  // Stop all movements
        CUSTOM_COMMAND("Custom");      // Custom ESP32 command
        
        private final String displayName;
        
        ActionType(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
    }
    
    // Parameter keys for different action types
    public static class ParameterKeys {
        // Movement parameters
        public static final String DIRECTION = "direction";
        public static final String SPEED = "speed";
        public static final String DISTANCE = "distance";
        public static final String DURATION = "duration";

        // Hand control parameters
        public static final String HAND_SIDE = "hand_side";        // "left" or "right"
        public static final String HAND_POSITION = "hand_position"; // "rest", "up", "forward", "side", "custom"
        public static final String ANGLE = "angle";                // Custom angle (0-180)
        public static final String SERVO_NAME = "servo_name";      // Legacy support

        // Head movement parameters
        public static final String HEAD_ACTION = "head_action";    // "turn", "tilt", "center", "look"
        public static final String HEAD_DIRECTION = "head_direction"; // "left", "right", "up", "down", "center"
        public static final String HEAD_ANGLE = "head_angle";      // Specific angle
        public static final String PAN_ANGLE = "pan_angle";        // Legacy support
        public static final String TILT_ANGLE = "tilt_angle";      // Legacy support
        public static final String HEAD_COMMAND = "head_command";  // Legacy support

        // Speech parameters
        public static final String TEXT = "text";
        public static final String VOICE_SPEED = "voice_speed";
        public static final String VOICE_PITCH = "voice_pitch";
        public static final String VOICE_GENDER = "voice_gender";  // "male" or "female"
        public static final String LANGUAGE = "language";          // Legacy support

        // Gesture parameters
        public static final String GESTURE_TYPE = "gesture_type";
        public static final String GESTURE_INTENSITY = "gesture_intensity"; // "subtle", "normal", "dramatic"

        // Delay parameters
        public static final String DELAY_MS = "delay_ms";

        // Custom command parameters
        public static final String COMMAND = "command";
        public static final String COMMAND_PARAMS = "command_params";
    }
    
    public PresetAction() {
        this.parameters = new HashMap<>();
    }
    
    public PresetAction(ActionType type) {
        this();
        this.type = type;
    }
    
    public PresetAction(ActionType type, String description) {
        this(type);
        this.description = description;
    }

    // Copy constructor for deep copying
    public PresetAction(PresetAction other) {
        this.id = other.id;
        this.type = other.type;
        this.description = other.description;
        this.parameters = new HashMap<>();
        if (other.parameters != null) {
            this.parameters.putAll(other.parameters);
        }
    }
    
    // Getters and Setters
    public long getId() { return id; }
    public void setId(long id) { this.id = id; }
    
    public ActionType getType() { return type; }
    public void setType(ActionType type) { this.type = type; }
    
    public Map<String, String> getParameters() { return parameters; }
    public void setParameters(Map<String, String> parameters) { this.parameters = parameters; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    // Parameter utility methods
    public void setParameter(String key, String value) {
        this.parameters.put(key, value);
    }
    
    public void setParameter(String key, int value) {
        this.parameters.put(key, String.valueOf(value));
    }
    
    public String getParameter(String key) {
        return parameters.get(key);
    }
    
    public String getParameter(String key, String defaultValue) {
        return parameters.getOrDefault(key, defaultValue);
    }
    
    public int getParameterAsInt(String key, int defaultValue) {
        try {
            String value = parameters.get(key);
            return value != null ? Integer.parseInt(value) : defaultValue;
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }
    
    public boolean hasParameter(String key) {
        return parameters.containsKey(key);
    }
    
    public PresetAction copy() {
        PresetAction copy = new PresetAction();
        copy.type = this.type;
        copy.description = this.description;
        copy.parameters = new HashMap<>(this.parameters);
        return copy;
    }
    
    // Factory methods for creating common actions
    public static PresetAction createMovementAction(String direction) {
        PresetAction action = new PresetAction(ActionType.MOVEMENT, "Move " + direction);
        action.setParameter(ParameterKeys.DIRECTION, direction);
        return action;
    }
    
    public static PresetAction createGestureAction(String gestureType) {
        PresetAction action = new PresetAction(ActionType.GESTURE, "Gesture: " + gestureType);
        action.setParameter(ParameterKeys.GESTURE_TYPE, gestureType);
        return action;
    }
    
    public static PresetAction createServoAction(String servoName, int angle) {
        PresetAction action = new PresetAction(ActionType.HAND_CONTROL,
                                             servoName + " to " + angle + "°");
        action.setParameter(ParameterKeys.SERVO_NAME, servoName);
        action.setParameter(ParameterKeys.ANGLE, angle);
        return action;
    }
    
    public static PresetAction createHeadMovementAction(int panAngle, int tiltAngle) {
        PresetAction action = new PresetAction(ActionType.HEAD_MOVEMENT, 
                                             "Head: Pan " + panAngle + "°, Tilt " + tiltAngle + "°");
        action.setParameter(ParameterKeys.PAN_ANGLE, panAngle);
        action.setParameter(ParameterKeys.TILT_ANGLE, tiltAngle);
        return action;
    }
    
    public static PresetAction createHeadCommandAction(String command) {
        PresetAction action = new PresetAction(ActionType.HEAD_MOVEMENT, "Head: " + command);
        action.setParameter(ParameterKeys.HEAD_COMMAND, command);
        return action;
    }
    
    public static PresetAction createSpeechAction(String text) {
        PresetAction action = new PresetAction(ActionType.SPEECH, "Say: " + text);
        action.setParameter(ParameterKeys.TEXT, text);
        return action;
    }
    
    public static PresetAction createDelayAction(int delayMs) {
        PresetAction action = new PresetAction(ActionType.DELAY, "Wait " + delayMs + "ms");
        action.setParameter(ParameterKeys.DELAY_MS, delayMs);
        return action;
    }
    
    public static PresetAction createStopAction() {
        return new PresetAction(ActionType.STOP, "Stop all movements");
    }
    
    public static PresetAction createCustomCommandAction(String command, String description) {
        PresetAction action = new PresetAction(ActionType.CUSTOM_COMMAND, description);
        action.setParameter(ParameterKeys.COMMAND, command);
        return action;
    }
    
    // Convert action to ESP32 command
    public String toESP32Command() {
        switch (type) {
            case MOVEMENT:
                return convertMovementToCommand();
            case GESTURE:
                return getParameter(ParameterKeys.GESTURE_TYPE, "REST");
            case HAND_CONTROL:
                return convertServoToCommand();
            case HEAD_MOVEMENT:
                return convertHeadToCommand();
            case STOP:
                return "S";
            case CUSTOM_COMMAND:
                return getParameter(ParameterKeys.COMMAND, "S");
            default:
                return null; // Speech and Delay don't have ESP32 commands
        }
    }
    
    private String convertMovementToCommand() {
        String direction = getParameter(ParameterKeys.DIRECTION, "stop");
        switch (direction.toLowerCase()) {
            case "forward": return "F";
            case "backward": return "B";
            case "turn_left": return "L";
            case "turn_right": return "R";
            case "slide_left": return "SL";
            case "slide_right": return "SR";
            case "diagonal_front_left": return "DFL";
            case "diagonal_front_right": return "DFR";
            case "diagonal_back_left": return "DBL";
            case "diagonal_back_right": return "DBR";
            case "rotate_left": return "ROT_L";
            case "rotate_right": return "ROT_R";
            default: return "S";
        }
    }
    
    private String convertServoToCommand() {
        String servoName = getParameter(ParameterKeys.SERVO_NAME, "");
        int angle = getParameterAsInt(ParameterKeys.ANGLE, 0);
        
        switch (servoName.toLowerCase()) {
            case "left_arm":
            case "la":
                return "LA" + angle;
            case "right_arm":
            case "ra":
                return "RA" + angle;
            default:
                return "REST";
        }
    }
    
    private String convertHeadToCommand() {
        if (hasParameter(ParameterKeys.HEAD_COMMAND)) {
            return getParameter(ParameterKeys.HEAD_COMMAND);
        }
        
        int panAngle = getParameterAsInt(ParameterKeys.PAN_ANGLE, 90);
        int tiltAngle = getParameterAsInt(ParameterKeys.TILT_ANGLE, 90);
        
        return "HP" + panAngle + ",HT" + tiltAngle;
    }
    
    @Override
    public String toString() {
        return "PresetAction{" +
                "type=" + type +
                ", description='" + description + '\'' +
                ", parameters=" + parameters +
                '}';
    }
}
