package com.stemrobo.humanoid.activities;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.WindowManager;
import android.widget.ImageButton;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.google.android.exoplayer2.ExoPlayer;
import com.google.android.exoplayer2.MediaItem;
import com.google.android.exoplayer2.PlaybackException;
import com.google.android.exoplayer2.Player;
import com.google.android.exoplayer2.ui.StyledPlayerView;
import com.stemrobo.humanoid.R;

/**
 * Activity for playing local LMS introduction video in fullscreen
 */
public class LMSVideoPlayerActivity extends AppCompatActivity {
    private static final String TAG = "LMSVideoPlayer";
    
    public static final String EXTRA_VIDEO_PATH = "video_path";
    public static final String EXTRA_VIDEO_TITLE = "video_title";
    
    private ExoPlayer exoPlayer;
    private StyledPlayerView playerView;
    private ImageButton closeButton;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // Enable fullscreen mode
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN,
                WindowManager.LayoutParams.FLAG_FULLSCREEN);
        
        // Hide action bar
        if (getSupportActionBar() != null) {
            getSupportActionBar().hide();
        }
        
        setContentView(R.layout.activity_lms_video_player);
        
        initializeViews();
        setupPlayer();
        
        // Get video path from intent
        String videoPath = getIntent().getStringExtra(EXTRA_VIDEO_PATH);
        String videoTitle = getIntent().getStringExtra(EXTRA_VIDEO_TITLE);
        
        if (videoPath != null) {
            playVideo(videoPath);
        } else {
            // Default to LMS intro video
            playVideo("LMS-intro.mp4");
        }
        
        Log.d(TAG, "LMS Video Player started for: " + (videoTitle != null ? videoTitle : "LMS Introduction"));
    }
    
    private void initializeViews() {
        playerView = findViewById(R.id.player_view);
        closeButton = findViewById(R.id.close_button);
        
        // Setup close button
        closeButton.setOnClickListener(v -> {
            Log.d(TAG, "Close button clicked");
            finish();
        });
        
        // Hide system UI for immersive experience
        hideSystemUI();
    }
    
    private void setupPlayer() {
        // Create ExoPlayer instance
        exoPlayer = new ExoPlayer.Builder(this).build();
        
        // Bind player to view
        playerView.setPlayer(exoPlayer);
        
        // Setup player listener
        exoPlayer.addListener(new Player.Listener() {
            @Override
            public void onPlaybackStateChanged(int playbackState) {
                switch (playbackState) {
                    case Player.STATE_READY:
                        Log.d(TAG, "Video ready to play");
                        break;
                    case Player.STATE_ENDED:
                        Log.d(TAG, "Video playback ended");
                        finish(); // Close activity when video ends
                        break;
                    case Player.STATE_BUFFERING:
                        Log.d(TAG, "Video buffering");
                        break;
                }
            }
            
            @Override
            public void onPlayerError(PlaybackException error) {
                Log.e(TAG, "Video playback error: " + error.getMessage());
                Toast.makeText(LMSVideoPlayerActivity.this, 
                    "Error playing video", Toast.LENGTH_SHORT).show();
                finish();
            }
        });
        
        // Auto-play when ready
        exoPlayer.setPlayWhenReady(true);
    }
    
    private void playVideo(String videoPath) {
        try {
            // Create URI for asset video
            Uri videoUri = Uri.parse("asset:///" + videoPath);
            
            // Create media item
            MediaItem mediaItem = MediaItem.fromUri(videoUri);
            
            // Set media item and prepare
            exoPlayer.setMediaItem(mediaItem);
            exoPlayer.prepare();
            
            Log.d(TAG, "Playing video: " + videoPath);
            
        } catch (Exception e) {
            Log.e(TAG, "Error setting up video playback", e);
            Toast.makeText(this, "Error loading video", Toast.LENGTH_SHORT).show();
            finish();
        }
    }
    
    private void hideSystemUI() {
        // Enable immersive fullscreen mode
        View decorView = getWindow().getDecorView();
        decorView.setSystemUiVisibility(
            View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
            | View.SYSTEM_UI_FLAG_LAYOUT_STABLE
            | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
            | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
            | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
            | View.SYSTEM_UI_FLAG_FULLSCREEN
        );
    }
    
    @Override
    protected void onStart() {
        super.onStart();
        if (exoPlayer != null) {
            exoPlayer.setPlayWhenReady(true);
        }
    }
    
    @Override
    protected void onStop() {
        super.onStop();
        if (exoPlayer != null) {
            exoPlayer.setPlayWhenReady(false);
        }
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (exoPlayer != null) {
            exoPlayer.release();
            exoPlayer = null;
        }
        Log.d(TAG, "LMS Video Player destroyed");
    }
    
    @Override
    public void onBackPressed() {
        // Allow back button to close video
        super.onBackPressed();
    }
}
