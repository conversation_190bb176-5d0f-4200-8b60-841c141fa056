package com.stemrobo.humanoid.models;

import java.util.Map;

public class RobotR<PERSON>ponse {
    private String controller;
    private String status;
    private String message;
    private Map<String, Object> data;
    private long timestamp;
    private String commandId;
    private boolean success;
    
    public RobotResponse() {
        this.timestamp = System.currentTimeMillis();
    }
    
    public RobotResponse(String controller, String status, String message) {
        this();
        this.controller = controller;
        this.status = status;
        this.message = message;
        this.success = "success".equalsIgnoreCase(status);
    }
    
    // Getters and setters
    public String getController() {
        return controller;
    }
    
    public void setController(String controller) {
        this.controller = controller;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
        this.success = "success".equalsIgnoreCase(status);
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public Map<String, Object> getData() {
        return data;
    }
    
    public void setData(Map<String, Object> data) {
        this.data = data;
    }
    
    public long getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }
    
    public String getCommandId() {
        return commandId;
    }
    
    public void setCommandId(String commandId) {
        this.commandId = commandId;
    }
    
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    @Override
    public String toString() {
        return "RobotResponse{" +
                "controller='" + controller + '\'' +
                ", status='" + status + '\'' +
                ", message='" + message + '\'' +
                ", data=" + data +
                ", timestamp=" + timestamp +
                ", commandId='" + commandId + '\'' +
                ", success=" + success +
                '}';
    }
}
