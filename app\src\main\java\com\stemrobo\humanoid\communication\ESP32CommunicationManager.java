package com.stemrobo.humanoid.communication;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.content.SharedPreferences;
import android.hardware.usb.UsbDevice;
import android.hardware.usb.UsbDeviceConnection;
import android.hardware.usb.UsbManager;
import android.net.wifi.WifiManager;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.util.Log;
import android.speech.tts.TextToSpeech;
import android.preference.PreferenceManager;
import java.util.Locale;

import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import com.stemrobo.humanoid.models.RobotCommand;
import com.stemrobo.humanoid.models.RobotResponse;
import com.stemrobo.humanoid.services.USBSerialService;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class ESP32CommunicationManager {
    private static final String TAG = "ESP32CommManager";
    private static ESP32CommunicationManager instance;

    // USB Serial Service for direct ESP32 communication
    private USBSerialService usbSerialService;
    private boolean isServiceBound = false;

    // Default ESP32 IP addresses (can be overridden from settings)
    private static final String DEFAULT_MOTOR_CONTROLLER_IP = "***********";
    private static final String DEFAULT_SERVO_CONTROLLER_IP = "***********";
    private static final String DEFAULT_SENSOR_CONTROLLER_IP = "***********";

    // Dynamic IP addresses from settings
    private String motorControllerIP = DEFAULT_MOTOR_CONTROLLER_IP;
    private String servoControllerIP = DEFAULT_SERVO_CONTROLLER_IP;
    private String sensorControllerIP = DEFAULT_SENSOR_CONTROLLER_IP;

    private static final int ESP32_PORT = 80;
    private static final MediaType JSON = MediaType.get("application/json; charset=utf-8");

    private Context context;
    private OkHttpClient httpClient;
    private Gson gson;
    private ExecutorService executorService;
    private WifiManager wifiManager;
    private UsbManager usbManager;
    private SharedPreferences sharedPreferences;

    // Communication mode tracking
    public enum CommunicationMode {
        USB_UART,
        WIFI
    }

    private CommunicationMode currentMode = CommunicationMode.WIFI; // Default to WiFi
    private boolean usbConnected = false;
    private UsbDevice esp32Device = null;
    private UsbDeviceConnection usbConnection = null;

    // Connection status tracking
    private Map<String, Boolean> connectionStatus;
    private Map<String, Long> lastHeartbeat;

    // ENHANCED COMMUNICATION RESILIENCE
    private final java.util.concurrent.BlockingQueue<QueuedCommand> commandQueue = new java.util.concurrent.LinkedBlockingQueue<>();
    private final java.util.concurrent.ConcurrentHashMap<String, Integer> commandRetryCount = new java.util.concurrent.ConcurrentHashMap<>();
    private final java.util.concurrent.ConcurrentHashMap<String, Long> lastCommandTime = new java.util.concurrent.ConcurrentHashMap<>();
    private volatile boolean isProcessingQueue = false;
    private static final int MAX_RETRY_ATTEMPTS = 3;
    private static final long COMMAND_TIMEOUT_MS = 5000; // 5 seconds
    private static final long RETRY_DELAY_BASE_MS = 1000; // 1 second base delay

    // Smart Greeting continuous monitoring
    private boolean isContinuousMonitoringActive = false;
    private Handler continuousMonitoringHandler;
    private Runnable distanceMonitoringRunnable;
    private static final int CONTINUOUS_MONITORING_INTERVAL = 200; // 200ms for real-time updates

    // Text-to-Speech for Smart Greeting
    private TextToSpeech textToSpeech;
    private boolean isTTSInitialized = false;

    // Distance streaming callback
    public interface DistanceUpdateCallback {
        void onDistanceUpdated(float distance);
        void onDistanceStreamingStatusChanged(boolean isActive);
    }

    private DistanceUpdateCallback distanceCallback;

    // COMMAND QUEUE PROCESSING CLASSES
    private class QueuedCommand {
        final String commandId;
        final String action;
        final String controllerId;
        final Map<String, Object> parameters;
        final long timestamp;
        final int priority; // 0 = highest, 10 = lowest
        final CommandType type;

        enum CommandType {
            MOTOR, SERVO, SENSOR, GREETING
        }

        QueuedCommand(String commandId, String action, String controllerId, Map<String, Object> parameters, int priority, CommandType type) {
            this.commandId = commandId;
            this.action = action;
            this.controllerId = controllerId;
            this.parameters = parameters;
            this.timestamp = System.currentTimeMillis();
            this.priority = priority;
            this.type = type;
        }

        boolean isExpired() {
            return (System.currentTimeMillis() - timestamp) > COMMAND_TIMEOUT_MS;
        }
    }
    private Map<String, String> connectionModeStatus; // Track connection mode per controller
    
    // Communication listeners
    public interface CommunicationListener {
        void onCommandSent(String controllerId, RobotCommand command);
        void onResponseReceived(String controllerId, RobotResponse response);
        void onConnectionStatusChanged(String controllerId, boolean connected);
        void onError(String controllerId, String error);
    }
    
    private CommunicationListener listener;

    // Service connection for USB Serial Service
    private ServiceConnection serviceConnection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            Log.d(TAG, "USB Serial Service connected");
            USBSerialService.SerialBinder binder = (USBSerialService.SerialBinder) service;
            usbSerialService = binder.getService();
            isServiceBound = true;
            setupUSBSerialListener();

            // Scan for devices after service connection
            if (usbSerialService != null) {
                usbSerialService.scanForDevices();
            }
        }

        @Override
        public void onServiceDisconnected(ComponentName name) {
            Log.d(TAG, "USB Serial Service disconnected");
            usbSerialService = null;
            isServiceBound = false;
            usbConnected = false;
            currentMode = CommunicationMode.WIFI;
        }
    };

    private ESP32CommunicationManager() {
        httpClient = new OkHttpClient.Builder()
            .connectTimeout(5, java.util.concurrent.TimeUnit.SECONDS)
            .readTimeout(10, java.util.concurrent.TimeUnit.SECONDS)
            .writeTimeout(10, java.util.concurrent.TimeUnit.SECONDS)
            .build();
            
        gson = new Gson();
        executorService = Executors.newFixedThreadPool(3);
        connectionStatus = new HashMap<>();
        lastHeartbeat = new HashMap<>();
        connectionModeStatus = new HashMap<>();

        // Initialize connection status
        connectionStatus.put("motor", false);
        connectionStatus.put("servo", false);
        connectionStatus.put("sensor", false);

        // Initialize connection mode status
        connectionModeStatus.put("motor", "wifi");
        connectionModeStatus.put("servo", "wifi");
        connectionModeStatus.put("sensor", "wifi");
    }
    
    public static synchronized ESP32CommunicationManager getInstance() {
        if (instance == null) {
            instance = new ESP32CommunicationManager();
        }
        return instance;
    }

    /**
     * ENHANCED COMMAND QUEUE PROCESSING METHODS
     */

    /**
     * Start command queue processing
     */
    private void startCommandQueueProcessing() {
        if (isProcessingQueue) {
            return;
        }

        isProcessingQueue = true;
        executorService.execute(() -> {
            android.util.Log.d(TAG, "Command queue processing started");

            while (isProcessingQueue && !Thread.currentThread().isInterrupted()) {
                try {
                    // Wait for commands with timeout
                    QueuedCommand command = commandQueue.poll(1, java.util.concurrent.TimeUnit.SECONDS);

                    if (command != null) {
                        if (command.isExpired()) {
                            android.util.Log.w(TAG, "Command expired: " + command.commandId);
                            continue;
                        }

                        processQueuedCommand(command);
                    }

                    // Clean up old retry counts
                    cleanupOldRetryData();

                } catch (InterruptedException e) {
                    android.util.Log.d(TAG, "Command queue processing interrupted");
                    Thread.currentThread().interrupt();
                    break;
                } catch (Exception e) {
                    android.util.Log.e(TAG, "Error in command queue processing", e);
                }
            }

            android.util.Log.d(TAG, "Command queue processing stopped");
        });
    }

    /**
     * Process a queued command with retry logic
     */
    private void processQueuedCommand(QueuedCommand command) {
        String retryKey = command.commandId;
        int currentRetries = commandRetryCount.getOrDefault(retryKey, 0);

        if (currentRetries >= MAX_RETRY_ATTEMPTS) {
            android.util.Log.e(TAG, "Command failed after " + MAX_RETRY_ATTEMPTS + " attempts: " + command.commandId);
            commandRetryCount.remove(retryKey);
            return;
        }

        try {
            boolean success = executeCommandDirectly(command);

            if (success) {
                android.util.Log.d(TAG, "Command executed successfully: " + command.commandId);
                commandRetryCount.remove(retryKey);
                lastCommandTime.put(retryKey, System.currentTimeMillis());
            } else {
                // Retry with exponential backoff
                int newRetryCount = currentRetries + 1;
                commandRetryCount.put(retryKey, newRetryCount);

                long delay = RETRY_DELAY_BASE_MS * (long) Math.pow(2, currentRetries);
                android.util.Log.w(TAG, "Command failed, retrying in " + delay + "ms (attempt " + newRetryCount + "): " + command.commandId);

                // Re-queue with delay
                new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(() -> {
                    commandQueue.offer(command);
                }, delay);
            }

        } catch (Exception e) {
            android.util.Log.e(TAG, "Exception executing command: " + command.commandId, e);

            // Retry on exception
            int newRetryCount = currentRetries + 1;
            commandRetryCount.put(retryKey, newRetryCount);

            if (newRetryCount < MAX_RETRY_ATTEMPTS) {
                long delay = RETRY_DELAY_BASE_MS * (long) Math.pow(2, currentRetries);
                new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(() -> {
                    commandQueue.offer(command);
                }, delay);
            }
        }
    }

    /**
     * Execute command directly without queuing
     */
    private boolean executeCommandDirectly(QueuedCommand command) {
        try {
            switch (command.type) {
                case MOTOR:
                    sendMotorCommand(command.action);
                    return true;
                case SERVO:
                    sendServoCommand(command.action);
                    return true;
                case SENSOR:
                    sendSensorCommand(command.action, command.parameters);
                    return true;
                case GREETING:
                    sendSensorCommand(command.action, null);
                    return true;
                default:
                    return false;
            }
        } catch (Exception e) {
            android.util.Log.e(TAG, "Error executing command directly: " + command.action, e);
            return false;
        }
    }

    /**
     * Clean up old retry data
     */
    private void cleanupOldRetryData() {
        long currentTime = System.currentTimeMillis();
        long maxAge = COMMAND_TIMEOUT_MS * 2; // Keep data for 2x timeout period

        lastCommandTime.entrySet().removeIf(entry ->
            (currentTime - entry.getValue()) > maxAge);
    }

    public void initialize(Context context) {
        this.context = context;
        this.wifiManager = (WifiManager) context.getSystemService(Context.WIFI_SERVICE);
        this.usbManager = (UsbManager) context.getSystemService(Context.USB_SERVICE);
        this.sharedPreferences = context.getSharedPreferences("robot_settings", Context.MODE_PRIVATE);

        // Initialize TTS for Smart Greeting
        initializeTTS();

        // Initialize USB Serial Service with enhanced safety and crash prevention
        System.out.println(TAG + ": === ENABLING USB SERIAL WITH CRASH PREVENTION ===");
        System.out.println(TAG + ": USB Serial Service enabled with enhanced safety measures");
        System.out.println(TAG + ": Priority: USB Serial > WiFi (with safe fallback)");

        try {
            // Delay USB service initialization to ensure smooth startup
            new Handler(Looper.getMainLooper()).postDelayed(() -> {
                try {
                    Log.d(TAG, "Starting USB Serial Service with safety checks...");
                    Intent serviceIntent = new Intent(context, USBSerialService.class);

                    // Enhanced error handling for service startup
                    boolean serviceStarted = false;
                    try {
                        context.startForegroundService(serviceIntent);
                        serviceStarted = true;
                        Log.d(TAG, "USB Serial Service started successfully");
                    } catch (Exception serviceException) {
                        Log.w(TAG, "Failed to start USB Serial Service: " + serviceException.getMessage());
                        serviceStarted = false;
                    }

                    if (serviceStarted) {
                        try {
                            context.bindService(serviceIntent, serviceConnection, Context.BIND_AUTO_CREATE);
                            Log.d(TAG, "USB Serial Service binding initiated");
                        } catch (Exception bindException) {
                            Log.w(TAG, "Failed to bind USB Serial Service: " + bindException.getMessage());
                            // Service started but binding failed - continue with WiFi
                            this.usbSerialService = null;
                            usbConnected = false;
                            currentMode = CommunicationMode.WIFI;
                        }
                    } else {
                        // Service failed to start - continue with WiFi only
                        this.usbSerialService = null;
                        usbConnected = false;
                        currentMode = CommunicationMode.WIFI;
                        Log.d(TAG, "USB Serial Service failed to start - using WiFi mode");
                    }

                } catch (Exception e) {
                    Log.e(TAG, "Error in USB Serial Service initialization: " + e.getMessage());
                    // Continue without USB serial - will use WiFi only
                    this.usbSerialService = null;
                    usbConnected = false;
                    currentMode = CommunicationMode.WIFI;
                    Log.d(TAG, "Falling back to WiFi-only mode due to USB error");
                }
            }, 5000); // 5 second delay for maximum safety

        } catch (Exception e) {
            Log.e(TAG, "CRITICAL: Error initializing USB Serial Service: " + e.getMessage());
            // Continue without USB serial - will use WiFi only
            this.usbSerialService = null;
            usbConnected = false;
            currentMode = CommunicationMode.WIFI;
            Log.d(TAG, "USB Serial initialization failed - using WiFi mode");
        }

        // Debug USB Serial Service status
        Log.d(TAG, "USB Serial Service binding status: " + isServiceBound);
        Log.d(TAG, "USB Connected: " + usbConnected);
        Log.d(TAG, "Current Mode: " + currentMode);

        // Load IP addresses from settings
        loadIPAddressesFromSettings();

        // Check for USB connection
        checkUSBConnection();

        Log.d(TAG, "ESP32 Communication Manager initialized");
        Log.d(TAG, "Communication mode: " + (usbConnected ? "USB" : "WiFi"));

        // Start enhanced resilience features
        startCommandQueueProcessing();

        startHeartbeatMonitoring();
        startUSBMonitoring();
    }

    private void setupUSBSerialListener() {
        if (usbSerialService == null) {
            Log.w(TAG, "USB Serial Service is null, skipping listener setup");
            return;
        }

        try {
            usbSerialService.setSerialListener(new USBSerialService.SerialListener() {
            @Override
            public void onSerialConnect() {
                Log.d(TAG, "=== USB SERIAL CONNECTED ===");
                Log.d(TAG, "Switching to USB UART mode");
                usbConnected = true;
                currentMode = CommunicationMode.USB_UART;
                updateConnectionModeStatus("usb");
                updateConnectionStatus("motor", true);
                updateConnectionStatus("servo", true);
                Log.d(TAG, "USB mode activated - commands will use USB serial");
                if (listener != null) {
                    listener.onConnectionStatusChanged("motor", true);
                    listener.onConnectionStatusChanged("servo", true);
                }
            }

            @Override
            public void onSerialDisconnect() {
                Log.d(TAG, "=== USB SERIAL DISCONNECTED - IMMEDIATE FALLBACK ===");
                Log.d(TAG, "Switching to WiFi mode immediately");
                usbConnected = false;
                currentMode = CommunicationMode.WIFI;
                updateConnectionModeStatus("wifi");
                
                // Update connection status to reflect WiFi mode
                updateConnectionStatus("motor", false);
                updateConnectionStatus("servo", false);
                
                // Load IP addresses for WiFi fallback
                loadIPAddressesFromSettings();
                Log.d(TAG, "WiFi fallback ready - Motor IP: " + motorControllerIP + ", Servo IP: " + servoControllerIP);
                
                if (listener != null) {
                    listener.onConnectionStatusChanged("motor", false);
                    listener.onConnectionStatusChanged("servo", false);
                }
            }

            @Override
            public void onSerialRead(String data) {
                Log.d(TAG, "USB data received: " + data.trim());
                // Handle received data from ESP32 (acknowledgments, status, etc.)
                parseESP32Response(data.trim());
            }

            @Override
            public void onSerialIoError(Exception e) {
                Log.e(TAG, "USB Serial error: " + e.getMessage());
                if (listener != null) {
                    listener.onError("usb", e.getMessage());
                }
            }
        });
        } catch (Exception e) {
            Log.e(TAG, "Error setting up USB serial listener: " + e.getMessage(), e);
        }
    }

    private void loadIPAddressesFromSettings() {
        motorControllerIP = sharedPreferences.getString("motor_controller_ip", DEFAULT_MOTOR_CONTROLLER_IP);
        servoControllerIP = sharedPreferences.getString("servo_controller_ip", DEFAULT_SERVO_CONTROLLER_IP);
        sensorControllerIP = sharedPreferences.getString("sensor_controller_ip", DEFAULT_SENSOR_CONTROLLER_IP);

        Log.d(TAG, "Loaded IP addresses - Motor: " + motorControllerIP +
                   ", Servo: " + servoControllerIP + ", Sensor: " + sensorControllerIP);
    }

    public void updateIPAddresses(String motorIP, String servoIP, String sensorIP) {
        motorControllerIP = motorIP;
        servoControllerIP = servoIP;
        sensorControllerIP = sensorIP;

        // Save to preferences
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.putString("motor_controller_ip", motorIP);
        editor.putString("servo_controller_ip", servoIP);
        editor.putString("sensor_controller_ip", sensorIP);
        editor.apply();

        Log.d(TAG, "Updated IP addresses - Motor: " + motorControllerIP +
                   ", Servo: " + servoControllerIP + ", Sensor: " + sensorControllerIP);
    }

    // Refresh IP addresses from settings (call this when settings change)
    public void refreshIPAddressesFromSettings() {
        if (sharedPreferences != null) {
            loadIPAddressesFromSettings();
            Log.d(TAG, "Refreshed IP addresses from settings - Motor: " + motorControllerIP +
                       ", Servo: " + servoControllerIP + ", Sensor: " + sensorControllerIP);
        }
    }

    // Force check communication mode and update status
    public void forceCheckCommunicationMode() {
        Log.d(TAG, "=== FORCE CHECK COMMUNICATION MODE ===");
        boolean usbAvailable = checkUSBAvailability();
        Log.d(TAG, "USB Available: " + usbAvailable);
        Log.d(TAG, "Current Mode: " + currentMode);
        Log.d(TAG, "Motor IP: " + motorControllerIP);
        Log.d(TAG, "Servo IP: " + servoControllerIP);
        Log.d(TAG, "Sensor IP: " + sensorControllerIP);
        
        if (!usbAvailable) {
            // Ensure WiFi settings are loaded
            refreshIPAddressesFromSettings();
            Log.d(TAG, "WiFi fallback ready with updated settings");
        }
    }

    // Test fallback mechanism
    public void testFallbackMechanism() {
        Log.d(TAG, "=== TESTING FALLBACK MECHANISM ===");
        
        // Test motor command fallback
        Log.d(TAG, "Testing motor command fallback...");
        sendMotorCommand("TEST_FALLBACK");
        
        // Test servo command fallback
        Log.d(TAG, "Testing servo command fallback...");
        sendServoCommand("TEST_FALLBACK");
        
        Log.d(TAG, "Fallback test completed");
    }
    
    public void setCommunicationListener(CommunicationListener listener) {
        this.listener = listener;
    }
    
    // Send command to motor controller - PRIORITY: USB Serial > WiFi with immediate fallback
    public void sendMotorCommand(String action) {
        Log.d(TAG, "=== MOTOR COMMAND - USB SERIAL PRIORITY WITH FALLBACK ===");
        Log.d(TAG, "Action: " + action);
        Log.d(TAG, "Current mode: " + currentMode);
        Log.d(TAG, "USB connected: " + usbConnected);

        // Check USB connection status first
        boolean usbAvailable = checkUSBAvailability();
        
        if (usbAvailable) {
            Log.d(TAG, "PRIORITY 1: Using USB Serial communication");
            try {
                sendUSBSerialCommand(action);
                updateConnectionStatus("motor", true);
                updateConnectionModeStatus("usb_serial");
                return;
            } catch (Exception e) {
                Log.w(TAG, "USB Serial failed, falling back to WiFi: " + e.getMessage());
                // Force fallback to WiFi
                usbConnected = false;
                currentMode = CommunicationMode.WIFI;
            }
        }
        
        // Fallback to WiFi (either USB not available or USB failed)
        Log.d(TAG, "FALLBACK: Using WiFi HTTP communication");
        Log.d(TAG, "Motor Controller IP: " + motorControllerIP);
        sendWiFiCommand(action);
    }

    /**
     * Check if USB is actually available and connected
     */
    private boolean checkUSBAvailability() {
        if (usbSerialService == null) {
            Log.d(TAG, "USB Serial Service is null");
            return false;
        }
        
        boolean connected = usbSerialService.isConnected();
        Log.d(TAG, "USB Serial Service connected: " + connected);
        
        if (!connected) {
            // Update our internal state
            usbConnected = false;
            currentMode = CommunicationMode.WIFI;
            updateConnectionModeStatus("wifi");
        } else {
            // Update our internal state
            usbConnected = true;
            currentMode = CommunicationMode.USB_UART;
            updateConnectionModeStatus("usb_serial");
        }
        
        return connected;
    }

    // Send command via USB Serial (PRIORITY METHOD)
    private void sendUSBSerialCommand(String action) throws Exception {
        Log.d(TAG, "=== USB SERIAL COMMAND ===");
        Log.d(TAG, "Command: " + action);
        Log.d(TAG, "Using REAL USB Serial communication");

        if (usbSerialService == null) {
            throw new Exception("USB Serial Service is null");
        }
        
        if (!usbSerialService.isConnected()) {
            throw new Exception("USB Serial not connected");
        }

        // Send command via real USB serial
        usbSerialService.write(action);

        // Update connection status
        updateConnectionStatus("motor", true);
        updateConnectionStatus("servo", true);
        updateConnectionModeStatus("usb_serial");

        Log.d(TAG, "USB Serial command sent: " + action);
    }

    // Send command via USB UART using real USB Serial communication
    private void sendUSBCommand(String action) {
        Log.d(TAG, "=== SENDING USB UART COMMAND ===");
        Log.d(TAG, "USB UART Command: " + action);
        Log.d(TAG, "Using REAL USB Serial communication - NO IP ADDRESS");

        if (usbSerialService == null || !usbSerialService.isConnected()) {
            Log.w(TAG, "USB Serial not connected, attempting to connect...");
            if (usbSerialService != null) {
                usbSerialService.scanForDevices();
            }

            // Fallback to WiFi if USB not available
            Log.w(TAG, "USB not available, falling back to WiFi");
            currentMode = CommunicationMode.WIFI;
            sendWiFiCommand(action);
            return;
        }

        // Send command via real USB serial
        usbSerialService.write(action);

        // Update connection status
        updateConnectionStatus("motor", true);
        updateConnectionStatus("servo", true);
        updateConnectionModeStatus("usb");

        Log.d(TAG, "USB UART command sent via real serial: " + action);
    }

    // Send command via WiFi
    private void sendWiFiCommand(String action) {
        Log.d(TAG, "=== SENDING WIFI COMMAND ===");
        Log.d(TAG, "WiFi Command: " + action);
        
        // Ensure IP addresses are loaded from settings
        loadIPAddressesFromSettings();
        
        Log.d(TAG, "Motor Controller IP: " + motorControllerIP);
        Log.d(TAG, "ESP32 Port: " + ESP32_PORT);

        if (motorControllerIP == null || motorControllerIP.isEmpty() || motorControllerIP.equals("null")) {
            Log.e(TAG, "Motor controller IP not set or invalid: " + motorControllerIP);
            Log.e(TAG, "Please set the ESP32 IP address in settings");
            updateConnectionStatus("motor", false);
            if (listener != null) {
                listener.onError("motor", "ESP32 IP address not configured. Please set it in settings.");
            }
            return;
        }

        executorService.execute(() -> {
            try {
                // Build simple HTTP request matching ESP32 code: /?State=F
                String url = "http://" + motorControllerIP + ":" + ESP32_PORT + "/?State=" + action;

                Request request = new Request.Builder()
                    .url(url)
                    .get()
                    .build();

                Log.d(TAG, "Sending WiFi command to URL: " + url);

                httpClient.newCall(request).enqueue(new Callback() {
                    @Override
                    public void onFailure(Call call, IOException e) {
                        Log.e(TAG, "Failed to send WiFi command: " + action + " to " + motorControllerIP, e);
                        updateConnectionStatus("motor", false);
                        if (listener != null) {
                            listener.onError("motor", "WiFi connection failed: " + e.getMessage() + 
                                           ". Check ESP32 IP address (" + motorControllerIP + ") and WiFi connection.");
                        }
                    }

                    @Override
                    public void onResponse(Call call, Response response) throws IOException {
                        if (response.isSuccessful()) {
                            updateConnectionStatus("motor", true);
                            updateConnectionModeStatus("wifi");
                            Log.d(TAG, "WiFi command successful: " + action + " to " + motorControllerIP);
                            if (listener != null) {
                                Map<String, Object> emptyParams = new HashMap<>();
                                RobotCommand command = new RobotCommand("motor", action, emptyParams);
                                listener.onCommandSent("motor", command);
                            }
                        } else {
                            Log.e(TAG, "HTTP error for WiFi command: " + response.code() + " to " + motorControllerIP);
                            updateConnectionStatus("motor", false);
                            if (listener != null) {
                                listener.onError("motor", "HTTP " + response.code() + " from " + motorControllerIP);
                            }
                        }
                        response.close();
                    }
                });

            } catch (Exception e) {
                Log.e(TAG, "Exception sending WiFi command: " + action + " to " + motorControllerIP, e);
                updateConnectionStatus("motor", false);
                if (listener != null) {
                    listener.onError("motor", "WiFi error: " + e.getMessage());
                }
            }
        });
    }

    // Backward compatibility method
    public void sendMotorCommand(String action, Map<String, Object> parameters) {
        // Ignore parameters and use simplified version
        sendMotorCommand(action);
    }
    
    // Send servo command - PRIORITY: USB Serial > WiFi with immediate fallback
    public void sendServoCommand(String action) {
        Log.d(TAG, "=== SERVO COMMAND - USB SERIAL PRIORITY WITH FALLBACK ===");
        Log.d(TAG, "Action: " + action);
        Log.d(TAG, "Current mode: " + currentMode);
        Log.d(TAG, "USB connected: " + usbConnected);

        // Check USB connection status first
        boolean usbAvailable = checkUSBAvailability();
        
        if (usbAvailable) {
            Log.d(TAG, "PRIORITY 1: Using USB Serial communication");
            try {
                sendUSBServoCommand(action);
                updateConnectionStatus("servo", true);
                updateConnectionModeStatus("usb_serial");
                return;
            } catch (Exception e) {
                Log.w(TAG, "USB Serial failed, falling back to WiFi: " + e.getMessage());
                // Force fallback to WiFi
                usbConnected = false;
                currentMode = CommunicationMode.WIFI;
            }
        }
        
        // Fallback to WiFi (either USB not available or USB failed)
        Log.d(TAG, "FALLBACK: Using WiFi HTTP communication");
        Log.d(TAG, "Servo Controller IP: " + servoControllerIP);
        sendWiFiServoCommand(action);
    }

    // Send servo command via USB UART
    private void sendUSBServoCommand(String action) throws Exception {
        Log.d(TAG, "=== SENDING USB UART SERVO COMMAND ===");
        Log.d(TAG, "USB UART Servo Command: " + action);

        if (usbSerialService == null || !usbSerialService.isConnected()) {
            throw new Exception("USB Serial not connected for servo command");
        }

        // Send servo command via real USB serial
        usbSerialService.write(action);

        // Update connection status
        updateConnectionStatus("servo", true);
        updateConnectionModeStatus("usb");

        Log.d(TAG, "USB UART servo command sent via real serial: " + action);
    }

    // Send servo command via WiFi
    private void sendWiFiServoCommand(String action) {
        Log.d(TAG, "=== SENDING WIFI SERVO COMMAND ===");
        Log.d(TAG, "WiFi Servo Command: " + action);
        
        // Ensure IP addresses are loaded from settings
        loadIPAddressesFromSettings();
        
        // Use servo controller IP if available, otherwise fall back to motor controller IP
        String targetIP = (servoControllerIP != null && !servoControllerIP.isEmpty() && !servoControllerIP.equals("null")) 
                         ? servoControllerIP : motorControllerIP;
        
        Log.d(TAG, "Servo Controller IP: " + targetIP);
        Log.d(TAG, "ESP32 Port: " + ESP32_PORT);

        if (targetIP == null || targetIP.isEmpty() || targetIP.equals("null")) {
            Log.e(TAG, "Servo controller IP not set or invalid: " + targetIP);
            Log.e(TAG, "Please set the ESP32 IP address in settings");
            updateConnectionStatus("servo", false);
            if (listener != null) {
                listener.onError("servo", "ESP32 IP address not configured. Please set it in settings.");
            }
            return;
        }

        executorService.execute(() -> {
            try {
                // Build simple HTTP request matching ESP32 code: /?State=WAVE
                String url = "http://" + targetIP + ":" + ESP32_PORT + "/?State=" + action;

                Request request = new Request.Builder()
                    .url(url)
                    .get()
                    .build();

                Log.d(TAG, "Sending WiFi servo command to URL: " + url);

                httpClient.newCall(request).enqueue(new Callback() {
                    @Override
                    public void onFailure(Call call, IOException e) {
                        Log.e(TAG, "Failed to send WiFi servo command: " + action + " to " + targetIP, e);
                        updateConnectionStatus("servo", false);
                        if (listener != null) {
                            listener.onError("servo", "WiFi connection failed: " + e.getMessage() + 
                                           ". Check ESP32 IP address (" + targetIP + ") and WiFi connection.");
                        }
                    }

                    @Override
                    public void onResponse(Call call, Response response) throws IOException {
                        if (response.isSuccessful()) {
                            updateConnectionStatus("servo", true);
                            updateConnectionModeStatus("wifi");
                            String responseBody = response.body().string();
                            Log.d(TAG, "WiFi servo command successful: " + action + " to " + targetIP);
                            Log.d(TAG, "Response: " + responseBody);

                            if (listener != null) {
                                Map<String, Object> emptyParams = new HashMap<>();
                                RobotCommand command = new RobotCommand("servo", action, emptyParams);
                                listener.onCommandSent("servo", command);
                            }
                        } else {
                            Log.e(TAG, "HTTP error from WiFi servo command: " + response.code());
                            updateConnectionStatus("servo", false);
                            if (listener != null) {
                                listener.onError("servo", "HTTP " + response.code());
                            }
                        }
                        response.close();
                    }
                });

            } catch (Exception e) {
                Log.e(TAG, "Exception sending WiFi servo command: " + action, e);
                updateConnectionStatus("servo", false);
                if (listener != null) {
                    listener.onError("servo", e.getMessage());
                }
            }
        });
    }

    // Send servo command with parameters (for individual arm control)
    public void sendServoCommand(String action, Map<String, Object> parameters) {
        if (parameters != null && parameters.containsKey("servo") && parameters.containsKey("angle")) {
            String servo = (String) parameters.get("servo");
            int angle = (Integer) parameters.get("angle");

            String command;
            if ("left_shoulder".equals(servo)) {
                command = "LA" + angle;
            } else if ("right_shoulder".equals(servo)) {
                command = "RA" + angle;
            } else if ("head_pan".equals(servo)) {
                command = "HP" + angle;
            } else if ("head_tilt".equals(servo)) {
                command = "HT" + angle;
            } else {
                // Default to simple action
                sendServoCommand(action);
                return;
            }

            sendServoCommand(command);
        } else {
            // Fallback to simple command
            sendServoCommand(action);
        }
    }
    
    // Send command to sensor controller - PRIORITY: USB Serial > WiFi with immediate fallback
    public void sendSensorCommand(String action, Map<String, Object> parameters) {
        Log.d(TAG, "=== SENSOR COMMAND - USB SERIAL PRIORITY WITH FALLBACK ===");
        Log.d(TAG, "Action: " + action + ", Parameters: " + parameters);
        Log.d(TAG, "Current mode: " + currentMode);
        Log.d(TAG, "USB connected: " + usbConnected);

        // Check USB connection status first
        boolean usbAvailable = checkUSBAvailability();

        if (usbAvailable) {
            Log.d(TAG, "PRIORITY 1: Using USB Serial for sensor command");
            // Convert sensor command to simple ESP32 format if possible
            String simpleCommand = convertSensorCommandToSimple(action, parameters);
            if (simpleCommand != null) {
                try {
                    sendUSBSerialCommand(simpleCommand);
                    updateConnectionStatus("sensor", true);
                    updateConnectionModeStatus("usb_serial");
                    return;
                } catch (Exception e) {
                    Log.w(TAG, "USB Serial failed for sensor command, falling back to WiFi: " + e.getMessage());
                    // Force fallback to WiFi
                    usbConnected = false;
                    currentMode = CommunicationMode.WIFI;
                }
            }
        }

        // Fallback to WiFi
        Log.d(TAG, "PRIORITY 2: Using WiFi for sensor command");
        Log.d(TAG, "Sensor Controller IP: " + sensorControllerIP);
        RobotCommand command = new RobotCommand("sensor", action, parameters);
        sendCommand(sensorControllerIP, "sensor", command);
    }

    // Convert sensor commands to simple ESP32 format when possible
    private String convertSensorCommandToSimple(String action, Map<String, Object> parameters) {
        // Convert sensor commands to simple ESP32 format
        switch (action) {
            case "get_heart_rate":
                return "STATUS"; // Request status which includes heart rate
            case "HUMAN_DETECT_ON":
                return "HUMAN_DETECT_ON";
            case "HUMAN_DETECT_OFF":
                return "HUMAN_DETECT_OFF";
            case "GET_DISTANCE":
                return "GET_DISTANCE";
            case "HANDSHAKE":
                return "HANDSHAKE"; // Smart Greeting handshake command
            case "GREETING_START":
                return "GREETING_START"; // Enhanced greeting start
            case "GREETING_END":
                return "GREETING_END"; // Enhanced greeting end
            default:
                // Handle commands that start with specific prefixes
                if (action.startsWith("SET_HANDSHAKE_DURATION:")) {
                    return action; // Pass through duration setting commands
                }
                if (action.startsWith("SET_SERVO_SPEED:")) {
                    return action; // Pass through servo speed setting commands
                }
                if (action.equals("LOOK_LEFT") || action.equals("LOOK_RIGHT") || action.equals("CENTER_HEAD")) {
                    return action; // Pass through head movement commands
                }
                Log.w(TAG, "Unknown sensor command: " + action);
                return null; // Unknown command
        }
    }

    private void sendCommand(String ipAddress, String controllerId, RobotCommand command) {
        executorService.execute(() -> {
            try {
                String url = "http://" + ipAddress + ":" + ESP32_PORT + "/command";
                String jsonCommand = gson.toJson(command);

                RequestBody body = RequestBody.create(jsonCommand, JSON);
                Request request = new Request.Builder()
                    .url(url)
                    .post(body)
                    .addHeader("Content-Type", "application/json")
                    .build();

                Log.d(TAG, "Sending WiFi command to " + controllerId + ": " + jsonCommand);
                
                httpClient.newCall(request).enqueue(new Callback() {
                    @Override
                    public void onFailure(Call call, IOException e) {
                        Log.e(TAG, "Failed to send command to " + controllerId, e);
                        updateConnectionStatus(controllerId, false);
                        if (listener != null) {
                            listener.onError(controllerId, e.getMessage());
                        }
                    }
                    
                    @Override
                    public void onResponse(Call call, Response response) throws IOException {
                        if (response.isSuccessful()) {
                            updateConnectionStatus(controllerId, true);
                            
                            String responseBody = response.body().string();
                            Log.d(TAG, "Response from " + controllerId + ": " + responseBody);
                            
                            try {
                                RobotResponse robotResponse = gson.fromJson(responseBody, RobotResponse.class);
                                if (listener != null) {
                                    listener.onCommandSent(controllerId, command);
                                    listener.onResponseReceived(controllerId, robotResponse);
                                }
                            } catch (JsonSyntaxException e) {
                                Log.e(TAG, "Failed to parse response from " + controllerId, e);
                            }
                        } else {
                            Log.e(TAG, "HTTP error from " + controllerId + ": " + response.code());
                            updateConnectionStatus(controllerId, false);
                            if (listener != null) {
                                listener.onError(controllerId, "HTTP " + response.code());
                            }
                        }
                        response.close();
                    }
                });
                
            } catch (Exception e) {
                Log.e(TAG, "Exception sending command to " + controllerId, e);
                if (listener != null) {
                    listener.onError(controllerId, e.getMessage());
                }
            }
        });
    }
    
    private void updateConnectionStatus(String controllerId, boolean connected) {
        boolean wasConnected = connectionStatus.getOrDefault(controllerId, false);
        connectionStatus.put(controllerId, connected);
        lastHeartbeat.put(controllerId, System.currentTimeMillis());
        
        if (wasConnected != connected && listener != null) {
            listener.onConnectionStatusChanged(controllerId, connected);
        }
    }
    
    private void startHeartbeatMonitoring() {
        executorService.execute(() -> {
            while (!Thread.currentThread().isInterrupted()) {
                try {
                    Thread.sleep(5000); // Check every 5 seconds
                    checkHeartbeats();
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        });
    }
    
    private void checkHeartbeats() {
        long currentTime = System.currentTimeMillis();
        long timeout = 15000; // 15 seconds timeout
        
        for (Map.Entry<String, Long> entry : lastHeartbeat.entrySet()) {
            String controllerId = entry.getKey();
            Long lastTime = entry.getValue();
            
            if (lastTime != null && (currentTime - lastTime) > timeout) {
                updateConnectionStatus(controllerId, false);
            }
        }
    }
    
    public boolean isControllerConnected(String controllerId) {
        return connectionStatus.getOrDefault(controllerId, false);
    }
    
    public Map<String, Boolean> getAllConnectionStatus() {
        return new HashMap<>(connectionStatus);
    }

    // Connection testing methods
    public interface ConnectionTestListener {
        void onTestResult(String controllerId, boolean success, String message);
        void onAllTestsComplete(Map<String, Boolean> results);
    }

    public interface SingleConnectionTestListener {
        void onTestResult(boolean success, String message);
    }

    public void testAllConnections(ConnectionTestListener testListener) {
        Map<String, Boolean> results = new HashMap<>();
        final int[] completedTests = {0};

        // Test motor controller
        testSingleConnection("motor", motorControllerIP, new SingleConnectionTestListener() {
            @Override
            public void onTestResult(boolean success, String message) {
                results.put("motor", success);
                testListener.onTestResult("motor", success, message);
                completedTests[0]++;
                if (completedTests[0] == 3) {
                    testListener.onAllTestsComplete(results);
                }
            }
        });

        // Test servo controller
        testSingleConnection("servo", servoControllerIP, new SingleConnectionTestListener() {
            @Override
            public void onTestResult(boolean success, String message) {
                results.put("servo", success);
                testListener.onTestResult("servo", success, message);
                completedTests[0]++;
                if (completedTests[0] == 3) {
                    testListener.onAllTestsComplete(results);
                }
            }
        });

        // Test sensor controller
        testSingleConnection("sensor", sensorControllerIP, new SingleConnectionTestListener() {
            @Override
            public void onTestResult(boolean success, String message) {
                results.put("sensor", success);
                testListener.onTestResult("sensor", success, message);
                completedTests[0]++;
                if (completedTests[0] == 3) {
                    testListener.onAllTestsComplete(results);
                }
            }
        });
    }

    public void testSingleConnection(String controllerId, String ipAddress, SingleConnectionTestListener testListener) {
        executorService.execute(() -> {
            try {
                String url = "http://" + ipAddress + ":" + ESP32_PORT + "/status";

                Request request = new Request.Builder()
                    .url(url)
                    .get()
                    .build();

                httpClient.newCall(request).enqueue(new Callback() {
                    @Override
                    public void onFailure(Call call, IOException e) {
                        testListener.onTestResult(false, "Connection failed: " + e.getMessage());
                    }

                    @Override
                    public void onResponse(Call call, Response response) throws IOException {
                        if (response.isSuccessful()) {
                            testListener.onTestResult(true, "Connected successfully");
                            updateConnectionStatus(controllerId, true);
                        } else {
                            testListener.onTestResult(false, "HTTP error: " + response.code());
                            updateConnectionStatus(controllerId, false);
                        }
                        response.close();
                    }
                });

            } catch (Exception e) {
                testListener.onTestResult(false, "Exception: " + e.getMessage());
            }
        });
    }

    // Robust reconnection mechanism
    public void startRobustConnection() {
        executorService.execute(() -> {
            while (!Thread.currentThread().isInterrupted()) {
                try {
                    // Check all connections every 10 seconds for responsive monitoring
                    testAllConnections(new ConnectionTestListener() {
                        @Override
                        public void onTestResult(String controllerId, boolean success, String message) {
                            // Only notify on actual status changes, not every check
                            Boolean lastStatus = connectionStatus.get(controllerId);
                            if (lastStatus == null || lastStatus != success) {
                                if (listener != null) {
                                    listener.onConnectionStatusChanged(controllerId, success);
                                }
                            }
                        }

                        @Override
                        public void onAllTestsComplete(Map<String, Boolean> results) {
                            // Reduced logging - only log when there are changes
                            // Log.d(TAG, "Connection check complete");
                        }
                    });

                    Thread.sleep(10000); // Check every 10 seconds for responsive monitoring
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                } catch (Exception e) {
                    Log.e(TAG, "Error in robust connection monitoring", e);
                }
            }
        });
    }

    // USB Communication Methods
    private void checkUSBConnection() {
        if (usbSerialService == null) {
            usbConnected = false;
            return;
        }

        // Use real USB serial service to check connection
        boolean previousUsbState = usbConnected;
        usbConnected = usbSerialService.isConnected();

        // If not connected, try to scan for devices
        if (!usbConnected) {
            usbSerialService.scanForDevices();
        }

        if (previousUsbState != usbConnected) {
            Log.d(TAG, "USB connection status changed: " + (usbConnected ? "Connected" : "Disconnected"));
            if (usbConnected) {
                currentMode = CommunicationMode.USB_UART;
                updateConnectionModeStatus("usb");
            } else {
                currentMode = CommunicationMode.WIFI;
                updateConnectionModeStatus("wifi");
            }
        }
    }

    private void updateConnectionModeStatus(String mode) {
        connectionModeStatus.put("motor", mode);
        connectionModeStatus.put("servo", mode);
        connectionModeStatus.put("sensor", mode);

        // Update connection status based on mode
        if ("usb".equals(mode)) {
            updateConnectionStatus("motor", usbConnected);
            updateConnectionStatus("servo", usbConnected);
        }
    }

    public CommunicationMode getCurrentMode() {
        return currentMode;
    }

    public boolean isUsbConnected() {
        // Check both internal flag and USB serial service status
        if (usbSerialService != null) {
            boolean serialConnected = usbSerialService.isConnected();
            if (serialConnected != usbConnected) {
                // Update internal flag to match actual USB status
                usbConnected = serialConnected;
                Log.d(TAG, "USB connection status updated: " + usbConnected);
            }
            return serialConnected;
        }
        return usbConnected;
    }

    public String getConnectionModeForController(String controllerId) {
        return connectionModeStatus.getOrDefault(controllerId, "wifi");
    }

    public Map<String, String> getAllConnectionModes() {
        return new HashMap<>(connectionModeStatus);
    }

    // Manual communication mode control
    public void forceUSBMode() {
        Log.d(TAG, "=== FORCE USB MODE ===");

        // Check actual USB serial service status
        boolean actualUsbStatus = (usbSerialService != null && usbSerialService.isConnected());
        Log.d(TAG, "USB Serial Service status: " + actualUsbStatus);
        Log.d(TAG, "Internal USB flag: " + usbConnected);

        if (actualUsbStatus) {
            usbConnected = true; // Update internal flag
            currentMode = CommunicationMode.USB_UART;
            updateConnectionModeStatus("usb");
            Log.d(TAG, "Forced switch to USB UART mode");
        } else {
            // Try to scan for USB devices first
            if (usbSerialService != null) {
                Log.d(TAG, "USB not connected, attempting to scan for devices...");
                usbSerialService.scanForDevices();
                // Give it a moment to scan
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }

                // Check again after scan
                if (usbSerialService.isConnected()) {
                    usbConnected = true;
                    currentMode = CommunicationMode.USB_UART;
                    updateConnectionModeStatus("usb");
                    Log.d(TAG, "USB device found and connected - switched to USB mode");
                    return;
                }
            }

            Log.w(TAG, "Cannot force USB mode - USB not connected");
            throw new IllegalStateException("USB not connected");
        }
    }

    public void forceWiFiMode() {
        Log.d(TAG, "=== FORCE WIFI MODE ===");
        currentMode = CommunicationMode.WIFI;
        updateConnectionModeStatus("wifi");
        Log.d(TAG, "Forced switch to WiFi mode");
    }

    // Manual USB scan method for debugging
    public void scanForUSBDevices() {
        Log.d(TAG, "=== MANUAL USB SCAN ===");
        if (usbSerialService != null) {
            usbSerialService.scanForDevices();
            Log.d(TAG, "USB scan initiated");
        } else {
            Log.e(TAG, "USB Serial Service is null - attempting re-initialization");
            reinitializeUSBSerialService();
        }
    }

    // Force re-initialization of USB Serial Service
    public void reinitializeUSBSerialService() {
        Log.d(TAG, "=== FORCE USB SERIAL SERVICE RE-INITIALIZATION ===");

        try {
            // Unbind existing service if any
            if (isServiceBound && context != null) {
                context.unbindService(serviceConnection);
                isServiceBound = false;
            }

            // Start and bind service again
            Intent serviceIntent = new Intent(context, USBSerialService.class);
            context.startForegroundService(serviceIntent);
            context.bindService(serviceIntent, serviceConnection, Context.BIND_AUTO_CREATE);

            Log.d(TAG, "USB Serial Service re-initialization initiated");
        } catch (Exception e) {
            Log.e(TAG, "Error during USB Serial Service re-initialization: " + e.getMessage(), e);
            this.usbSerialService = null;
            usbConnected = false;
            currentMode = CommunicationMode.WIFI;
        }
    }

    // Get detailed USB status for debugging
    public String getUSBDebugInfo() {
        StringBuilder info = new StringBuilder();
        info.append("=== USB DEBUG INFO ===\n");
        info.append("USB Serial Service: ").append(usbSerialService != null ? "Available" : "NULL").append("\n");
        if (usbSerialService != null) {
            info.append("USB Connected: ").append(usbSerialService.isConnected()).append("\n");
        }
        info.append("Internal USB Flag: ").append(usbConnected).append("\n");
        info.append("Current Mode: ").append(currentMode).append("\n");
        info.append("======================");
        return info.toString();
    }

    // Emergency stop all operations
    public void emergencyStopAll() {
        Log.d(TAG, "=== EMERGENCY STOP ALL OPERATIONS ===");
        sendMotorCommand("STOP_ALL");
        sendServoCommand("REST");
    }

    // Send immediate override command
    public void sendImmediateCommand(String action) {
        Log.d(TAG, "=== IMMEDIATE OVERRIDE COMMAND ===");
        Log.d(TAG, "Sending immediate command: " + action);
        sendMotorCommand(action);
    }

    // Human Detection and Ultrasonic Sensor Commands
    public void sendHelloGreeting() {
        Log.d(TAG, "=== SENDING HELLO GREETING COMMAND ===");
        sendServoCommand("HELLO");
    }

    public void enableHumanDetection() {
        Log.d(TAG, "=== ENABLING HUMAN DETECTION ===");
        sendSensorCommand("HUMAN_DETECT_ON", null);
    }

    public void disableHumanDetection() {
        Log.d(TAG, "=== DISABLING HUMAN DETECTION ===");
        sendSensorCommand("HUMAN_DETECT_OFF", null);
    }

    public void getUltrasonicDistance() {
        Log.d(TAG, "=== REQUESTING ULTRASONIC DISTANCE ===");
        sendSensorCommand("GET_DISTANCE", null);
    }

    // Smart Greeting Integration Methods
    public void sendHandshakeGreeting() {
        System.out.println(TAG + ": === SENDING HANDSHAKE GREETING ===");

        // Send handshake command to ESP32
        sendSensorCommand("HANDSHAKE", null);

        // Speak greeting using TTS
        speakGreeting("Hi there! Nice to meet you!");
    }

    /**
     * Send handshake duration setting to ESP32
     */
    public void setHandshakeDuration(int durationMs) {
        System.out.println(TAG + ": === SETTING HANDSHAKE DURATION: " + durationMs + "ms ===");

        // Send duration setting command to ESP32
        String command = "SET_HANDSHAKE_DURATION:" + durationMs;
        sendSensorCommand(command, null);
    }

    /**
     * Initialize Text-to-Speech for Smart Greeting
     */
    private void initializeTTS() {
        if (context != null && textToSpeech == null) {
            try {
                textToSpeech = new TextToSpeech(context, status -> {
                    if (status == TextToSpeech.SUCCESS) {
                        int result = textToSpeech.setLanguage(Locale.US);
                        if (result == TextToSpeech.LANG_MISSING_DATA || result == TextToSpeech.LANG_NOT_SUPPORTED) {
                            System.out.println(TAG + ": TTS Language not supported");
                            isTTSInitialized = false;
                        } else {
                            System.out.println(TAG + ": TTS initialized successfully for Smart Greeting");
                            isTTSInitialized = true;

                            // Configure voice gender for Smart Greeting
                            configureVoiceGender();
                        }
                    } else {
                        System.out.println(TAG + ": TTS initialization failed");
                        isTTSInitialized = false;
                    }
                });
            } catch (Exception e) {
                System.out.println(TAG + ": Error initializing TTS: " + e.getMessage());
                isTTSInitialized = false;
            }
        }
    }

    /**
     * Speak greeting text using TTS
     */
    private void speakGreeting(String text) {
        try {
            // Initialize TTS if not already done
            if (!isTTSInitialized) {
                initializeTTS();
            }

            // Speak the greeting if TTS is available
            if (textToSpeech != null && isTTSInitialized) {
                // Apply voice gender settings before speaking
                configureVoiceGender();

                textToSpeech.speak(text, TextToSpeech.QUEUE_FLUSH, null, "smart_greeting");
                System.out.println(TAG + ": Speaking Smart Greeting: " + text);
            } else {
                System.out.println(TAG + ": TTS not available for Smart Greeting: " + text);
            }
        } catch (Exception e) {
            System.out.println(TAG + ": Error speaking greeting: " + e.getMessage());
        }
    }

    /**
     * Configure voice gender based on user settings with enhanced pitch control
     */
    private void configureVoiceGender() {
        if (textToSpeech != null && context != null) {
            try {
                SharedPreferences sharedPreferences = PreferenceManager.getDefaultSharedPreferences(context);
                String voiceGender = sharedPreferences.getString("voice_gender", "female");

                if ("male".equals(voiceGender)) {
                    // Male voice configuration - much more aggressive settings
                    textToSpeech.setPitch(0.6f);  // Much lower pitch for deeper male voice
                    textToSpeech.setSpeechRate(0.85f); // Significantly slower
                    System.out.println(TAG + ": Smart Greeting MALE voice configured (pitch: 0.6, rate: 0.85)");
                } else {
                    // Female voice configuration - higher and faster
                    textToSpeech.setPitch(1.2f);  // Higher pitch for female voice
                    textToSpeech.setSpeechRate(1.05f); // Slightly faster
                    System.out.println(TAG + ": Smart Greeting FEMALE voice configured (pitch: 1.2, rate: 1.05)");
                }
            } catch (Exception e) {
                System.out.println(TAG + ": Error configuring voice gender: " + e.getMessage());
            }
        }
    }

    public void enableDistanceStreaming() {
        Log.d(TAG, "=== ENABLING DISTANCE STREAMING ===");
        sendSensorCommand("STREAM_DISTANCE_ON", null);
    }

    public void disableDistanceStreaming() {
        System.out.println(TAG + ": === DISABLING DISTANCE STREAMING ===");
        sendSensorCommand("STREAM_DISTANCE_OFF", null);
    }

    /**
     * Start continuous distance monitoring for Smart Greeting
     */
    public void startContinuousDistanceMonitoring() {
        if (isContinuousMonitoringActive) {
            System.out.println(TAG + ": Continuous monitoring already active");
            return;
        }

        isContinuousMonitoringActive = true;

        // Initialize handler if needed
        if (continuousMonitoringHandler == null) {
            continuousMonitoringHandler = new Handler(Looper.getMainLooper());
        }

        // Initialize monitoring runnable
        distanceMonitoringRunnable = new Runnable() {
            @Override
            public void run() {
                if (isContinuousMonitoringActive) {
                    // Request distance reading
                    getUltrasonicDistance();

                    // Schedule next reading
                    continuousMonitoringHandler.postDelayed(this, CONTINUOUS_MONITORING_INTERVAL);
                }
            }
        };

        // Start monitoring
        continuousMonitoringHandler.post(distanceMonitoringRunnable);

        // Enable distance streaming on ESP32
        enableDistanceStreaming();

        System.out.println(TAG + ": Continuous distance monitoring started (interval: " +
                          CONTINUOUS_MONITORING_INTERVAL + "ms)");

        // Notify callback
        if (distanceCallback != null) {
            distanceCallback.onDistanceStreamingStatusChanged(true);
        }
    }

    /**
     * Stop continuous distance monitoring
     */
    public void stopContinuousDistanceMonitoring() {
        if (!isContinuousMonitoringActive) {
            return;
        }

        isContinuousMonitoringActive = false;

        // Stop monitoring runnable
        if (continuousMonitoringHandler != null && distanceMonitoringRunnable != null) {
            continuousMonitoringHandler.removeCallbacks(distanceMonitoringRunnable);
        }

        // Disable distance streaming on ESP32
        disableDistanceStreaming();

        System.out.println(TAG + ": Continuous distance monitoring stopped");

        // Notify callback
        if (distanceCallback != null) {
            distanceCallback.onDistanceStreamingStatusChanged(false);
        }
    }

    /**
     * Set distance update callback
     */
    public void setDistanceUpdateCallback(DistanceUpdateCallback callback) {
        this.distanceCallback = callback;
    }

    /**
     * Check if continuous monitoring is active
     */
    public boolean isContinuousMonitoringActive() {
        return isContinuousMonitoringActive;
    }

    // Parse ESP32 responses from USB serial
    private void parseESP32Response(String data) {
        if (data == null || data.trim().isEmpty()) {
            return;
        }

        try {
            // Parse distance response: "USB_DISTANCE: 25.4 cm" or "USB_DISTANCE_STREAM: 25.4 cm"
            if (data.startsWith("USB_DISTANCE:") || data.startsWith("USB_DISTANCE_STREAM:")) {
                String prefix = data.startsWith("USB_DISTANCE_STREAM:") ? "USB_DISTANCE_STREAM:" : "USB_DISTANCE:";
                String distanceStr = data.substring(prefix.length()).trim();
                if (distanceStr.endsWith(" cm")) {
                    distanceStr = distanceStr.substring(0, distanceStr.length() - 3).trim(); // Remove " cm"
                }

                float distance = Float.parseFloat(distanceStr);
                System.out.println(TAG + ": Parsed ultrasonic distance: " + distance + " cm" +
                          (data.startsWith("USB_DISTANCE_STREAM:") ? " (streaming)" : ""));

                // Update current distance for Smart Greeting
                updateCurrentDistance(distance);

                // Notify distance callback for continuous monitoring
                if (distanceCallback != null) {
                    distanceCallback.onDistanceUpdated(distance);
                }

                // Create response object and notify listener
                if (listener != null) {
                    Map<String, Object> data_map = new HashMap<>();
                    data_map.put("ultrasonic_distance", distance);

                    RobotResponse response = new RobotResponse("sensor", "success", "Distance measurement");
                    response.setData(data_map);
                    listener.onResponseReceived("sensor", response);
                }
            }
            // Parse human detection status: "HUMAN_DETECTED: Distance 25.4cm - Greeting initiated"
            else if (data.startsWith("HUMAN_DETECTED:")) {
                Log.d(TAG, "Human detected: " + data);

                if (listener != null) {
                    Map<String, Object> data_map = new HashMap<>();
                    data_map.put("human_detected", true);

                    RobotResponse response = new RobotResponse("sensor", "success", "Human detected");
                    response.setData(data_map);
                    listener.onResponseReceived("sensor", response);
                }
            }
            // Parse status responses: "USB_STATUS: Human Detection Enabled - ACTIVE"
            else if (data.startsWith("USB_STATUS:")) {
                Log.d(TAG, "Status update: " + data);

                if (listener != null) {
                    Map<String, Object> data_map = new HashMap<>();
                    data_map.put("status_message", data.substring(11).trim());

                    RobotResponse response = new RobotResponse("sensor", "success", "Status update");
                    response.setData(data_map);
                    listener.onResponseReceived("sensor", response);
                }
            }
            // Parse error responses: "USB_ERROR: Unknown command"
            else if (data.startsWith("USB_ERROR:")) {
                Log.e(TAG, "ESP32 error: " + data);

                if (listener != null) {
                    listener.onError("sensor", data.substring(10).trim());
                }
            }
            else {
                // Log other responses for debugging
                Log.d(TAG, "ESP32 response: " + data);
            }

        } catch (NumberFormatException e) {
            Log.e(TAG, "Error parsing distance from ESP32 response: " + data, e);
        } catch (Exception e) {
            Log.e(TAG, "Error parsing ESP32 response: " + data, e);
        }
    }



    // Get current communication mode status
    public String getCurrentModeStatus() {
        String mode = currentMode == CommunicationMode.USB_UART ? "USB Serial" : "WiFi";
        boolean usbAvailable = usbSerialService != null && usbSerialService.isConnected();
        return mode + " (USB Available: " + usbAvailable + ")";
    }



    // Get connection status for UI display
    public Map<String, Boolean> getConnectionStatus() {
        return new HashMap<>(connectionStatus);
    }

    // Get connection mode status for UI display
    public Map<String, String> getConnectionModeStatus() {
        return new HashMap<>(connectionModeStatus);
    }

    // USB monitoring for connection status updates with faster detection
    private void startUSBMonitoring() {
        if (usbSerialService == null) {
            Log.w(TAG, "USB Serial Service is null, skipping USB monitoring");
            return;
        }

        executorService.execute(() -> {
            while (!Thread.currentThread().isInterrupted()) {
                try {
                    // Check USB connection status every 1 second for faster detection
                    Thread.sleep(1000);

                    boolean currentUsbStatus = usbSerialService.isConnected();
                    if (currentUsbStatus != usbConnected) {
                        Log.d(TAG, "=== USB STATUS CHANGE DETECTED ===");
                        Log.d(TAG, "Previous: " + usbConnected + ", Current: " + currentUsbStatus);
                        
                        usbConnected = currentUsbStatus;
                        if (usbConnected) {
                            currentMode = CommunicationMode.USB_UART;
                            updateConnectionModeStatus("usb");
                            Log.d(TAG, "USB connection detected - switched to USB mode");
                            
                            // Update connection status for UI
                            updateConnectionStatus("motor", true);
                            updateConnectionStatus("servo", true);
                        } else {
                            currentMode = CommunicationMode.WIFI;
                            updateConnectionModeStatus("wifi");
                            loadIPAddressesFromSettings(); // Ensure WiFi settings are ready
                            Log.d(TAG, "USB disconnected - switched to WiFi mode (Motor IP: " + motorControllerIP + ")");
                            
                            // Update connection status for UI
                            updateConnectionStatus("motor", false);
                            updateConnectionStatus("servo", false);
                        }
                    }

                    // Try to scan for devices if not connected (less frequently)
                    if (!usbConnected && (System.currentTimeMillis() % 5000 < 1000)) {
                        usbSerialService.scanForDevices();
                    }

                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                } catch (Exception e) {
                    Log.e(TAG, "Error in USB monitoring: " + e.getMessage());
                }
            }
        });
    }

    /**
     * Check if USB is currently connected
     */
    public boolean isUSBConnected() {
        return usbConnected && usbSerialService != null && usbSerialService.isConnected();
    }

    /**
     * Test WiFi connection to ESP32
     */
    public void testWiFiConnection(SingleConnectionTestListener testListener) {
        Log.d(TAG, "=== TESTING WIFI CONNECTION ===");
        loadIPAddressesFromSettings();
        
        if (motorControllerIP == null || motorControllerIP.isEmpty() || motorControllerIP.equals("null")) {
            if (testListener != null) {
                testListener.onTestResult(false, "IP address not configured");
            }
            return;
        }

        executorService.execute(() -> {
            try {
                String url = "http://" + motorControllerIP + ":" + ESP32_PORT + "/status";
                
                Request request = new Request.Builder()
                    .url(url)
                    .get()
                    .build();

                Log.d(TAG, "Testing WiFi connection to: " + url);

                httpClient.newCall(request).enqueue(new Callback() {
                    @Override
                    public void onFailure(Call call, IOException e) {
                        Log.e(TAG, "WiFi connection test failed: " + e.getMessage());
                        if (testListener != null) {
                            testListener.onTestResult(false, "Connection failed: " + e.getMessage());
                        }
                    }

                    @Override
                    public void onResponse(Call call, Response response) throws IOException {
                        boolean success = response.isSuccessful();
                        String message = success ? "Connected successfully" : "HTTP " + response.code();
                        Log.d(TAG, "WiFi connection test result: " + success + " - " + message);
                        
                        if (testListener != null) {
                            testListener.onTestResult(success, message);
                        }
                        response.close();
                    }
                });

            } catch (Exception e) {
                Log.e(TAG, "Exception testing WiFi connection: " + e.getMessage());
                if (testListener != null) {
                    testListener.onTestResult(false, "Test failed: " + e.getMessage());
                }
            }
        });
    }

    // Check if ultrasonic sensor is enabled in settings
    public boolean isUltrasonicSensorEnabled() {
        if (sharedPreferences == null) {
            return true; // Default to enabled if preferences not available
        }
        return sharedPreferences.getBoolean("ultrasonic_sensor_enabled", true);
    }

    public void cleanup() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }

        // Cleanup USB Serial Service
        if (isServiceBound && context != null) {
            context.unbindService(serviceConnection);
            isServiceBound = false;
        }

        Log.d(TAG, "ESP32 Communication Manager cleaned up");
    }

    // Current distance tracking for Smart Greeting
    private float currentDistance = 999.0f; // Default to far distance

    /**
     * Get current distance reading for Smart Greeting integration
     */
    public float getCurrentDistance() {
        return currentDistance;
    }

    /**
     * Update current distance (called when distance data is received)
     */
    private void updateCurrentDistance(float distance) {
        this.currentDistance = distance;
    }
}
