package com.stemrobo.humanoid.ai;

import android.content.Context;
import android.content.SharedPreferences;
import android.preference.PreferenceManager;
import android.util.Log;

import com.stemrobo.humanoid.database.ChatConversation;
import com.stemrobo.humanoid.database.ChatHistory;
import com.stemrobo.humanoid.database.PersonDatabase;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Manages conversation memory for AI chat interactions
 * Handles conversation history, context building, and memory management
 */
public class ConversationMemoryManager {
    private static final String TAG = "ConversationMemoryManager";
    
    // Configuration constants
    private static final int DEFAULT_MAX_CONTEXT_PAIRS = 10; // Default last 10 Q&A pairs for context
    private static final int MAX_STORED_MESSAGES = 100; // Maximum messages per conversation
    private static final long CONVERSATION_TIMEOUT_MS = 30 * 60 * 1000; // 30 minutes
    private static final String PREF_CONTEXT_PAIRS = "ai_context_pairs";
    
    private Context context;
    private PersonDatabase database;
    private ExecutorService executor;
    private String currentConversationId;
    
    public ConversationMemoryManager(Context context) {
        this.context = context;
        this.database = PersonDatabase.getInstance(context);
        this.executor = Executors.newSingleThreadExecutor();
    }

    /**
     * Get the current maximum context pairs setting
     */
    private int getMaxContextPairs() {
        try {
            SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(context);
            return prefs.getInt(PREF_CONTEXT_PAIRS, DEFAULT_MAX_CONTEXT_PAIRS);
        } catch (Exception e) {
            return DEFAULT_MAX_CONTEXT_PAIRS;
        }
    }

    /**
     * Update the maximum context pairs setting
     */
    public void setMaxContextPairs(int maxPairs) {
        try {
            SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(context);
            SharedPreferences.Editor editor = prefs.edit();
            editor.putInt(PREF_CONTEXT_PAIRS, Math.max(1, Math.min(20, maxPairs))); // Clamp between 1-20
            editor.apply();
            Log.d(TAG, "Updated max context pairs to: " + maxPairs);
        } catch (Exception e) {
            Log.e(TAG, "Error updating max context pairs", e);
        }
    }

    /**
     * Get the current maximum context pairs setting (public access)
     */
    public int getCurrentMaxContextPairs() {
        return getMaxContextPairs();
    }
    
    /**
     * Start a new conversation or continue existing one
     */
    public String startConversation(String languageCode) {
        // Check if there's an active conversation that's still recent
        ChatConversation activeConversation = database.chatConversationDao().getActiveConversation();
        
        if (activeConversation != null) {
            long timeSinceLastUpdate = System.currentTimeMillis() - activeConversation.getLastUpdatedTimestamp();
            if (timeSinceLastUpdate < CONVERSATION_TIMEOUT_MS) {
                // Continue existing conversation
                currentConversationId = activeConversation.getConversationId();
                Log.d(TAG, "Continuing existing conversation: " + currentConversationId);
                return currentConversationId;
            } else {
                // Deactivate old conversation
                database.chatConversationDao().deactivateAllConversations();
            }
        }
        
        // Create new conversation
        currentConversationId = UUID.randomUUID().toString();
        ChatConversation newConversation = new ChatConversation(currentConversationId);
        newConversation.setLanguageCode(languageCode);
        newConversation.setActive(true);
        
        executor.execute(() -> {
            database.chatConversationDao().insertConversation(newConversation);
            Log.d(TAG, "Started new conversation: " + currentConversationId);
        });
        
        return currentConversationId;
    }
    
    /**
     * Add user message to conversation
     */
    public void addUserMessage(String message, String languageCode) {
        if (currentConversationId == null) {
            startConversation(languageCode);
        }
        
        executor.execute(() -> {
            try {
                int sequenceNumber = database.chatHistoryDao().getNextSequenceNumber(currentConversationId);
                
                ChatHistory userMessage = new ChatHistory(
                    currentConversationId, 
                    message, 
                    ChatHistory.TYPE_USER, 
                    sequenceNumber,
                    languageCode
                );
                
                database.chatHistoryDao().insertMessage(userMessage);
                database.chatConversationDao().incrementMessageCount(currentConversationId, System.currentTimeMillis());
                
                // Clean up old messages if needed
                cleanupOldMessages(currentConversationId);
                
                Log.d(TAG, "Added user message to conversation: " + currentConversationId);
            } catch (Exception e) {
                Log.e(TAG, "Error adding user message", e);
            }
        });
    }
    
    /**
     * Add AI response to conversation
     */
    public void addAIResponse(String response, String languageCode, long responseTimeMs, String contextUsed) {
        if (currentConversationId == null) {
            Log.w(TAG, "No active conversation for AI response");
            return;
        }
        
        executor.execute(() -> {
            try {
                int sequenceNumber = database.chatHistoryDao().getNextSequenceNumber(currentConversationId);
                
                ChatHistory aiMessage = new ChatHistory(
                    currentConversationId, 
                    response, 
                    ChatHistory.TYPE_AI, 
                    sequenceNumber,
                    languageCode
                );
                aiMessage.setResponseTimeMs(responseTimeMs);
                aiMessage.setContextUsed(contextUsed);
                
                database.chatHistoryDao().insertMessage(aiMessage);
                database.chatConversationDao().incrementMessageCount(currentConversationId, System.currentTimeMillis());
                
                Log.d(TAG, "Added AI response to conversation: " + currentConversationId);
            } catch (Exception e) {
                Log.e(TAG, "Error adding AI response", e);
            }
        });
    }
    
    /**
     * Get conversation context for AI (last N Q&A pairs)
     */
    public String getConversationContext() {
        if (currentConversationId == null) {
            return "";
        }
        
        try {
            // Get last N*2 messages (N pairs of user+AI messages)
            int maxContextPairs = getMaxContextPairs();
            List<ChatHistory> recentMessages = database.chatHistoryDao()
                .getLastQAPairsForConversation(currentConversationId, maxContextPairs * 2);
            
            if (recentMessages.isEmpty()) {
                return "";
            }
            
            // Reverse to get chronological order
            Collections.reverse(recentMessages);
            
            StringBuilder contextBuilder = new StringBuilder();
            contextBuilder.append("Previous conversation context:\n");
            
            // Group messages into Q&A pairs
            List<ChatHistory> userMessages = new ArrayList<>();
            List<ChatHistory> aiMessages = new ArrayList<>();
            
            for (ChatHistory message : recentMessages) {
                if (message.isUserMessage()) {
                    userMessages.add(message);
                } else if (message.isAIMessage()) {
                    aiMessages.add(message);
                }
            }
            
            // Build context string with Q&A pairs
            int pairCount = Math.min(userMessages.size(), aiMessages.size());
            for (int i = 0; i < pairCount && i < maxContextPairs; i++) {
                if (i < userMessages.size() && i < aiMessages.size()) {
                    contextBuilder.append("Q").append(i + 1).append(": ")
                        .append(userMessages.get(i).getMessageText()).append("\n");
                    contextBuilder.append("A").append(i + 1).append(": ")
                        .append(aiMessages.get(i).getMessageText()).append("\n");
                }
            }
            
            // Add any remaining user message (if no AI response yet)
            if (userMessages.size() > aiMessages.size()) {
                ChatHistory lastUserMessage = userMessages.get(userMessages.size() - 1);
                contextBuilder.append("Current Q: ").append(lastUserMessage.getMessageText()).append("\n");
            }
            
            contextBuilder.append("\nPlease respond considering this conversation history.\n");
            
            String context = contextBuilder.toString();
            Log.d(TAG, "Generated conversation context with " + pairCount + " Q&A pairs");
            return context;
            
        } catch (Exception e) {
            Log.e(TAG, "Error getting conversation context", e);
            return "";
        }
    }
    
    /**
     * Get current conversation ID
     */
    public String getCurrentConversationId() {
        return currentConversationId;
    }
    
    /**
     * End current conversation
     */
    public void endConversation() {
        if (currentConversationId != null) {
            executor.execute(() -> {
                database.chatConversationDao().deactivateAllConversations();
                Log.d(TAG, "Ended conversation: " + currentConversationId);
            });
            currentConversationId = null;
        }
    }

    /**
     * Clear current conversation memory (for clear chat functionality)
     */
    public void clearConversationMemory() {
        if (currentConversationId != null) {
            executor.execute(() -> {
                try {
                    // Delete all messages for current conversation
                    database.chatHistoryDao().deleteMessagesForConversation(currentConversationId);

                    // Deactivate current conversation
                    database.chatConversationDao().deactivateAllConversations();

                    Log.d(TAG, "Cleared conversation memory for: " + currentConversationId);
                } catch (Exception e) {
                    Log.e(TAG, "Error clearing conversation memory", e);
                }
            });
            currentConversationId = null;
        }
    }
    
    /**
     * Clean up old messages to maintain performance
     */
    private void cleanupOldMessages(String conversationId) {
        try {
            int messageCount = database.chatHistoryDao().getMessageCountForConversation(conversationId);
            if (messageCount > MAX_STORED_MESSAGES) {
                // Keep only the most recent messages
                database.chatHistoryDao().deleteOldMessagesForConversation(conversationId, MAX_STORED_MESSAGES);
                Log.d(TAG, "Cleaned up old messages for conversation: " + conversationId);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error cleaning up old messages", e);
        }
    }
    
    /**
     * Get conversation statistics
     */
    public ConversationStats getConversationStats() {
        if (currentConversationId == null) {
            return new ConversationStats();
        }
        
        try {
            int messageCount = database.chatHistoryDao().getMessageCountForConversation(currentConversationId);
            double avgResponseTime = database.chatHistoryDao().getAverageResponseTime(currentConversationId);
            
            return new ConversationStats(messageCount, avgResponseTime);
        } catch (Exception e) {
            Log.e(TAG, "Error getting conversation stats", e);
            return new ConversationStats();
        }
    }
    
    /**
     * Conversation statistics data class
     */
    public static class ConversationStats {
        public final int messageCount;
        public final double averageResponseTimeMs;
        
        public ConversationStats() {
            this.messageCount = 0;
            this.averageResponseTimeMs = 0.0;
        }
        
        public ConversationStats(int messageCount, double averageResponseTimeMs) {
            this.messageCount = messageCount;
            this.averageResponseTimeMs = averageResponseTimeMs;
        }
    }
    
    /**
     * Cleanup resources
     */
    public void cleanup() {
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
    }
}
