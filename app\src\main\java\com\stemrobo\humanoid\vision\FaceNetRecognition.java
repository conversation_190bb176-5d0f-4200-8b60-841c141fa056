package com.stemrobo.humanoid.vision;

import android.content.Context;
import android.content.SharedPreferences;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.RectF;
import android.preference.PreferenceManager;
import android.speech.tts.TextToSpeech;
import android.util.Log;

import com.google.mlkit.vision.face.Face;
import com.stemrobo.humanoid.database.Person;
import com.stemrobo.humanoid.database.PersonDatabase;
import com.stemrobo.humanoid.database.PersonEmbedding;

import org.tensorflow.lite.Interpreter;
import org.tensorflow.lite.support.common.ops.NormalizeOp;
import org.tensorflow.lite.support.image.ImageProcessor;
import org.tensorflow.lite.support.image.TensorImage;
import org.tensorflow.lite.support.image.ops.ResizeOp;

import java.io.IOException;
import java.io.InputStream;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.MappedByteBuffer;
import java.nio.channels.FileChannel;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * Real Face Recognition using FaceNet embeddings
 * Provides accurate person identification and differentiation
 */
public class FaceNetRecognition {
    
    private static final String TAG = "FaceNetRecognition";
    
    // MobileFaceNet model parameters
    private static final int INPUT_SIZE = 112;
    private static final int EMBEDDING_SIZE = 128; // MobileFaceNet embedding size

    // Enhanced recognition thresholds for better accuracy
    private static final float BASE_RECOGNITION_THRESHOLD = 0.90f; // Increased base threshold
    private static final float STRICT_RECOGNITION_THRESHOLD = 0.93f; // For multiple people scenarios
    private static final float MINIMUM_CONFIDENCE_GAP = 0.12f; // Increased gap requirement
    private static final float UNKNOWN_PERSON_THRESHOLD = 0.88f; // Below this = unknown person

    private static final String[] MODEL_FILES = {
        "models/MobileFaceNet.tflite",  // Try models folder first
        "models/mobile_facenet.tflite", // Alternative name in models folder
        "MobileFaceNet.tflite",         // Fallback to root assets
        "mobile_facenet.tflite"         // Alternative fallback
    };
    
    private final Context context;
    private final PersonDatabase database;
    private TextToSpeech textToSpeech;
    
    // TensorFlow Lite components
    private Interpreter interpreter;
    private ImageProcessor imageProcessor;
    
    // Face tracking and recognition
    private final Map<Integer, String> trackedFaces = new HashMap<>(); // trackingId -> personName
    private final Map<Integer, Long> lastGreetingTime = new HashMap<>(); // trackingId -> timestamp
    private static final long GREETING_COOLDOWN = 10000; // 10 seconds
    
    // Recognition callback
    private RecognitionCallback callback;
    
    // Multi-photo registration state
    private boolean isMultiPhotoRegistrationActive = false;
    private String currentRegistrationName = "";
    private MultiPhotoRegistrationCallback multiPhotoCallback;
    private List<float[]> collectedEmbeddings = new ArrayList<>();
    private int targetPhotoCount = 5; // Number of photos to collect
    private long lastPhotoTime = 0;
    private static final long PHOTO_INTERVAL_MS = 1500; // 1.5 seconds between photos
    
    public interface RecognitionCallback {
        void onPersonRecognized(String personName, Face face, float confidence);
        void onUnknownPersonDetected(Face face);
        void onPersonRegistered(String personName, boolean success);
        void onRecognitionError(Exception error);
    }
    
    /**
     * Callback interface for multi-photo registration
     */
    public interface MultiPhotoRegistrationCallback {
        void onPhotoNeeded(int photoNumber, int totalPhotos);
        void onRegistrationComplete(boolean success, String message);
        void onProgress(String message);
    }
    
    public FaceNetRecognition(Context context) {
        this.context = context;
        this.database = PersonDatabase.getInstance(context);
        
        initializeTextToSpeech();
        initializeFaceNetModel();
        setupImageProcessor();
    }
    
    /**
     * Initialize Text-to-Speech for greetings
     */
    private void initializeTextToSpeech() {
        textToSpeech = new TextToSpeech(context, status -> {
            if (status == TextToSpeech.SUCCESS) {
                textToSpeech.setLanguage(Locale.US);

                // Configure voice gender based on settings
                configureVoiceGender();

                Log.d(TAG, "Text-to-Speech initialized successfully");
            } else {
                Log.e(TAG, "Text-to-Speech initialization failed");
            }
        });
    }

    /**
     * Configure voice gender based on user settings with enhanced pitch control
     */
    private void configureVoiceGender() {
        if (textToSpeech != null && context != null) {
            try {
                SharedPreferences sharedPreferences = PreferenceManager.getDefaultSharedPreferences(context);
                String voiceGender = sharedPreferences.getString("voice_gender", "female");

                if ("male".equals(voiceGender)) {
                    // Male voice configuration - much more aggressive settings
                    textToSpeech.setPitch(0.6f);  // Much lower pitch for deeper male voice
                    textToSpeech.setSpeechRate(0.85f); // Significantly slower
                    Log.d(TAG, "Face recognition MALE voice configured (pitch: 0.6, rate: 0.85)");
                } else {
                    // Female voice configuration - higher and faster
                    textToSpeech.setPitch(1.2f);  // Higher pitch for female voice
                    textToSpeech.setSpeechRate(1.05f); // Slightly faster
                    Log.d(TAG, "Face recognition FEMALE voice configured (pitch: 1.2, rate: 1.05)");
                }
            } catch (Exception e) {
                Log.e(TAG, "Error configuring voice gender: " + e.getMessage());
            }
        }
    }
    
    /**
     * Initialize FaceNet TensorFlow Lite model
     */
    private void initializeFaceNetModel() {
        Log.d(TAG, "Starting FaceNet model initialization...");
        
        // Try loading models in order of preference
        for (String modelFile : MODEL_FILES) {
            try {
                Log.d(TAG, "Attempting to load model: " + modelFile);
                MappedByteBuffer modelBuffer = loadModelFile(modelFile);
                
                // Configure interpreter options
                Interpreter.Options options = new Interpreter.Options();
                options.setNumThreads(4);
                options.setUseNNAPI(false); // Disable NNAPI for better compatibility
                
                // Create interpreter
                interpreter = new Interpreter(modelBuffer, options);
                
                Log.d(TAG, "FaceNet model loaded successfully: " + modelFile);
                return; // Success, exit the loop
                
            } catch (IOException e) {
                Log.w(TAG, "Failed to load model: " + modelFile + ", trying next...", e);
                if (interpreter != null) {
                    interpreter.close();
                    interpreter = null;
                }
            }
        }
        
        // If all models failed, create dummy interpreter
        Log.e(TAG, "Failed to load any FaceNet model");
        createDummyInterpreter();
    }
    
    /**
     * Load TensorFlow Lite model from assets
     */
    private MappedByteBuffer loadModelFile(String modelFile) throws IOException {
        try (InputStream inputStream = context.getAssets().open(modelFile)) {
            // Read the entire file into a byte array
            byte[] modelBytes = new byte[inputStream.available()];
            inputStream.read(modelBytes);

            // Create a ByteBuffer and wrap it as MappedByteBuffer
            ByteBuffer byteBuffer = ByteBuffer.allocateDirect(modelBytes.length);
            byteBuffer.order(ByteOrder.nativeOrder());
            byteBuffer.put(modelBytes);
            byteBuffer.flip();

            return (MappedByteBuffer) byteBuffer;

        } catch (IOException e) {
            Log.w(TAG, "Model file not found in assets: " + modelFile, e);
            throw e;
        }
    }
    
    /**
     * Create dummy interpreter when model is not available
     */
    private void createDummyInterpreter() {
        Log.w(TAG, "Using dummy interpreter - face recognition will use simplified matching");
        interpreter = null;
    }
    
    /**
     * Setup image processor for MobileFaceNet input
     */
    private void setupImageProcessor() {
        imageProcessor = new ImageProcessor.Builder()
            .add(new ResizeOp(INPUT_SIZE, INPUT_SIZE, ResizeOp.ResizeMethod.BILINEAR))
            .add(new NormalizeOp(127.5f, 127.5f)) // Normalize to [-1, 1] for MobileFaceNet
            .build();
    }
    
    /**
     * Set recognition callback
     */
    public void setCallback(RecognitionCallback callback) {
        this.callback = callback;
    }
    
    /**
     * Process detected faces for recognition
     */
    public void processFaces(List<Face> faces, Bitmap cameraBitmap) {
        // Periodically clear stale tracked faces (every 50 calls)
        if (Math.random() < 0.02) { // 2% chance = roughly every 50 calls
            clearStaleTrackedFaces();
        }
        
        for (Face face : faces) {
            processSingleFace(face, cameraBitmap);
        }
    }
    
    /**
     * Process a single detected face
     */
    private void processSingleFace(Face face, Bitmap cameraBitmap) {
        Integer trackingId = face.getTrackingId();
        
        if (trackingId == null) {
            // No tracking ID, always process for recognition
            recognizeFace(face, cameraBitmap, null);
            return;
        }
        
        // Check if we have a cached recognition for this tracking ID
        String cachedName = trackedFaces.get(trackingId);
        if (cachedName != null) {
            // We have a cached recognition, but verify it periodically to prevent false associations
            long currentTime = System.currentTimeMillis();
            Long lastVerification = lastGreetingTime.get(trackingId);
            
            // Re-verify every 5 seconds to ensure accuracy
            if (lastVerification == null || (currentTime - lastVerification) > 5000) {
                Log.d(TAG, "Re-verifying cached recognition for tracking ID: " + trackingId + " (cached: " + cachedName + ")");
                recognizeFace(face, cameraBitmap, trackingId);
            } else {
                // Use cached result but don't greet again
                if (callback != null) {
                    callback.onPersonRecognized(cachedName, face, 0.9f); // Use high confidence for cached results
                }
            }
        } else {
            // No cached recognition, process normally
            recognizeFace(face, cameraBitmap, trackingId);
        }
    }
    
    /**
     * Recognize a face using FaceNet embeddings
     */
    private void recognizeFace(Face face, Bitmap cameraBitmap, Integer trackingId) {
        try {
            // Extract face from camera bitmap
            Bitmap faceBitmap = extractFaceBitmap(face, cameraBitmap);
            if (faceBitmap == null) {
                processUnknownFace(face, cameraBitmap);
                return;
            }
            
            // Handle multi-photo registration if active
            if (isMultiPhotoRegistrationActive) {
                handleMultiPhotoRegistration(face, faceBitmap);
                return;
            }
            
            // Get face embedding
            float[] faceEmbedding = getFaceEmbedding(faceBitmap);
            if (faceEmbedding == null) {
                processUnknownFace(face, cameraBitmap);
                return;
            }
            
            // Debug: Analyze all stored persons for this embedding
            debugAnalyzeStoredPersons(faceEmbedding);
            
            // Compare with stored embeddings
            RecognitionResult result = compareWithStoredFaces(faceEmbedding);
            
            if (result != null && result.confidence > getDynamicRecognitionThreshold()) { // Cosine similarity: higher is better
                // Known person - but only cache if confidence is very high and consistent
                if (trackingId != null && result.confidence > 0.92f) { // Very high threshold for caching
                    String previousName = trackedFaces.get(trackingId);
                    if (previousName == null) {
                        // First recognition for this tracking ID
                        trackedFaces.put(trackingId, result.personName);
                        Log.d(TAG, "Cached new recognition: " + result.personName + " for tracking ID: " + trackingId);
                    } else if (previousName.equals(result.personName)) {
                        // Consistent recognition - update timestamp
                        lastGreetingTime.put(trackingId, System.currentTimeMillis());
                        Log.d(TAG, "Confirmed cached recognition: " + result.personName + " for tracking ID: " + trackingId);
                    } else {
                        // Different person detected for same tracking ID - this is suspicious
                        Log.w(TAG, "TRACKING CONFLICT: was " + previousName + ", now " + result.personName + " (conf: " + result.confidence + ")");
                        // Clear the cache and don't cache this result - force re-recognition
                        trackedFaces.remove(trackingId);
                        lastGreetingTime.remove(trackingId);
                        // Don't cache this conflicting result
                    }
                } else if (trackingId != null && result.confidence <= 0.90f) {
                    // Lower confidence - check if it conflicts with cached result
                    String previousName = trackedFaces.get(trackingId);
                    if (previousName != null && !previousName.equals(result.personName)) {
                        Log.w(TAG, "Low confidence conflict detected - ignoring result. Cached: " + previousName + ", detected: " + result.personName);
                        return; // Don't process this low-confidence conflicting result
                    }
                }
                
                if (callback != null) {
                    callback.onPersonRecognized(result.personName, face, result.confidence);
                }
                
                // Greet the person (with cooldown)
                if (trackingId != null && shouldGreetPerson(trackingId)) {
                    greetPerson(result.personName);
                    lastGreetingTime.put(trackingId, System.currentTimeMillis());
                } else if (trackingId == null) {
                    // No tracking ID, greet immediately but don't cache
                    greetPerson(result.personName);
                }
                
                Log.d(TAG, "Recognized: " + result.personName + " (confidence: " + result.confidence + ")");
                
            } else {
                // Unknown person or low confidence
                if (result != null) {
                    Log.d(TAG, "Low confidence recognition: " + result.personName + " (confidence: " + result.confidence + ")");
                }
                processUnknownFace(face, cameraBitmap);
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error recognizing face", e);
            if (callback != null) {
                callback.onRecognitionError(e);
            }
        }
    }
    
    /**
     * Extract face bitmap from camera frame
     */
    private Bitmap extractFaceBitmap(Face face, Bitmap cameraBitmap) {
        try {
            Rect boundingBox = face.getBoundingBox();
            
            // Add padding around face
            int padding = 20;
            int left = Math.max(0, boundingBox.left - padding);
            int top = Math.max(0, boundingBox.top - padding);
            int right = Math.min(cameraBitmap.getWidth(), boundingBox.right + padding);
            int bottom = Math.min(cameraBitmap.getHeight(), boundingBox.bottom + padding);
            
            // Extract face region
            Bitmap faceBitmap = Bitmap.createBitmap(cameraBitmap, left, top, right - left, bottom - top);
            
            // Resize to model input size
            return Bitmap.createScaledBitmap(faceBitmap, INPUT_SIZE, INPUT_SIZE, true);
            
        } catch (Exception e) {
            Log.e(TAG, "Error extracting face bitmap", e);
            return null;
        }
    }
    
    /**
     * Get face embedding using FaceNet model
     */
    private float[] getFaceEmbedding(Bitmap faceBitmap) {
        if (interpreter == null) {
            Log.w(TAG, "FaceNet model not loaded, using simplified features");
            return getSimplifiedFeatures(faceBitmap);
        }

        try {
            // Prepare input tensor
            TensorImage tensorImage = new TensorImage();
            tensorImage.load(faceBitmap);
            tensorImage = imageProcessor.process(tensorImage);

            // Prepare output tensor
            float[][] output = new float[1][EMBEDDING_SIZE];

            // Run inference
            interpreter.run(tensorImage.getBuffer(), output);

            // Validate output
            if (output[0] == null || output[0].length != EMBEDDING_SIZE) {
                Log.e(TAG, "Invalid embedding output from model");
                return getSimplifiedFeatures(faceBitmap);
            }

            // Normalize embedding
            float[] normalized = normalizeEmbedding(output[0]);
            Log.d(TAG, "Generated FaceNet embedding with " + normalized.length + " dimensions");
            return normalized;

        } catch (Exception e) {
            Log.e(TAG, "Error getting face embedding", e);
            return getSimplifiedFeatures(faceBitmap);
        }
    }
    
    /**
     * Simplified feature extraction when FaceNet model is not available
     */
    private float[] getSimplifiedFeatures(Bitmap faceBitmap) {
        // Extract basic visual features as a fallback
        float[] features = new float[64]; // Smaller feature vector
        
        // Calculate basic statistics from face bitmap
        int[] pixels = new int[faceBitmap.getWidth() * faceBitmap.getHeight()];
        faceBitmap.getPixels(pixels, 0, faceBitmap.getWidth(), 0, 0, faceBitmap.getWidth(), faceBitmap.getHeight());
        
        // Extract color and texture features
        for (int i = 0; i < Math.min(features.length, pixels.length / 1000); i++) {
            int pixel = pixels[i * 1000];
            features[i] = ((pixel >> 16) & 0xFF) + ((pixel >> 8) & 0xFF) + (pixel & 0xFF);
        }
        
        return normalizeEmbedding(features);
    }
    
    /**
     * Normalize embedding vector
     */
    private float[] normalizeEmbedding(float[] embedding) {
        float norm = 0.0f;
        for (float value : embedding) {
            norm += value * value;
        }
        norm = (float) Math.sqrt(norm);
        
        if (norm > 0) {
            for (int i = 0; i < embedding.length; i++) {
                embedding[i] /= norm;
            }
        }
        
        return embedding;
    }
    
    /**
     * Compare face embedding with stored faces using cosine similarity
     */
    private RecognitionResult compareWithStoredFaces(float[] faceEmbedding) {
        try {
            List<Person> persons = database.personDao().getAllPersons();
            Log.d(TAG, "Comparing with " + persons.size() + " stored persons");

            RecognitionResult bestMatch = null;
            float bestSimilarity = -1.0f; // Cosine similarity ranges from -1 to 1
            float secondBestSimilarity = -1.0f; // Track second best for confidence gap

            for (Person person : persons) {
                // Try to use multiple embeddings if available
                List<PersonEmbedding> personEmbeddings = null;
                try {
                    personEmbeddings = database.personEmbeddingDao().getEmbeddingsForPersonOrderedByQuality(person.uid);
                } catch (Exception e) {
                    Log.w(TAG, "Could not load multiple embeddings for " + person.name + ", using primary embedding");
                }
                
                float personBestSimilarity = -1.0f;
                
                if (personEmbeddings != null && !personEmbeddings.isEmpty()) {
                    // Use multiple embeddings for better accuracy
                    Log.d(TAG, "Comparing with " + personEmbeddings.size() + " embeddings for " + person.name);
                    
                    float totalSimilarity = 0;
                    int validEmbeddings = 0;
                    
                    for (PersonEmbedding personEmbedding : personEmbeddings) {
                        float[] storedEmbedding = parseEmbedding(personEmbedding.embeddingJson);
                        if (storedEmbedding != null && storedEmbedding.length == faceEmbedding.length) {
                            float similarity = calculateCosineSimilarity(faceEmbedding, storedEmbedding);
                            totalSimilarity += similarity;
                            validEmbeddings++;
                            
                            // Track the best individual similarity
                            if (similarity > personBestSimilarity) {
                                personBestSimilarity = similarity;
                            }
                        }
                    }
                    
                    if (validEmbeddings > 0) {
                        // Use weighted average: 80% best match + 20% average of all matches (more weight on best)
                        float averageSimilarity = totalSimilarity / validEmbeddings;
                        float finalSimilarity = (personBestSimilarity * 0.8f) + (averageSimilarity * 0.2f);
                        
                        // Additional requirement: if we have multiple embeddings, require majority to be above 0.65
                        boolean hasStrongMatches = true;
                        if (validEmbeddings >= 2) {
                            int strongMatches = 0;
                            for (PersonEmbedding personEmbedding : personEmbeddings) {
                                float[] storedEmbedding = parseEmbedding(personEmbedding.embeddingJson);
                                if (storedEmbedding != null && storedEmbedding.length == faceEmbedding.length) {
                                    float similarity = calculateCosineSimilarity(faceEmbedding, storedEmbedding);
                                    if (similarity > 0.65f) {
                                        strongMatches++;
                                    }
                                }
                            }
                            // Require at least half of the embeddings to be strong matches
                            hasStrongMatches = strongMatches >= Math.max(1, validEmbeddings / 2);
                        }
                        
                        Log.d(TAG, String.format("Person %s: best=%.3f, avg=%.3f, final=%.3f (%d embeddings, strong=%s)", 
                            person.name, personBestSimilarity, averageSimilarity, finalSimilarity, validEmbeddings, hasStrongMatches));
                        
                        if (finalSimilarity > bestSimilarity && hasStrongMatches) {
                            secondBestSimilarity = bestSimilarity; // Update second best
                            bestSimilarity = finalSimilarity;
                            bestMatch = new RecognitionResult(person.name, finalSimilarity);
                        } else if (finalSimilarity > secondBestSimilarity) {
                            secondBestSimilarity = finalSimilarity; // Track second best
                        }
                    }
                } else {
                    // Fallback to primary embedding
                    float[] storedEmbedding = parseEmbedding(person.embeddingJson);
                    if (storedEmbedding != null) {
                        // Validate embedding dimensions
                        if (storedEmbedding.length != faceEmbedding.length) {
                            Log.w(TAG, "Embedding dimension mismatch for " + person.name +
                                  ": stored=" + storedEmbedding.length + ", current=" + faceEmbedding.length);
                            continue;
                        }

                        float similarity = calculateCosineSimilarity(faceEmbedding, storedEmbedding);
                        Log.d(TAG, "Similarity to " + person.name + " (primary): " + similarity);

                        if (similarity > bestSimilarity) {
                            secondBestSimilarity = bestSimilarity; // Update second best
                            bestSimilarity = similarity;
                            bestMatch = new RecognitionResult(person.name, similarity);
                        } else if (similarity > secondBestSimilarity) {
                            secondBestSimilarity = similarity; // Track second best
                        }
                    } else {
                        Log.w(TAG, "Failed to parse embedding for " + person.name);
                    }
                }
            }

            if (bestMatch != null) {
                // If only one person is registered, don't require confidence gap
                if (persons.size() == 1) {
                    Log.d(TAG, String.format("Single person registered: %s (%.3f)", 
                        bestMatch.personName, bestSimilarity));
                    return bestMatch;
                }
                
                // Require a minimum confidence gap between best and second best to avoid confusion
                float confidenceGap = bestSimilarity - secondBestSimilarity;
                float minimumGap = MINIMUM_CONFIDENCE_GAP; // Enhanced gap requirement for better accuracy
                
                Log.d(TAG, String.format("Best match: %s (%.3f), Second best: %.3f, Gap: %.3f", 
                    bestMatch.personName, bestSimilarity, secondBestSimilarity, confidenceGap));
                
                if (confidenceGap >= minimumGap) {
                    Log.d(TAG, "Confident match with sufficient gap");
                    return bestMatch;
                } else {
                    Log.d(TAG, "Rejecting match due to insufficient confidence gap");
                    return null;
                }
            } else {
                Log.d(TAG, "No matches found");
                return null;
            }

        } catch (Exception e) {
            Log.e(TAG, "Error comparing with stored faces", e);
            return null;
        }
    }
    
    /**
     * Calculate cosine similarity between two embeddings
     * Higher similarity means more similar faces (range: -1 to 1)
     */
    private float calculateCosineSimilarity(float[] embedding1, float[] embedding2) {
        if (embedding1.length != embedding2.length) {
            return -1.0f; // Lowest possible similarity
        }

        float dotProduct = 0.0f;
        float norm1 = 0.0f;
        float norm2 = 0.0f;

        for (int i = 0; i < embedding1.length; i++) {
            dotProduct += embedding1[i] * embedding2[i];
            norm1 += embedding1[i] * embedding1[i];
            norm2 += embedding2[i] * embedding2[i];
        }

        norm1 = (float) Math.sqrt(norm1);
        norm2 = (float) Math.sqrt(norm2);

        if (norm1 == 0.0f || norm2 == 0.0f) {
            return 0.0f;
        }

        return dotProduct / (norm1 * norm2);
    }
    
    /**
     * Parse embedding from JSON string
     */
    private float[] parseEmbedding(String embeddingJson) {
        try {
            // Simple JSON parsing for float array
            if (embeddingJson == null || embeddingJson.equals("{}")) {
                return null;
            }
            
            // Remove brackets and split by comma
            String[] values = embeddingJson.replace("[", "").replace("]", "").split(",");
            float[] embedding = new float[values.length];
            
            for (int i = 0; i < values.length; i++) {
                embedding[i] = Float.parseFloat(values[i].trim());
            }
            
            return embedding;
            
        } catch (Exception e) {
            Log.e(TAG, "Error parsing embedding JSON", e);
            return null;
        }
    }
    
    /**
     * Convert embedding to JSON string
     */
    private String embeddingToJson(float[] embedding) {
        StringBuilder json = new StringBuilder("[");
        for (int i = 0; i < embedding.length; i++) {
            json.append(embedding[i]);
            if (i < embedding.length - 1) {
                json.append(",");
            }
        }
        json.append("]");
        return json.toString();
    }
    
    /**
     * Get dynamic recognition threshold based on number of registered people
     */
    private float getDynamicRecognitionThreshold() {
        try {
            List<Person> persons = database.personDao().getAllPersons();
            int personCount = persons.size();

            if (personCount == 0) {
                return BASE_RECOGNITION_THRESHOLD;
            } else if (personCount == 1) {
                return BASE_RECOGNITION_THRESHOLD; // Single person - use base threshold
            } else {
                return STRICT_RECOGNITION_THRESHOLD; // Multiple people - use stricter threshold
            }
        } catch (Exception e) {
            Log.e(TAG, "Error getting person count for dynamic threshold", e);
            return STRICT_RECOGNITION_THRESHOLD; // Default to stricter threshold on error
        }
    }

    /**
     * Enhanced unknown person detection with confidence scoring
     */
    private void processUnknownFace(Face face, Bitmap cameraBitmap) {
        // Try to get the best match confidence even if below threshold
        float[] faceEmbedding = null;
        try {
            Bitmap faceBitmap = extractFaceBitmap(face, cameraBitmap);
            if (faceBitmap != null) {
                faceEmbedding = getFaceEmbedding(faceBitmap);
            }
        } catch (Exception e) {
            Log.w(TAG, "Error extracting embedding for unknown face analysis", e);
        }

        float bestConfidence = 0.0f;
        String closestMatch = "None";

        if (faceEmbedding != null) {
            RecognitionResult result = compareWithStoredFaces(faceEmbedding);
            if (result != null) {
                bestConfidence = result.confidence;
                closestMatch = result.personName;

                // Log detailed analysis for debugging
                Log.d(TAG, String.format("Unknown face analysis - Closest match: %s (%.3f), Threshold: %.3f, Gap needed: %.3f",
                    closestMatch, bestConfidence, getDynamicRecognitionThreshold(), MINIMUM_CONFIDENCE_GAP));

                // Check if this is a borderline case that might need attention
                if (bestConfidence > UNKNOWN_PERSON_THRESHOLD) {
                    Log.w(TAG, String.format("BORDERLINE CASE: Face has %.1f%% similarity to %s but below recognition threshold",
                        bestConfidence * 100, closestMatch));
                }
            }
        }

        if (callback != null) {
            callback.onUnknownPersonDetected(face);
        }

        // Enhanced logging for debugging
        Log.d(TAG, String.format("Unknown person detected - Best match: %s (confidence: %.3f)",
            closestMatch, bestConfidence));
    }
    
    /**
     * Register a new person with their face
     */
    public void registerPerson(String name, Face face, Bitmap cameraBitmap) {
        try {
            // Extract face bitmap
            Bitmap faceBitmap = extractFaceBitmap(face, cameraBitmap);
            if (faceBitmap == null) {
                if (callback != null) {
                    callback.onPersonRegistered(name, false);
                }
                return;
            }
            
            // Get face embedding
            float[] faceEmbedding = getFaceEmbedding(faceBitmap);
            if (faceEmbedding == null) {
                if (callback != null) {
                    callback.onPersonRegistered(name, false);
                }
                return;
            }
            
            // Create person record
            Person person = new Person();
            person.name = name;
            person.embeddingJson = embeddingToJson(faceEmbedding);
            person.createdTimestamp = System.currentTimeMillis();
            person.recognitionCount = 0;
            person.lastSeenTimestamp = 0;
            person.confidenceThreshold = BASE_RECOGNITION_THRESHOLD;
            
            // Save to database
            long id = database.personDao().insertPerson(person);
            
            boolean success = id > 0;
            
            if (success) {
                // Associate with current tracking ID if available
                Integer trackingId = face.getTrackingId();
                if (trackingId != null) {
                    trackedFaces.put(trackingId, name);
                }
                
                Log.d(TAG, "Person registered successfully: " + name + " (ID: " + id + ")");
            } else {
                Log.e(TAG, "Failed to register person: " + name);
            }
            
            if (callback != null) {
                callback.onPersonRegistered(name, success);
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error registering person: " + name, e);
            if (callback != null) {
                callback.onPersonRegistered(name, false);
            }
        }
    }
    
    /**
     * Check if we should greet a person (cooldown check)
     */
    private boolean shouldGreetPerson(Integer trackingId) {
        Long lastGreeting = lastGreetingTime.get(trackingId);
        if (lastGreeting == null) {
            return true;
        }
        
        return System.currentTimeMillis() - lastGreeting > GREETING_COOLDOWN;
    }
    
    /**
     * Greet a recognized person
     */
    private void greetPerson(String personName) {
        String greeting = "Hi " + personName + ", how are you? I am STEM-Xpert robot.";
        
        if (textToSpeech != null) {
            textToSpeech.speak(greeting, TextToSpeech.QUEUE_FLUSH, null, null);
            Log.d(TAG, "Greeting: " + greeting);
        }
    }
    
    /**
     * Get all registered persons
     */
    public List<Person> getAllPersons() {
        try {
            return database.personDao().getAllPersons();
        } catch (Exception e) {
            Log.e(TAG, "Error getting all persons", e);
            return null;
        }
    }
    
    /**
     * Delete a person from database
     */
    public boolean deletePerson(int personId) {
        try {
            database.personDao().deletePersonById(personId);
            
            // Remove from tracked faces
            trackedFaces.entrySet().removeIf(entry -> {
                // This is simplified - in a real implementation,
                // we'd need to match by person ID
                return false;
            });
            
            Log.d(TAG, "Person deleted: " + personId);
            return true;
            
        } catch (Exception e) {
            Log.e(TAG, "Error deleting person: " + personId, e);
            return false;
        }
    }
    
    /**
     * Clear all tracked faces (useful when switching cameras or fixing recognition issues)
     */
    public void clearTrackedFaces() {
        trackedFaces.clear();
        lastGreetingTime.clear();
        Log.d(TAG, "Cleared all tracked faces - this will force re-recognition of all faces");
    }
    
    /**
     * Force clear cache for a specific tracking ID (useful when recognition is wrong)
     */
    public void clearTrackingId(Integer trackingId) {
        if (trackingId != null) {
            String removedName = trackedFaces.remove(trackingId);
            lastGreetingTime.remove(trackingId);
            Log.d(TAG, "Cleared tracking ID " + trackingId + " (was: " + removedName + ")");
        }
    }
    
    /**
     * Get recognition statistics
     */
    public String getRecognitionStats() {
        try {
            List<Person> persons = database.personDao().getAllPersons();
            StringBuilder stats = new StringBuilder();
            stats.append(String.format("Registered: %d persons, Tracked: %d faces, Threshold: %.2f\n",
                persons.size(), trackedFaces.size(), BASE_RECOGNITION_THRESHOLD));
            
            // Add tracked faces info
            if (!trackedFaces.isEmpty()) {
                stats.append("Tracked faces: ");
                for (Map.Entry<Integer, String> entry : trackedFaces.entrySet()) {
                    stats.append(String.format("[ID:%d->%s] ", entry.getKey(), entry.getValue()));
                }
                stats.append("\n");
            }
            
            // Add registered persons info
            if (!persons.isEmpty()) {
                stats.append("Registered persons: ");
                for (Person person : persons) {
                    stats.append(String.format("[%s] ", person.name));
                }
            }
            
            return stats.toString();
        } catch (Exception e) {
            return "Error getting stats: " + e.getMessage();
        }
    }
    
    /**
     * Force re-recognition of all faces (clears cache)
     */
    public void forceReRecognition() {
        clearTrackedFaces();
        Log.d(TAG, "Forced re-recognition - all faces will be re-evaluated");
    }
    
    /**
     * Clear stale tracked faces (faces not seen recently)
     */
    public void clearStaleTrackedFaces() {
        long currentTime = System.currentTimeMillis();
        long staleThreshold = 30000; // 30 seconds
        
        trackedFaces.entrySet().removeIf(entry -> {
            Long lastSeen = lastGreetingTime.get(entry.getKey());
            return lastSeen != null && (currentTime - lastSeen) > staleThreshold;
        });
        
        lastGreetingTime.entrySet().removeIf(entry -> 
            (currentTime - entry.getValue()) > staleThreshold);
        
        Log.d(TAG, "Cleared stale tracked faces");
    }

    /**
     * Clear all registered persons from database (for debugging)
     */
    public void clearAllPersons() {
        try {
            database.personDao().clearAllPersons();
            if (database.personEmbeddingDao() != null) {
                database.personEmbeddingDao().deleteAllEmbeddings();
            }
            clearTrackedFaces();
            Log.d(TAG, "Cleared all registered persons from database");
        } catch (Exception e) {
            Log.e(TAG, "Error clearing all persons", e);
        }
    }
    
    /**
     * Reset face recognition system completely
     */
    public void resetFaceRecognitionSystem() {
        Log.d(TAG, "=== RESETTING FACE RECOGNITION SYSTEM ===");
        
        // Clear all data
        clearAllPersons();
        
        // Reset tracking
        trackedFaces.clear();
        lastGreetingTime.clear();
        
        // Stop any ongoing registration
        stopMultiPhotoRegistration();
        
        Log.d(TAG, "Face recognition system reset complete. Please re-register all persons.");
        Log.d(TAG, "IMPORTANT: For best results:");
        Log.d(TAG, "1. Register each person with 3-5 different photos");
        Log.d(TAG, "2. Use good lighting and clear face visibility");
        Log.d(TAG, "3. Capture different angles and expressions");
        Log.d(TAG, "4. Ensure only one person per registration session");
    }
    
    /**
     * Debug method to analyze all stored persons and their similarities
     */
    public void debugAnalyzeStoredPersons(float[] testEmbedding) {
        try {
            List<Person> persons = database.personDao().getAllPersons();
            Log.d(TAG, "=== DEBUG: Analyzing " + persons.size() + " stored persons ===");
            
            for (Person person : persons) {
                Log.d(TAG, "--- Person: " + person.name + " (ID: " + person.uid + ") ---");
                
                // Check multiple embeddings if available
                List<PersonEmbedding> personEmbeddings = null;
                try {
                    personEmbeddings = database.personEmbeddingDao().getEmbeddingsForPersonOrderedByQuality(person.uid);
                    Log.d(TAG, "  Multiple embeddings: " + personEmbeddings.size());
                } catch (Exception e) {
                    Log.d(TAG, "  Using primary embedding only");
                }
                
                if (personEmbeddings != null && !personEmbeddings.isEmpty()) {
                    for (int i = 0; i < personEmbeddings.size(); i++) {
                        PersonEmbedding pe = personEmbeddings.get(i);
                        float[] storedEmbedding = parseEmbedding(pe.embeddingJson);
                        if (storedEmbedding != null && storedEmbedding.length == testEmbedding.length) {
                            float similarity = calculateCosineSimilarity(testEmbedding, storedEmbedding);
                            Log.d(TAG, "    Embedding " + (i+1) + ": similarity=" + String.format("%.4f", similarity) + 
                                      ", quality=" + pe.qualityScore);
                        }
                    }
                } else {
                    // Primary embedding
                    float[] storedEmbedding = parseEmbedding(person.embeddingJson);
                    if (storedEmbedding != null && storedEmbedding.length == testEmbedding.length) {
                        float similarity = calculateCosineSimilarity(testEmbedding, storedEmbedding);
                        Log.d(TAG, "    Primary embedding: similarity=" + String.format("%.4f", similarity));
                    }
                }
            }
            Log.d(TAG, "=== END DEBUG ANALYSIS ===");
        } catch (Exception e) {
            Log.e(TAG, "Error in debug analysis", e);
        }
    }
    
    /**
     * Start multi-photo registration process
     */
    public void startMultiPhotoRegistration(String personName, MultiPhotoRegistrationCallback callback) {
        if (isMultiPhotoRegistrationActive) {
            callback.onRegistrationComplete(false, "Registration already in progress");
            return;
        }
        
        this.isMultiPhotoRegistrationActive = true;
        this.currentRegistrationName = personName;
        this.multiPhotoCallback = callback;
        this.collectedEmbeddings.clear();
        this.lastPhotoTime = 0;
        
        Log.d(TAG, "Starting multi-photo registration for: " + personName);
        callback.onProgress("Starting registration for " + personName);
        callback.onPhotoNeeded(1, targetPhotoCount);
    }
    
    /**
     * Stop multi-photo registration process
     */
    public void stopMultiPhotoRegistration() {
        this.isMultiPhotoRegistrationActive = false;
        this.currentRegistrationName = "";
        this.multiPhotoCallback = null;
        this.collectedEmbeddings.clear();
        this.lastPhotoTime = 0;
    }
    
    /**
     * Handle multi-photo registration process
     */
    private void handleMultiPhotoRegistration(Face face, Bitmap faceBitmap) {
        long currentTime = System.currentTimeMillis();
        
        // Check if enough time has passed since last photo
        if (currentTime - lastPhotoTime < PHOTO_INTERVAL_MS) {
            return; // Too soon for next photo
        }
        
        // Get face embedding
        float[] faceEmbedding = getFaceEmbedding(faceBitmap);
        if (faceEmbedding == null) {
            if (multiPhotoCallback != null) {
                multiPhotoCallback.onProgress("Failed to extract face features, please try again");
            }
            return;
        }
        
        // Check quality of the embedding (face should be clear and well-lit)
        float quality = calculateEmbeddingQuality(faceEmbedding, face);
        if (quality < 0.6f) { // Increased minimum quality threshold
            if (multiPhotoCallback != null) {
                multiPhotoCallback.onProgress("Photo quality too low, please ensure good lighting and clear face");
            }
            return;
        }

        // Check diversity - ensure this embedding is sufficiently different from existing ones
        if (!collectedEmbeddings.isEmpty()) {
            boolean isDiverse = checkEmbeddingDiversity(faceEmbedding, collectedEmbeddings);
            if (!isDiverse) {
                if (multiPhotoCallback != null) {
                    multiPhotoCallback.onProgress("Photo too similar to previous ones, please change angle or expression");
                }
                return;
            }
        }
        
        // Add embedding to collection
        collectedEmbeddings.add(faceEmbedding);
        lastPhotoTime = currentTime;
        
        Log.d(TAG, String.format("Collected photo %d/%d for %s (quality: %.2f)", 
            collectedEmbeddings.size(), targetPhotoCount, currentRegistrationName, quality));
        
        if (multiPhotoCallback != null) {
            if (collectedEmbeddings.size() < targetPhotoCount) {
                // Need more photos
                multiPhotoCallback.onProgress(String.format("Photo %d captured (quality: %.1f%%). Move slightly for next photo.", 
                    collectedEmbeddings.size(), quality * 100));
                multiPhotoCallback.onPhotoNeeded(collectedEmbeddings.size() + 1, targetPhotoCount);
            } else {
                // All photos collected, process registration
                completeMultiPhotoRegistration();
            }
        }
    }
    
    /**
     * Check if a new embedding is sufficiently diverse from existing ones
     */
    private boolean checkEmbeddingDiversity(float[] newEmbedding, List<float[]> existingEmbeddings) {
        final float MIN_DIVERSITY_THRESHOLD = 0.15f; // Minimum difference required

        for (float[] existing : existingEmbeddings) {
            float similarity = calculateCosineSimilarity(newEmbedding, existing);
            // If too similar to any existing embedding, reject
            if (similarity > (1.0f - MIN_DIVERSITY_THRESHOLD)) {
                Log.d(TAG, String.format("Embedding too similar to existing one: %.3f", similarity));
                return false;
            }
        }

        Log.d(TAG, String.format("Embedding is sufficiently diverse from %d existing embeddings", existingEmbeddings.size()));
        return true;
    }

    /**
     * Enhanced quality score calculation for embeddings
     */
    private float calculateEmbeddingQuality(float[] embedding, Face face) {
        // Embedding magnitude (well-extracted features should have reasonable magnitude)
        float magnitude = 0;
        for (float value : embedding) {
            magnitude += value * value;
        }
        magnitude = (float) Math.sqrt(magnitude);
        float magnitudeScore = Math.min(1.0f, magnitude / 10.0f); // Normalize

        // Embedding variance (good embeddings should have varied values, not all similar)
        float mean = 0;
        for (float value : embedding) {
            mean += value;
        }
        mean /= embedding.length;

        float variance = 0;
        for (float value : embedding) {
            variance += (value - mean) * (value - mean);
        }
        variance /= embedding.length;
        float varianceScore = Math.min(1.0f, variance * 10.0f); // Normalize

        if (face == null) {
            // For stored embeddings, use magnitude and variance
            return (magnitudeScore * 0.6f + varianceScore * 0.4f);
        }

        // Enhanced quality metrics for live faces
        float faceSize = Math.min(face.getBoundingBox().width(), face.getBoundingBox().height());
        float sizeScore = Math.min(1.0f, faceSize / 120.0f); // Prefer larger faces (increased threshold)

        // Head pose (prefer frontal faces with stricter requirements)
        Float headEulerAngleY = face.getHeadEulerAngleY();
        Float headEulerAngleZ = face.getHeadEulerAngleZ();
        float poseScore = 1.0f;
        if (headEulerAngleY != null) {
            poseScore *= Math.max(0.2f, 1.0f - Math.abs(headEulerAngleY) / 35.0f); // Stricter Y angle
        }
        if (headEulerAngleZ != null) {
            poseScore *= Math.max(0.2f, 1.0f - Math.abs(headEulerAngleZ) / 25.0f); // Stricter Z angle
        }

        // Eye openness (if available)
        float eyeScore = 1.0f;
        Float leftEyeOpenProbability = face.getLeftEyeOpenProbability();
        Float rightEyeOpenProbability = face.getRightEyeOpenProbability();
        if (leftEyeOpenProbability != null && rightEyeOpenProbability != null) {
            eyeScore = (leftEyeOpenProbability + rightEyeOpenProbability) / 2.0f;
        }

        // Weighted combination with emphasis on pose and eye openness for registration
        return (sizeScore * 0.2f + magnitudeScore * 0.3f + varianceScore * 0.2f + poseScore * 0.2f + eyeScore * 0.1f);
    }
    
    /**
     * Complete multi-photo registration by storing all embeddings
     */
    private void completeMultiPhotoRegistration() {
        try {
            if (collectedEmbeddings.isEmpty()) {
                if (multiPhotoCallback != null) {
                    multiPhotoCallback.onRegistrationComplete(false, "No valid photos collected");
                }
                stopMultiPhotoRegistration();
                return;
            }
            
            // Create person record
            Person person = new Person();
            person.name = currentRegistrationName;
            person.createdTimestamp = System.currentTimeMillis();
            person.confidenceThreshold = BASE_RECOGNITION_THRESHOLD;
            
            // Store primary embedding (best quality one) in person record for backward compatibility
            float[] bestEmbedding = findBestEmbedding(collectedEmbeddings);
            person.embeddingJson = embeddingToJson(bestEmbedding);
            
            // Insert person and get ID
            long personId = database.personDao().insertPerson(person);
            
            // Store all embeddings in PersonEmbedding table
            for (int i = 0; i < collectedEmbeddings.size(); i++) {
                float[] embedding = collectedEmbeddings.get(i);
                float quality = calculateEmbeddingQuality(embedding, null); // Simplified quality for stored embeddings
                
                PersonEmbedding personEmbedding = new PersonEmbedding((int) personId, embeddingToJson(embedding), quality);
                personEmbedding.isPrimary = (embedding == bestEmbedding);
                
                database.personEmbeddingDao().insertEmbedding(personEmbedding);
            }
            
            Log.d(TAG, String.format("Successfully registered %s with %d embeddings", 
                currentRegistrationName, collectedEmbeddings.size()));
            
            if (multiPhotoCallback != null) {
                multiPhotoCallback.onRegistrationComplete(true, 
                    String.format("Successfully registered %s with %d photos", 
                        currentRegistrationName, collectedEmbeddings.size()));
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error completing multi-photo registration", e);
            if (multiPhotoCallback != null) {
                multiPhotoCallback.onRegistrationComplete(false, "Registration failed: " + e.getMessage());
            }
        } finally {
            stopMultiPhotoRegistration();
        }
    }
    
    /**
     * Find the best quality embedding from a list
     */
    private float[] findBestEmbedding(List<float[]> embeddings) {
        if (embeddings.isEmpty()) return null;
        if (embeddings.size() == 1) return embeddings.get(0);
        
        float[] bestEmbedding = embeddings.get(0);
        float bestMagnitude = calculateMagnitude(bestEmbedding);
        
        for (int i = 1; i < embeddings.size(); i++) {
            float[] embedding = embeddings.get(i);
            float magnitude = calculateMagnitude(embedding);
            if (magnitude > bestMagnitude) {
                bestEmbedding = embedding;
                bestMagnitude = magnitude;
            }
        }
        
        return bestEmbedding;
    }
    
    /**
     * Calculate magnitude of an embedding vector
     */
    private float calculateMagnitude(float[] embedding) {
        float magnitude = 0;
        for (float value : embedding) {
            magnitude += value * value;
        }
        return (float) Math.sqrt(magnitude);
    }
    
    /**
     * Get number of currently tracked faces
     */
    public int getTrackedFaceCount() {
        return trackedFaces.size();
    }
    

    
    /**
     * Cleanup resources
     */
    public void cleanup() {
        clearTrackedFaces();
        
        if (textToSpeech != null) {
            textToSpeech.stop();
            textToSpeech.shutdown();
        }
        
        if (interpreter != null) {
            interpreter.close();
        }
        
        Log.d(TAG, "Face recognition cleaned up");
    }
    
    /**
     * Recognition result class
     */
    private static class RecognitionResult {
        final String personName;
        final float confidence;
        
        RecognitionResult(String personName, float confidence) {
            this.personName = personName;
            this.confidence = confidence;
        }
    }
}
