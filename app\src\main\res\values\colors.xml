<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Primary Colors -->
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="black">#FF000000</color>
    <color name="white">#FFFFFFFF</color>
    
    <!-- Robot Theme Colors -->
    <color name="robot_primary">#FF2196F3</color>
    <color name="robot_primary_dark">#FF1976D2</color>
    <color name="robot_secondary">#FF4CAF50</color>
    <color name="robot_accent">#FFFF5722</color>
    
    <!-- Background Colors -->
    <color name="robot_background">#FF121212</color>
    <color name="content_background">#FF1E1E1E</color>
    <color name="status_bar_background">#FF2C2C2C</color>
    <color name="navigation_background">#FF1A1A1A</color>
    
    <!-- Status Colors -->
    <color name="status_connected">#FF4CAF50</color>
    <color name="status_disconnected">#FFF44336</color>
    <color name="status_warning">#FFFF9800</color>
    
    <!-- Navigation Colors -->
    <color name="navigation_item_color">@color/robot_primary</color>
    <color name="navigation_item_selected">#FFFFFFFF</color>
    
    <!-- Robot Face Colors -->
    <color name="robot_eye_color">#FF00BCD4</color>
    <color name="robot_face_background">#FF263238</color>
    

    
    <!-- Control Colors -->
    <color name="control_button_background">#FF37474F</color>
    <color name="control_button_pressed">#FF455A64</color>
    <color name="control_button_text">#FFFFFFFF</color>
    
    <!-- Text Colors -->
    <color name="text_primary">#FFFFFFFF</color>
    <color name="text_secondary">#FFBDBDBD</color>
    <color name="text_hint">#FF757575</color>

    <!-- Transcription Colors -->
    <color name="transcription_background">#FF2E2E2E</color>
    <color name="transcription_text_background">#FF3A3A3A</color>
    <color name="transcription_text">#FF4CAF50</color>

    <!-- Preset Category Colors -->
    <color name="category_movement">#FF2196F3</color>
    <color name="category_gesture">#FF4CAF50</color>
    <color name="category_dance">#FFE91E63</color>
    <color name="category_security">#FFFF5722</color>
    <color name="category_presentation">#FF9C27B0</color>
    <color name="category_custom">#FF607D8B</color>

    <!-- Additional UI Colors -->
    <color name="background_primary">#FF121212</color>
    <color name="background_secondary">#FF1E1E1E</color>
    <color name="divider_color">#FF424242</color>
    <color name="status_success">#FF4CAF50</color>
    <color name="status_error">#FFF44336</color>
</resources>
