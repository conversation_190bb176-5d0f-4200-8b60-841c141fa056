<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="2dp"
    app:cardBackgroundColor="@color/background_primary"
    app:cardCornerRadius="6dp"
    app:cardElevation="1dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="8dp">

        <!-- Action Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <!-- Action Type Icon -->
            <TextView
                android:id="@+id/text_action_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:text="A"
                android:textSize="14sp"
                android:gravity="center"
                android:layout_marginEnd="8dp" />

            <!-- Action Type Spinner -->
            <Spinner
                android:id="@+id/spinner_action_type"
                android:layout_width="0dp"
                android:layout_height="36dp"
                android:layout_weight="1"
                android:background="@drawable/spinner_background"
                android:layout_marginEnd="8dp" />

            <!-- Delete Action -->
            <Button
                android:id="@+id/btn_delete_action"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:text="X"
                android:textSize="12sp"
                android:backgroundTint="@color/status_error"
                android:textColor="@color/white" />

        </LinearLayout>

        <!-- Action Description -->
        <EditText
            android:id="@+id/edit_action_description"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Action description"
            android:textColor="@color/text_primary"
            android:textColorHint="@color/text_secondary"
            android:textSize="12sp"
            android:background="@drawable/edit_text_background"
            android:padding="6dp"
            android:layout_marginBottom="8dp" />

        <!-- Parameters Container -->
        <LinearLayout
            android:id="@+id/layout_parameters"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Movement Parameters -->
            <LinearLayout
                android:id="@+id/layout_movement_params"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Movement Direction:"
                    android:textColor="@color/text_secondary"
                    android:textSize="11sp"
                    android:layout_marginBottom="4dp" />

                <Spinner
                    android:id="@+id/spinner_movement_direction"
                    android:layout_width="match_parent"
                    android:layout_height="32dp"
                    android:background="@drawable/spinner_background"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Speed:"
                    android:textColor="@color/text_secondary"
                    android:textSize="11sp"
                    android:layout_marginBottom="4dp" />

                <Spinner
                    android:id="@+id/spinner_movement_speed"
                    android:layout_width="match_parent"
                    android:layout_height="32dp"
                    android:background="@drawable/spinner_background"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Duration (seconds):"
                    android:textColor="@color/text_secondary"
                    android:textSize="11sp"
                    android:layout_marginBottom="4dp" />

                <EditText
                    android:id="@+id/edit_movement_duration"
                    android:layout_width="match_parent"
                    android:layout_height="32dp"
                    android:hint="2.0"
                    android:inputType="numberDecimal"
                    android:textSize="12sp"
                    android:background="@drawable/edit_text_background"
                    android:padding="8dp"
                    android:layout_marginBottom="8dp" />

            </LinearLayout>

            <!-- Gesture Parameters -->
            <LinearLayout
                android:id="@+id/layout_gesture_params"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Gesture Type:"
                    android:textColor="@color/text_secondary"
                    android:textSize="11sp"
                    android:layout_marginBottom="4dp" />

                <Spinner
                    android:id="@+id/spinner_gesture_type"
                    android:layout_width="match_parent"
                    android:layout_height="32dp"
                    android:background="@drawable/spinner_background"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Intensity:"
                    android:textColor="@color/text_secondary"
                    android:textSize="11sp"
                    android:layout_marginBottom="4dp" />

                <Spinner
                    android:id="@+id/spinner_gesture_intensity"
                    android:layout_width="match_parent"
                    android:layout_height="32dp"
                    android:background="@drawable/spinner_background"
                    android:layout_marginBottom="8dp" />

            </LinearLayout>

            <!-- Hand Control Parameters -->
            <LinearLayout
                android:id="@+id/layout_hand_params"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Hand Side:"
                    android:textColor="@color/text_secondary"
                    android:textSize="11sp"
                    android:layout_marginBottom="4dp" />

                <Spinner
                    android:id="@+id/spinner_hand_side"
                    android:layout_width="match_parent"
                    android:layout_height="32dp"
                    android:background="@drawable/spinner_background"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Position:"
                    android:textColor="@color/text_secondary"
                    android:textSize="11sp"
                    android:layout_marginBottom="4dp" />

                <Spinner
                    android:id="@+id/spinner_hand_position"
                    android:layout_width="match_parent"
                    android:layout_height="32dp"
                    android:background="@drawable/spinner_background"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Custom Angle (0-180°):"
                    android:textColor="@color/text_secondary"
                    android:textSize="11sp"
                    android:layout_marginBottom="4dp" />

                <EditText
                    android:id="@+id/edit_hand_angle"
                    android:layout_width="match_parent"
                    android:layout_height="32dp"
                    android:hint="90"
                    android:inputType="number"
                    android:textSize="12sp"
                    android:background="@drawable/edit_text_background"
                    android:padding="8dp"
                    android:layout_marginBottom="8dp" />

            </LinearLayout>

            <!-- Servo Parameters -->
            <LinearLayout
                android:id="@+id/layout_servo_params"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="4dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Servo:"
                        android:textColor="@color/text_secondary"
                        android:textSize="11sp" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Angle:"
                        android:textColor="@color/text_secondary"
                        android:textSize="11sp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <Spinner
                        android:id="@+id/spinner_servo_type"
                        android:layout_width="0dp"
                        android:layout_height="32dp"
                        android:layout_weight="1"
                        android:background="@drawable/spinner_background"
                        android:layout_marginEnd="8dp" />

                    <EditText
                        android:id="@+id/edit_servo_angle"
                        android:layout_width="0dp"
                        android:layout_height="32dp"
                        android:layout_weight="1"
                        android:hint="0-360"
                        android:textColor="@color/text_primary"
                        android:textSize="11sp"
                        android:inputType="number"
                        android:background="@drawable/edit_text_background"
                        android:padding="4dp"
                        android:gravity="center" />

                </LinearLayout>

            </LinearLayout>

            <!-- Head Movement Parameters -->
            <LinearLayout
                android:id="@+id/layout_head_params"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Head Action:"
                    android:textColor="@color/text_secondary"
                    android:textSize="11sp"
                    android:layout_marginBottom="4dp" />

                <Spinner
                    android:id="@+id/spinner_head_action"
                    android:layout_width="match_parent"
                    android:layout_height="32dp"
                    android:background="@drawable/spinner_background"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Direction:"
                    android:textColor="@color/text_secondary"
                    android:textSize="11sp"
                    android:layout_marginBottom="4dp" />

                <Spinner
                    android:id="@+id/spinner_head_direction"
                    android:layout_width="match_parent"
                    android:layout_height="32dp"
                    android:background="@drawable/spinner_background"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Custom Angle (0-180°):"
                    android:textColor="@color/text_secondary"
                    android:textSize="11sp"
                    android:layout_marginBottom="4dp" />

                <EditText
                    android:id="@+id/edit_head_angle"
                    android:layout_width="match_parent"
                    android:layout_height="32dp"
                    android:hint="90"
                    android:inputType="number"
                    android:textSize="12sp"
                    android:background="@drawable/edit_text_background"
                    android:padding="8dp"
                    android:layout_marginBottom="8dp" />

            </LinearLayout>

            <!-- Speech Parameters -->
            <LinearLayout
                android:id="@+id/layout_speech_params"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Text to Speak:"
                    android:textColor="@color/text_secondary"
                    android:textSize="11sp"
                    android:layout_marginBottom="4dp" />

                <EditText
                    android:id="@+id/edit_speech_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Enter text to speak"
                    android:textColor="@color/text_primary"
                    android:textColorHint="@color/text_secondary"
                    android:textSize="11sp"
                    android:background="@drawable/edit_text_background"
                    android:padding="6dp"
                    android:maxLines="2"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Voice Gender:"
                    android:textColor="@color/text_secondary"
                    android:textSize="11sp"
                    android:layout_marginBottom="4dp" />

                <Spinner
                    android:id="@+id/spinner_voice_gender"
                    android:layout_width="match_parent"
                    android:layout_height="32dp"
                    android:background="@drawable/spinner_background"
                    android:layout_marginBottom="8dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:layout_marginEnd="4dp">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Speed:"
                            android:textColor="@color/text_secondary"
                            android:textSize="11sp"
                            android:layout_marginBottom="4dp" />

                        <EditText
                            android:id="@+id/edit_voice_speed"
                            android:layout_width="match_parent"
                            android:layout_height="32dp"
                            android:hint="1.0"
                            android:inputType="numberDecimal"
                            android:textSize="12sp"
                            android:background="@drawable/edit_text_background"
                            android:padding="8dp" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:layout_marginStart="4dp">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Pitch:"
                            android:textColor="@color/text_secondary"
                            android:textSize="11sp"
                            android:layout_marginBottom="4dp" />

                        <EditText
                            android:id="@+id/edit_voice_pitch"
                            android:layout_width="match_parent"
                            android:layout_height="32dp"
                            android:hint="1.0"
                            android:inputType="numberDecimal"
                            android:textSize="12sp"
                            android:background="@drawable/edit_text_background"
                            android:padding="8dp" />

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

            <!-- Delay Parameters -->
            <LinearLayout
                android:id="@+id/layout_delay_params"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:visibility="gone">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Delay:"
                    android:textColor="@color/text_secondary"
                    android:textSize="11sp"
                    android:layout_marginEnd="8dp" />

                <EditText
                    android:id="@+id/edit_delay_duration"
                    android:layout_width="80dp"
                    android:layout_height="32dp"
                    android:hint="1.0"
                    android:textColor="@color/text_primary"
                    android:textSize="11sp"
                    android:inputType="numberDecimal"
                    android:background="@drawable/edit_text_background"
                    android:padding="4dp"
                    android:gravity="center"
                    android:layout_marginEnd="4dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="seconds"
                    android:textColor="@color/text_secondary"
                    android:textSize="11sp" />

            </LinearLayout>

            <!-- Custom Command Parameters -->
            <LinearLayout
                android:id="@+id/layout_custom_params"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="ESP32 Command:"
                    android:textColor="@color/text_secondary"
                    android:textSize="11sp"
                    android:layout_marginBottom="4dp" />

                <EditText
                    android:id="@+id/edit_custom_command"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Enter ESP32 command"
                    android:textColor="@color/text_primary"
                    android:textColorHint="@color/text_secondary"
                    android:textSize="11sp"
                    android:background="@drawable/edit_text_background"
                    android:padding="6dp" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
