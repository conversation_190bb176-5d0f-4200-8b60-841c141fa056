<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/content_background"
    android:padding="8dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Connection Status Section -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            app:cardBackgroundColor="@color/control_button_background"
            app:cardCornerRadius="8dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="12dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/connection_status"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <!-- Motor Controller Status -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="8dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/motor_controller"
                        android:textColor="@color/text_secondary" />

                    <TextView
                        android:id="@+id/motor_status_text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/disconnected"
                        android:textColor="@color/status_disconnected" />

                </LinearLayout>

                <!-- Servo Controller Status -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="8dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/servo_controller"
                        android:textColor="@color/text_secondary" />

                    <TextView
                        android:id="@+id/servo_status_text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/disconnected"
                        android:textColor="@color/status_disconnected" />

                </LinearLayout>

                <!-- Sensor Controller Status -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/sensor_controller"
                        android:textColor="@color/text_secondary" />

                    <TextView
                        android:id="@+id/sensor_status_text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/disconnected"
                        android:textColor="@color/status_disconnected" />

                </LinearLayout>

                <!-- Test Connection Button -->
                <Button
                    android:id="@+id/test_connection_button"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="🔍 Test All Connections"
                    android:textColor="@color/text_primary"
                    android:background="@drawable/control_button_background"
                    android:layout_marginTop="12dp"
                    android:padding="12dp" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Sensor Data Section -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardBackgroundColor="@color/control_button_background"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/sensor_title"
                    android:textColor="@color/text_primary"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="16dp" />

                <!-- Battery Level -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="8dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/battery_level"
                        android:textColor="@color/text_secondary" />

                    <TextView
                        android:id="@+id/battery_level_text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="--"
                        android:textColor="@color/text_primary" />

                </LinearLayout>

                <!-- Heart Rate -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="8dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/heart_rate"
                        android:textColor="@color/text_secondary" />

                    <TextView
                        android:id="@+id/heart_rate_text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="--"
                        android:textColor="@color/text_primary" />

                </LinearLayout>

                <!-- Temperature -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/temperature"
                        android:textColor="@color/text_secondary" />

                    <TextView
                        android:id="@+id/temperature_text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="--"
                        android:textColor="@color/text_primary" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- System Information Section -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardBackgroundColor="@color/control_button_background"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="System Information"
                    android:textColor="@color/text_primary"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="16dp" />

                <!-- App Version -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="8dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/version"
                        android:textColor="@color/text_secondary" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="1.0.0"
                        android:textColor="@color/text_primary" />

                </LinearLayout>

                <!-- Uptime -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Uptime"
                        android:textColor="@color/text_secondary" />

                    <TextView
                        android:id="@+id/uptime_text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="--"
                        android:textColor="@color/text_primary" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Human Detection Status Section -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            app:cardBackgroundColor="@color/control_button_background"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Human Detection Status"
                    android:textColor="@color/text_primary"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="16dp" />

                <!-- Ultrasonic Distance -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="8dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Ultrasonic Distance"
                        android:textColor="@color/text_secondary" />

                    <TextView
                        android:id="@+id/status_ultrasonic_distance"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="-- cm"
                        android:textColor="@color/text_primary"
                        android:textStyle="bold" />

                </LinearLayout>

                <!-- Human Detection Status -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="8dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Human Detected"
                        android:textColor="@color/text_secondary" />

                    <TextView
                        android:id="@+id/status_human_detected"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="No"
                        android:textColor="@color/status_disconnected"
                        android:textStyle="bold" />

                </LinearLayout>

                <!-- Auto Detection Status -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Auto Detection"
                        android:textColor="@color/text_secondary" />

                    <TextView
                        android:id="@+id/status_auto_detection"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Disabled"
                        android:textColor="@color/status_disconnected"
                        android:textStyle="bold" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

    </LinearLayout>

</ScrollView>
