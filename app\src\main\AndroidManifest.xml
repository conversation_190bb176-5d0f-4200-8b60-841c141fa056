<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Network permissions -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    
    <!-- Voice permissions -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    
    <!-- Camera permissions -->
    <uses-permission android:name="android.permission.CAMERA" />

    <!-- Location permissions for GPS-based weather -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

    <!-- Storage permissions -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    
    <!-- Wake lock for continuous operation -->
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    
    <!-- Vibration for feedback -->
    <uses-permission android:name="android.permission.VIBRATE" />

    <!-- USB permissions for ESP32 communication -->
    <uses-permission android:name="android.permission.USB_PERMISSION" />
    <uses-feature android:name="android.hardware.usb.host" android:required="false" />

    <!-- Foreground service permissions for reliable USB communication -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_CONNECTED_DEVICE" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.StemRobot"
        android:usesCleartextTraffic="true"
        tools:targetApi="31">
        
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:theme="@style/Theme.StemRobot.NoActionBar">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

            <!-- USB device attachment intent filter -->
            <intent-filter>
                <action android:name="android.hardware.usb.action.USB_DEVICE_ATTACHED" />
            </intent-filter>

            <!-- USB device metadata -->
            <meta-data android:name="android.hardware.usb.action.USB_DEVICE_ATTACHED"
                android:resource="@xml/device_filter" />
        </activity>

        <!-- Working Object Detection Activity -->
        <activity
            android:name=".activities.WorkingObjectDetectionActivity"
            android:exported="true"
            android:configChanges="orientation|screenSize|keyboardHidden|screenLayout"
            android:theme="@style/Theme.StemRobot.NoActionBar"
            android:screenOrientation="sensor" />

        <!-- Preset Editor Activity -->
        <activity
            android:name=".activities.PresetEditorActivity"
            android:exported="false"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:theme="@style/Theme.StemRobot.NoActionBar"
            android:parentActivityName=".MainActivity" />

        <!-- Voice Command Test Activity -->
        <activity
            android:name=".activities.VoiceCommandTestActivity"
            android:exported="false"
            android:theme="@style/Theme.StemRobot.NoActionBar" />

        <!-- Real-time Data Settings Activity -->
        <activity
            android:name=".activities.RealTimeDataSettingsActivity"
            android:exported="false"
            android:theme="@style/Theme.StemRobot.NoActionBar" />

        <!-- LMS Video Player Activity -->
        <activity
            android:name=".activities.LMSVideoPlayerActivity"
            android:exported="false"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:theme="@style/Theme.StemRobot.NoActionBar"
            android:screenOrientation="landscape" />

        <!-- LMS YouTube Player Activity -->
        <activity
            android:name=".activities.LMSYouTubePlayerActivity"
            android:exported="false"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:theme="@style/Theme.StemRobot.NoActionBar"
            android:screenOrientation="landscape" />

        <!-- Voice recognition service -->
        <service
            android:name=".services.VoiceRecognitionService"
            android:enabled="true"
            android:exported="false" />

        <!-- Heart rate sensor service -->
        <service
            android:name=".services.HeartRateSensorService"
            android:enabled="true"
            android:exported="false" />

        <!-- USB Serial Service for ESP32 communication -->
        <service
            android:name=".services.USBSerialService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="connectedDevice" />

    </application>

</manifest>
