package com.stemrobo.humanoid.vision;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.view.View;

import java.util.ArrayList;
import java.util.List;

/**
 * Overlay view for displaying object detection results
 */
public class ObjectDetectionOverlay extends View {
    private static final String TAG = "ObjectDetectionOverlay";
    
    private Paint boxPaint;
    private Paint textPaint;
    private Paint backgroundPaint;
    private List<ObjectDetectionManager.Detection> detections = new ArrayList<>();
    
    public ObjectDetectionOverlay(Context context) {
        super(context);
        init();
    }
    
    public ObjectDetectionOverlay(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }
    
    public ObjectDetectionOverlay(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }
    
    private void init() {
        // Box paint for detection rectangles
        boxPaint = new Paint();
        boxPaint.setColor(Color.GREEN);
        boxPaint.setStyle(Paint.Style.STROKE);
        boxPaint.setStrokeWidth(4f);
        boxPaint.setAntiAlias(true);
        
        // Text paint for labels
        textPaint = new Paint();
        textPaint.setColor(Color.WHITE);
        textPaint.setTextSize(32f);
        textPaint.setAntiAlias(true);
        textPaint.setStyle(Paint.Style.FILL);
        
        // Background paint for text
        backgroundPaint = new Paint();
        backgroundPaint.setColor(Color.GREEN);
        backgroundPaint.setStyle(Paint.Style.FILL);
        backgroundPaint.setAlpha(180);
    }
    
    /**
     * Update detections to display
     */
    public void updateDetections(List<ObjectDetectionManager.Detection> newDetections) {
        this.detections.clear();
        if (newDetections != null) {
            this.detections.addAll(newDetections);
        }
        invalidate(); // Trigger redraw
    }
    
    /**
     * Clear all detections
     */
    public void clearDetections() {
        this.detections.clear();
        invalidate();
    }
    
    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        
        if (detections.isEmpty()) {
            return;
        }
        
        // Get view dimensions
        int viewWidth = getWidth();
        int viewHeight = getHeight();
        
        if (viewWidth == 0 || viewHeight == 0) {
            return;
        }
        
        // Draw each detection
        for (ObjectDetectionManager.Detection detection : detections) {
            drawDetection(canvas, detection, viewWidth, viewHeight);
        }
    }
    
    private void drawDetection(Canvas canvas, ObjectDetectionManager.Detection detection, int viewWidth, int viewHeight) {
        RectF boundingBox = detection.getLocation();
        
        // The bounding box coordinates are typically normalized (0-1) or in image coordinates
        // We need to scale them to the view dimensions
        float left, top, right, bottom;
        
        if (boundingBox.right <= 1.0f && boundingBox.bottom <= 1.0f) {
            // Normalized coordinates (0-1)
            left = boundingBox.left * viewWidth;
            top = boundingBox.top * viewHeight;
            right = boundingBox.right * viewWidth;
            bottom = boundingBox.bottom * viewHeight;
        } else {
            // Assume coordinates are in image space, scale proportionally
            // This is a fallback - ideally we'd know the original image dimensions
            float scaleX = viewWidth / 640f; // Assume 640x480 input
            float scaleY = viewHeight / 480f;
            
            left = boundingBox.left * scaleX;
            top = boundingBox.top * scaleY;
            right = boundingBox.right * scaleX;
            bottom = boundingBox.bottom * scaleY;
        }
        
        // Ensure bounds are within view
        left = Math.max(0, Math.min(viewWidth, left));
        top = Math.max(0, Math.min(viewHeight, top));
        right = Math.max(0, Math.min(viewWidth, right));
        bottom = Math.max(0, Math.min(viewHeight, bottom));
        
        // Draw bounding box
        canvas.drawRect(left, top, right, bottom, boxPaint);
        
        // Prepare label text
        String label = String.format("%s (%.1f%%)", 
            detection.getTitle(), 
            detection.getConfidence() * 100);
        
        // Measure text dimensions
        float textWidth = textPaint.measureText(label);
        float textHeight = textPaint.getTextSize();
        
        // Draw text background
        float textBackgroundLeft = left;
        float textBackgroundTop = top - textHeight - 8;
        float textBackgroundRight = left + textWidth + 16;
        float textBackgroundBottom = top;
        
        // Ensure text background is within view
        if (textBackgroundTop < 0) {
            textBackgroundTop = bottom;
            textBackgroundBottom = bottom + textHeight + 8;
        }
        
        canvas.drawRect(textBackgroundLeft, textBackgroundTop, 
                       textBackgroundRight, textBackgroundBottom, backgroundPaint);
        
        // Draw text
        canvas.drawText(label, left + 8, textBackgroundBottom - 4, textPaint);
    }
    
    /**
     * Set overlay colors
     */
    public void setColors(int boxColor, int textColor, int backgroundColor) {
        boxPaint.setColor(boxColor);
        textPaint.setColor(textColor);
        backgroundPaint.setColor(backgroundColor);
        invalidate();
    }
}