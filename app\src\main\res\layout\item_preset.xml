<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardBackgroundColor="@color/background_secondary"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp">

        <!-- Main Content Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <!-- Preset Info -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <!-- Name and Category -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:id="@+id/text_preset_name"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Preset Name"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:maxLines="1"
                        android:ellipsize="end" />

                    <TextView
                        android:id="@+id/text_preset_category"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="CUSTOM"
                        android:textColor="@color/robot_accent"
                        android:textSize="10sp"
                        android:textStyle="bold"
                        android:background="@drawable/category_badge_background"
                        android:padding="4dp"
                        android:layout_marginStart="8dp" />

                </LinearLayout>

                <!-- Description -->
                <TextView
                    android:id="@+id/text_preset_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Preset description"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp"
                    android:maxLines="2"
                    android:ellipsize="end"
                    android:layout_marginTop="4dp" />

                <!-- Stats Row -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginTop="8dp">

                    <TextView
                        android:id="@+id/text_preset_duration"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="⏱️ 5.2s"
                        android:textColor="@color/text_secondary"
                        android:textSize="11sp"
                        android:layout_marginEnd="12dp" />

                    <TextView
                        android:id="@+id/text_preset_steps"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="📋 3 steps"
                        android:textColor="@color/text_secondary"
                        android:textSize="11sp"
                        android:layout_marginEnd="12dp" />

                    <TextView
                        android:id="@+id/text_preset_modified"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="📅 2 days ago"
                        android:textColor="@color/text_secondary"
                        android:textSize="11sp"
                        android:gravity="end" />

                </LinearLayout>

            </LinearLayout>

            <!-- Action Buttons -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginStart="12dp">

                <!-- Play Button -->
                <Button
                    android:id="@+id/btn_play_preset"
                    android:layout_width="50dp"
                    android:layout_height="40dp"
                    android:text="▶️"
                    android:textSize="14sp"
                    android:backgroundTint="@color/status_success"
                    android:textColor="@color/white"
                    android:layout_marginBottom="4dp" />

                <!-- More Actions Button -->
                <Button
                    android:id="@+id/btn_preset_menu"
                    android:layout_width="50dp"
                    android:layout_height="35dp"
                    android:text="⋮"
                    android:textSize="14sp"
                    android:backgroundTint="@color/robot_secondary"
                    android:textColor="@color/white" />

            </LinearLayout>

        </LinearLayout>

        <!-- Expandable Details Section -->
        <LinearLayout
            android:id="@+id/layout_preset_details"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="12dp"
            android:visibility="gone">

            <!-- Divider -->
            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/divider_color"
                android:layout_marginBottom="8dp" />

            <!-- Steps Preview -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="📋 Steps Preview:"
                android:textColor="@color/text_primary"
                android:textSize="12sp"
                android:textStyle="bold"
                android:layout_marginBottom="4dp" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_preset_steps"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp" />

            <!-- Action Buttons Row -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center">

                <Button
                    android:id="@+id/btn_edit_preset"
                    android:layout_width="0dp"
                    android:layout_height="35dp"
                    android:layout_weight="1"
                    android:text="✏️ Edit"
                    android:textSize="10sp"
                    android:backgroundTint="@color/robot_accent"
                    android:textColor="@color/white"
                    android:layout_marginEnd="4dp" />

                <Button
                    android:id="@+id/btn_duplicate_preset"
                    android:layout_width="0dp"
                    android:layout_height="35dp"
                    android:layout_weight="1"
                    android:text="📋 Copy"
                    android:textSize="10sp"
                    android:backgroundTint="@color/robot_secondary"
                    android:textColor="@color/white"
                    android:layout_marginHorizontal="4dp" />

                <Button
                    android:id="@+id/btn_delete_preset"
                    android:layout_width="0dp"
                    android:layout_height="35dp"
                    android:layout_weight="1"
                    android:text="🗑️ Delete"
                    android:textSize="10sp"
                    android:backgroundTint="@color/status_error"
                    android:textColor="@color/white"
                    android:layout_marginStart="4dp" />

            </LinearLayout>

        </LinearLayout>

        <!-- Execution Progress Bar -->
        <ProgressBar
            android:id="@+id/progress_preset_execution"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="match_parent"
            android:layout_height="4dp"
            android:layout_marginTop="8dp"
            android:progressTint="@color/status_success"
            android:visibility="gone" />

        <!-- Execution Status -->
        <TextView
            android:id="@+id/text_execution_status"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="▶️ Executing..."
            android:textColor="@color/status_success"
            android:textSize="11sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginTop="4dp"
            android:visibility="gone" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
