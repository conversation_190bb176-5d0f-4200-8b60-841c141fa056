package com.stemrobo.humanoid.services;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.speech.tts.TextToSpeech;
import android.util.Log;

import com.stemrobo.humanoid.communication.ESP32CommunicationManager;
import com.stemrobo.humanoid.models.Preset;
import com.stemrobo.humanoid.models.PresetAction;
import com.stemrobo.humanoid.models.PresetStep;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

/**
 * Service for executing preset action sequences.
 * Handles timing, coordination, and execution of multiple simultaneous actions.
 */
public class PresetExecutionService {
    
    private static final String TAG = "PresetExecutionService";
    
    private Context context;
    private ESP32CommunicationManager esp32Manager;
    private TextToSpeech textToSpeech;
    private ExecutorService executorService;
    private Handler mainHandler;
    
    // Execution state
    private boolean isExecuting = false;
    private boolean isPaused = false;
    private Preset currentPreset = null;
    private ExecutionCallback currentCallback = null;
    private Future<?> executionTask = null;
    private long executionStartTime = 0;
    
    public interface ExecutionCallback {
        void onExecutionStarted(Preset preset);
        void onExecutionProgress(Preset preset, int currentStep, int totalSteps, int progressPercent);
        void onExecutionCompleted(Preset preset);
        void onExecutionError(Preset preset, String error);
    }
    
    public PresetExecutionService(Context context) {
        this.context = context;
        this.executorService = Executors.newSingleThreadExecutor();
        this.mainHandler = new Handler(Looper.getMainLooper());
        
        // Initialize ESP32 communication
        this.esp32Manager = ESP32CommunicationManager.getInstance();
        
        // Initialize Text-to-Speech
        initializeTextToSpeech();
    }
    
    private void initializeTextToSpeech() {
        textToSpeech = new TextToSpeech(context, status -> {
            if (status == TextToSpeech.SUCCESS) {
                Log.d(TAG, "TextToSpeech initialized successfully");
            } else {
                Log.e(TAG, "TextToSpeech initialization failed");
            }
        });
    }
    
    public boolean isExecuting() {
        return isExecuting;
    }
    
    public Preset getCurrentPreset() {
        return currentPreset;
    }

    public boolean isPaused() {
        return isPaused;
    }

    public void pauseExecution() {
        if (isExecuting && !isPaused) {
            isPaused = true;
            stopAllActions(); // Stop current actions but keep execution state
            Log.d(TAG, "Preset execution paused");
        }
    }

    public void resumeExecution() {
        if (isExecuting && isPaused) {
            isPaused = false;
            Log.d(TAG, "Preset execution resumed");
        }
    }

    public void executePreset(Preset preset, ExecutionCallback callback) {
        if (isExecuting) {
            callback.onExecutionError(preset, "Another preset is currently executing");
            return;
        }
        
        if (preset == null || preset.isEmpty()) {
            callback.onExecutionError(preset, "Preset is empty or invalid");
            return;
        }
        
        // Set execution state
        isExecuting = true;
        currentPreset = preset;
        currentCallback = callback;
        executionStartTime = System.currentTimeMillis();
        
        // Notify execution started
        mainHandler.post(() -> callback.onExecutionStarted(preset));
        
        // Start execution in background thread
        executionTask = executorService.submit(() -> executePresetInternal(preset));
    }
    
    public void stopExecution() {
        if (!isExecuting) {
            return;
        }
        
        // Cancel execution task
        if (executionTask != null && !executionTask.isDone()) {
            executionTask.cancel(true);
        }
        
        // Stop all robot actions
        stopAllActions();
        
        // Reset execution state
        resetExecutionState();
        
        Log.d(TAG, "Preset execution stopped");
    }
    
    private void executePresetInternal(Preset preset) {
        try {
            Log.d(TAG, "Starting execution of preset: " + preset.getName());
            
            List<PresetStep> steps = preset.getSteps();
            int totalSteps = steps.size();
            
            // Execute each step according to its timing
            for (int i = 0; i < totalSteps; i++) {
                if (Thread.currentThread().isInterrupted()) {
                    Log.d(TAG, "Execution interrupted");
                    return;
                }
                
                PresetStep step = steps.get(i);
                executeStep(step, i, totalSteps);
                
                // Calculate and report progress
                int progressPercent = (int) ((i + 1) * 100.0 / totalSteps);
                final int currentStepIndex = i;
                mainHandler.post(() -> {
                    if (currentCallback != null) {
                        currentCallback.onExecutionProgress(preset, currentStepIndex + 1, totalSteps, progressPercent);
                    }
                });
            }
            
            // Wait for all actions to complete
            waitForPresetCompletion(preset);
            
            // Execution completed successfully
            mainHandler.post(() -> {
                if (currentCallback != null) {
                    currentCallback.onExecutionCompleted(preset);
                }
                resetExecutionState();
            });
            
        } catch (InterruptedException e) {
            Log.d(TAG, "Preset execution was interrupted");
            mainHandler.post(() -> {
                if (currentCallback != null) {
                    currentCallback.onExecutionError(preset, "Execution was stopped");
                }
                resetExecutionState();
            });
        } catch (Exception e) {
            Log.e(TAG, "Error executing preset", e);
            mainHandler.post(() -> {
                if (currentCallback != null) {
                    currentCallback.onExecutionError(preset, "Execution error: " + e.getMessage());
                }
                resetExecutionState();
            });
        }
    }
    
    private void executeStep(PresetStep step, int stepIndex, int totalSteps) throws InterruptedException {
        Log.d(TAG, "Executing step " + (stepIndex + 1) + "/" + totalSteps + ": " + step.getName());
        
        // Wait until it's time to start this step
        long currentTime = System.currentTimeMillis() - executionStartTime;
        long stepStartTime = step.getStartTimeMs();
        
        if (stepStartTime > currentTime) {
            long waitTime = stepStartTime - currentTime;
            Log.d(TAG, "Waiting " + waitTime + "ms before starting step");
            Thread.sleep(waitTime);
        }
        
        // Execute all actions in this step simultaneously
        for (PresetAction action : step.getActions()) {
            executeAction(action);
        }
        
        // If this step has a duration, we don't need to wait here
        // The next step will handle its own timing
    }
    
    private void executeAction(PresetAction action) {
        Log.d(TAG, "Executing action: " + action.getDescription());
        
        switch (action.getType()) {
            case MOVEMENT:
            case GESTURE:
            case HAND_CONTROL:
            case HEAD_MOVEMENT:
            case STOP:
            case CUSTOM_COMMAND:
                executeESP32Action(action);
                break;
                
            case SPEECH:
                executeSpeechAction(action);
                break;
                
            case DELAY:
                executeDelayAction(action);
                break;
                
            default:
                Log.w(TAG, "Unknown action type: " + action.getType());
                break;
        }
    }
    
    private void executeESP32Action(PresetAction action) {
        String command = action.toESP32Command();
        if (command != null && !command.isEmpty()) {
            esp32Manager.sendMotorCommand(command);
            Log.d(TAG, "Sent ESP32 command: " + command);
        }
    }
    
    private void executeSpeechAction(PresetAction action) {
        String text = action.getParameter(PresetAction.ParameterKeys.TEXT);
        if (text != null && !text.isEmpty() && textToSpeech != null) {
            mainHandler.post(() -> {
                textToSpeech.speak(text, TextToSpeech.QUEUE_ADD, null, null);
            });
            Log.d(TAG, "Speaking: " + text);
        }
    }
    
    private void executeDelayAction(PresetAction action) {
        try {
            int delayMs = action.getParameterAsInt(PresetAction.ParameterKeys.DELAY_MS, 1000);
            Log.d(TAG, "Delaying for " + delayMs + "ms");
            Thread.sleep(delayMs);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException(e);
        }
    }
    
    private void waitForPresetCompletion(Preset preset) throws InterruptedException {
        // Wait for the total duration of the preset
        long totalDuration = preset.getTotalDurationMs();
        long elapsed = System.currentTimeMillis() - executionStartTime;
        
        if (totalDuration > elapsed) {
            long remainingTime = totalDuration - elapsed;
            Log.d(TAG, "Waiting " + remainingTime + "ms for preset completion");
            Thread.sleep(remainingTime);
        }
        
        // Stop all actions to ensure clean completion
        stopAllActions();
    }
    
    private void stopAllActions() {
        // Stop all robot movements
        esp32Manager.sendMotorCommand("S");
        
        // Stop text-to-speech
        if (textToSpeech != null) {
            textToSpeech.stop();
        }
        
        Log.d(TAG, "All actions stopped");
    }
    
    private void resetExecutionState() {
        isExecuting = false;
        currentPreset = null;
        currentCallback = null;
        executionTask = null;
        executionStartTime = 0;
    }
    
    public void cleanup() {
        stopExecution();
        
        if (textToSpeech != null) {
            textToSpeech.shutdown();
            textToSpeech = null;
        }
        
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
        
        Log.d(TAG, "PresetExecutionService cleaned up");
    }
}
