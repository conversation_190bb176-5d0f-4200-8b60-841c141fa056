package com.stemrobo.humanoid.database;

import android.content.ContentValues;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.util.Log;

import com.stemrobo.humanoid.models.Preset;
import com.stemrobo.humanoid.models.PresetAction;
import com.stemrobo.humanoid.models.PresetStep;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Data Access Object for preset operations.
 * Handles all database CRUD operations for presets, steps, and actions.
 */
public class PresetDao {
    private static final String TAG = "PresetDao";
    private PresetDatabase dbHelper;
    
    public PresetDao(PresetDatabase dbHelper) {
        this.dbHelper = dbHelper;
    }
    
    // Preset CRUD operations
    public long insertPreset(Preset preset) {
        SQLiteDatabase db = dbHelper.getWritableDatabase();
        long presetId = -1;
        
        try {
            db.beginTransaction();
            
            // Insert preset
            ContentValues presetValues = new ContentValues();
            presetValues.put(PresetDatabase.PRESET_NAME, preset.getName());
            presetValues.put(PresetDatabase.PRESET_DESCRIPTION, preset.getDescription());
            presetValues.put(PresetDatabase.PRESET_CATEGORY, preset.getCategory().name());
            presetValues.put(PresetDatabase.PRESET_CREATED_AT, preset.getCreatedAt());
            presetValues.put(PresetDatabase.PRESET_MODIFIED_AT, preset.getModifiedAt());
            presetValues.put(PresetDatabase.PRESET_IS_ACTIVE, preset.isActive() ? 1 : 0);
            presetValues.put(PresetDatabase.PRESET_TOTAL_DURATION, preset.getTotalDurationMs());
            
            presetId = db.insert(PresetDatabase.TABLE_PRESETS, null, presetValues);
            
            if (presetId != -1) {
                preset.setId(presetId);
                
                // Insert steps and actions
                for (int i = 0; i < preset.getSteps().size(); i++) {
                    PresetStep step = preset.getSteps().get(i);
                    long stepId = insertPresetStep(db, presetId, step, i);
                    if (stepId != -1) {
                        step.setId(stepId);
                    }
                }
                
                db.setTransactionSuccessful();
            }
        } catch (Exception e) {
            Log.e(TAG, "Error inserting preset", e);
        } finally {
            db.endTransaction();
            db.close();
        }
        
        return presetId;
    }
    
    public boolean updatePreset(Preset preset) {
        SQLiteDatabase db = dbHelper.getWritableDatabase();
        boolean success = false;
        
        try {
            db.beginTransaction();
            
            // Update preset
            ContentValues presetValues = new ContentValues();
            presetValues.put(PresetDatabase.PRESET_NAME, preset.getName());
            presetValues.put(PresetDatabase.PRESET_DESCRIPTION, preset.getDescription());
            presetValues.put(PresetDatabase.PRESET_CATEGORY, preset.getCategory().name());
            presetValues.put(PresetDatabase.PRESET_MODIFIED_AT, System.currentTimeMillis());
            presetValues.put(PresetDatabase.PRESET_IS_ACTIVE, preset.isActive() ? 1 : 0);
            presetValues.put(PresetDatabase.PRESET_TOTAL_DURATION, preset.getTotalDurationMs());
            
            int rowsUpdated = db.update(PresetDatabase.TABLE_PRESETS, presetValues,
                    PresetDatabase.PRESET_ID + " = ?", new String[]{String.valueOf(preset.getId())});
            
            if (rowsUpdated > 0) {
                // Delete existing steps and actions
                deletePresetStepsAndActions(db, preset.getId());
                
                // Insert updated steps and actions
                for (int i = 0; i < preset.getSteps().size(); i++) {
                    PresetStep step = preset.getSteps().get(i);
                    long stepId = insertPresetStep(db, preset.getId(), step, i);
                    if (stepId != -1) {
                        step.setId(stepId);
                    }
                }
                
                success = true;
                db.setTransactionSuccessful();
            }
        } catch (Exception e) {
            Log.e(TAG, "Error updating preset", e);
        } finally {
            db.endTransaction();
            db.close();
        }
        
        return success;
    }
    
    public boolean deletePreset(long presetId) {
        SQLiteDatabase db = dbHelper.getWritableDatabase();
        boolean success = false;
        
        try {
            db.beginTransaction();
            
            // Delete actions first (foreign key constraint)
            db.delete(PresetDatabase.TABLE_PRESET_ACTIONS,
                    PresetDatabase.ACTION_STEP_ID + " IN (SELECT " + PresetDatabase.STEP_ID +
                    " FROM " + PresetDatabase.TABLE_PRESET_STEPS +
                    " WHERE " + PresetDatabase.STEP_PRESET_ID + " = ?)",
                    new String[]{String.valueOf(presetId)});
            
            // Delete steps
            db.delete(PresetDatabase.TABLE_PRESET_STEPS,
                    PresetDatabase.STEP_PRESET_ID + " = ?",
                    new String[]{String.valueOf(presetId)});
            
            // Delete preset
            int rowsDeleted = db.delete(PresetDatabase.TABLE_PRESETS,
                    PresetDatabase.PRESET_ID + " = ?",
                    new String[]{String.valueOf(presetId)});
            
            success = rowsDeleted > 0;
            db.setTransactionSuccessful();
        } catch (Exception e) {
            Log.e(TAG, "Error deleting preset", e);
        } finally {
            db.endTransaction();
            db.close();
        }
        
        return success;
    }
    
    public Preset getPreset(long presetId) {
        SQLiteDatabase db = dbHelper.getReadableDatabase();
        Preset preset = null;
        
        try {
            Cursor cursor = db.query(PresetDatabase.TABLE_PRESETS, null,
                    PresetDatabase.PRESET_ID + " = ?", new String[]{String.valueOf(presetId)},
                    null, null, null);
            
            if (cursor.moveToFirst()) {
                preset = cursorToPreset(cursor);
                if (preset != null) {
                    loadPresetSteps(db, preset);
                }
            }
            cursor.close();
        } catch (Exception e) {
            Log.e(TAG, "Error getting preset", e);
        } finally {
            db.close();
        }
        
        return preset;
    }

    public Preset getPresetById(long presetId) {
        return getPreset(presetId);
    }

    public List<Preset> getAllPresets() {
        List<Preset> presets = new ArrayList<>();
        SQLiteDatabase db = dbHelper.getReadableDatabase();

        try {
            Cursor cursor = db.query(PresetDatabase.TABLE_PRESETS, null, null, null,
                    null, null, PresetDatabase.PRESET_NAME + " ASC");

            while (cursor.moveToNext()) {
                Preset preset = cursorToPreset(cursor);
                if (preset != null) {
                    // Load steps and actions for each preset
                    loadPresetSteps(db, preset);
                    presets.add(preset);
                }
            }
            cursor.close();
        } catch (Exception e) {
            Log.e(TAG, "Error getting all presets", e);
        } finally {
            db.close();
        }

        return presets;
    }
    
    public List<Preset> getPresetsByCategory(Preset.Category category) {
        List<Preset> presets = new ArrayList<>();
        SQLiteDatabase db = dbHelper.getReadableDatabase();
        
        try {
            Cursor cursor = db.query(PresetDatabase.TABLE_PRESETS, null,
                    PresetDatabase.PRESET_CATEGORY + " = ? AND " + PresetDatabase.PRESET_IS_ACTIVE + " = 1",
                    new String[]{category.name()}, null, null, PresetDatabase.PRESET_NAME + " ASC");
            
            while (cursor.moveToNext()) {
                Preset preset = cursorToPreset(cursor);
                if (preset != null) {
                    presets.add(preset);
                }
            }
            cursor.close();
        } catch (Exception e) {
            Log.e(TAG, "Error getting presets by category", e);
        } finally {
            db.close();
        }
        
        return presets;
    }
    
    public List<Preset> searchPresets(String searchTerm) {
        List<Preset> presets = new ArrayList<>();
        SQLiteDatabase db = dbHelper.getReadableDatabase();
        
        try {
            String selection = "(" + PresetDatabase.PRESET_NAME + " LIKE ? OR " +
                             PresetDatabase.PRESET_DESCRIPTION + " LIKE ?) AND " +
                             PresetDatabase.PRESET_IS_ACTIVE + " = 1";
            String[] selectionArgs = {"%" + searchTerm + "%", "%" + searchTerm + "%"};
            
            Cursor cursor = db.query(PresetDatabase.TABLE_PRESETS, null, selection, selectionArgs,
                    null, null, PresetDatabase.PRESET_NAME + " ASC");
            
            while (cursor.moveToNext()) {
                Preset preset = cursorToPreset(cursor);
                if (preset != null) {
                    presets.add(preset);
                }
            }
            cursor.close();
        } catch (Exception e) {
            Log.e(TAG, "Error searching presets", e);
        } finally {
            db.close();
        }
        
        return presets;
    }
    
    // Helper methods
    private Preset cursorToPreset(Cursor cursor) {
        try {
            Preset preset = new Preset();
            preset.setId(cursor.getLong(cursor.getColumnIndexOrThrow(PresetDatabase.PRESET_ID)));
            preset.setName(cursor.getString(cursor.getColumnIndexOrThrow(PresetDatabase.PRESET_NAME)));
            preset.setDescription(cursor.getString(cursor.getColumnIndexOrThrow(PresetDatabase.PRESET_DESCRIPTION)));

            String categoryName = cursor.getString(cursor.getColumnIndexOrThrow(PresetDatabase.PRESET_CATEGORY));
            preset.setCategory(Preset.Category.valueOf(categoryName));

            preset.setCreatedAt(cursor.getLong(cursor.getColumnIndexOrThrow(PresetDatabase.PRESET_CREATED_AT)));
            preset.setModifiedAt(cursor.getLong(cursor.getColumnIndexOrThrow(PresetDatabase.PRESET_MODIFIED_AT)));
            preset.setActive(cursor.getInt(cursor.getColumnIndexOrThrow(PresetDatabase.PRESET_IS_ACTIVE)) == 1);
            preset.setTotalDurationMs(cursor.getInt(cursor.getColumnIndexOrThrow(PresetDatabase.PRESET_TOTAL_DURATION)));

            return preset;
        } catch (Exception e) {
            Log.e(TAG, "Error converting cursor to preset", e);
            return null;
        }
    }

    private long insertPresetStep(SQLiteDatabase db, long presetId, PresetStep step, int order) {
        ContentValues stepValues = new ContentValues();
        stepValues.put(PresetDatabase.STEP_PRESET_ID, presetId);
        stepValues.put(PresetDatabase.STEP_START_TIME, step.getStartTimeMs());
        stepValues.put(PresetDatabase.STEP_DURATION, step.getDurationMs());
        stepValues.put(PresetDatabase.STEP_NAME, step.getName());
        stepValues.put(PresetDatabase.STEP_ORDER, order);

        long stepId = db.insert(PresetDatabase.TABLE_PRESET_STEPS, null, stepValues);

        if (stepId != -1) {
            // Insert actions for this step
            for (int i = 0; i < step.getActions().size(); i++) {
                PresetAction action = step.getActions().get(i);
                insertPresetAction(db, stepId, action, i);
            }
        }

        return stepId;
    }

    private long insertPresetAction(SQLiteDatabase db, long stepId, PresetAction action, int order) {
        ContentValues actionValues = new ContentValues();
        actionValues.put(PresetDatabase.ACTION_STEP_ID, stepId);
        actionValues.put(PresetDatabase.ACTION_TYPE, action.getType().name());
        actionValues.put(PresetDatabase.ACTION_DESCRIPTION, action.getDescription());
        actionValues.put(PresetDatabase.ACTION_PARAMETERS, parametersToJson(action.getParameters()));
        actionValues.put(PresetDatabase.ACTION_ORDER, order);

        return db.insert(PresetDatabase.TABLE_PRESET_ACTIONS, null, actionValues);
    }

    private void loadPresetSteps(SQLiteDatabase db, Preset preset) {
        Cursor stepCursor = db.query(PresetDatabase.TABLE_PRESET_STEPS, null,
                PresetDatabase.STEP_PRESET_ID + " = ?", new String[]{String.valueOf(preset.getId())},
                null, null, PresetDatabase.STEP_ORDER + " ASC");

        List<PresetStep> steps = new ArrayList<>();
        while (stepCursor.moveToNext()) {
            PresetStep step = cursorToPresetStep(stepCursor);
            if (step != null) {
                loadStepActions(db, step);
                steps.add(step);
            }
        }
        stepCursor.close();

        preset.setSteps(steps);
    }

    private PresetStep cursorToPresetStep(Cursor cursor) {
        try {
            PresetStep step = new PresetStep();
            step.setId(cursor.getLong(cursor.getColumnIndexOrThrow(PresetDatabase.STEP_ID)));
            step.setStartTimeMs(cursor.getInt(cursor.getColumnIndexOrThrow(PresetDatabase.STEP_START_TIME)));
            step.setDurationMs(cursor.getInt(cursor.getColumnIndexOrThrow(PresetDatabase.STEP_DURATION)));
            step.setName(cursor.getString(cursor.getColumnIndexOrThrow(PresetDatabase.STEP_NAME)));
            return step;
        } catch (Exception e) {
            Log.e(TAG, "Error converting cursor to preset step", e);
            return null;
        }
    }

    private void loadStepActions(SQLiteDatabase db, PresetStep step) {
        Cursor actionCursor = db.query(PresetDatabase.TABLE_PRESET_ACTIONS, null,
                PresetDatabase.ACTION_STEP_ID + " = ?", new String[]{String.valueOf(step.getId())},
                null, null, PresetDatabase.ACTION_ORDER + " ASC");

        List<PresetAction> actions = new ArrayList<>();
        while (actionCursor.moveToNext()) {
            PresetAction action = cursorToPresetAction(actionCursor);
            if (action != null) {
                actions.add(action);
            }
        }
        actionCursor.close();

        step.setActions(actions);
    }

    private PresetAction cursorToPresetAction(Cursor cursor) {
        try {
            PresetAction action = new PresetAction();
            action.setId(cursor.getLong(cursor.getColumnIndexOrThrow(PresetDatabase.ACTION_ID)));

            String typeName = cursor.getString(cursor.getColumnIndexOrThrow(PresetDatabase.ACTION_TYPE));
            action.setType(PresetAction.ActionType.valueOf(typeName));

            action.setDescription(cursor.getString(cursor.getColumnIndexOrThrow(PresetDatabase.ACTION_DESCRIPTION)));

            String parametersJson = cursor.getString(cursor.getColumnIndexOrThrow(PresetDatabase.ACTION_PARAMETERS));
            action.setParameters(jsonToParameters(parametersJson));

            return action;
        } catch (Exception e) {
            Log.e(TAG, "Error converting cursor to preset action", e);
            return null;
        }
    }

    private void deletePresetStepsAndActions(SQLiteDatabase db, long presetId) {
        // Delete actions first
        db.delete(PresetDatabase.TABLE_PRESET_ACTIONS,
                PresetDatabase.ACTION_STEP_ID + " IN (SELECT " + PresetDatabase.STEP_ID +
                " FROM " + PresetDatabase.TABLE_PRESET_STEPS +
                " WHERE " + PresetDatabase.STEP_PRESET_ID + " = ?)",
                new String[]{String.valueOf(presetId)});

        // Delete steps
        db.delete(PresetDatabase.TABLE_PRESET_STEPS,
                PresetDatabase.STEP_PRESET_ID + " = ?",
                new String[]{String.valueOf(presetId)});
    }

    private String parametersToJson(Map<String, String> parameters) {
        try {
            JSONObject json = new JSONObject();
            for (Map.Entry<String, String> entry : parameters.entrySet()) {
                json.put(entry.getKey(), entry.getValue());
            }
            return json.toString();
        } catch (JSONException e) {
            Log.e(TAG, "Error converting parameters to JSON", e);
            return "{}";
        }
    }

    private Map<String, String> jsonToParameters(String json) {
        Map<String, String> parameters = new HashMap<>();
        if (json == null || json.isEmpty()) {
            return parameters;
        }

        try {
            JSONObject jsonObject = new JSONObject(json);
            java.util.Iterator<String> keys = jsonObject.keys();
            while (keys.hasNext()) {
                String key = keys.next();
                parameters.put(key, jsonObject.getString(key));
            }
        } catch (JSONException e) {
            Log.e(TAG, "Error converting JSON to parameters", e);
        }

        return parameters;
    }
}
