package com.stemrobo.humanoid.database;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;
import java.util.List;

/**
 * Data Access Object for Person entity
 * Provides methods for CRUD operations on the person database
 */
@Dao
public interface PersonDao {
    
    /**
     * Get all registered persons
     */
    @Query("SELECT * FROM known_persons ORDER BY name ASC")
    List<Person> getAllPersons();
    
    /**
     * Get person by ID
     */
    @Query("SELECT * FROM known_persons WHERE uid = :uid")
    Person getPersonById(int uid);
    
    /**
     * Get person by name
     */
    @Query("SELECT * FROM known_persons WHERE name = :name LIMIT 1")
    Person getPersonByName(String name);
    
    /**
     * Insert a new person
     */
    @Insert
    long insertPerson(Person person);
    
    /**
     * Update existing person
     */
    @Update
    void updatePerson(Person person);
    
    /**
     * Delete person
     */
    @Delete
    void deletePerson(Person person);
    
    /**
     * Delete person by ID
     */
    @Query("DELETE FROM known_persons WHERE uid = :uid")
    void deletePersonById(int uid);
    
    /**
     * Get count of registered persons
     */
    @Query("SELECT COUNT(*) FROM known_persons")
    int getPersonCount();
    
    /**
     * Get recently seen persons (last 7 days)
     */
    @Query("SELECT * FROM known_persons WHERE last_seen_timestamp > :timestamp ORDER BY last_seen_timestamp DESC")
    List<Person> getRecentlySeenPersons(long timestamp);
    
    /**
     * Get most recognized persons
     */
    @Query("SELECT * FROM known_persons ORDER BY recognition_count DESC LIMIT :limit")
    List<Person> getMostRecognizedPersons(int limit);
    
    /**
     * Update recognition statistics for a person
     */
    @Query("UPDATE known_persons SET recognition_count = recognition_count + 1, last_seen_timestamp = :timestamp WHERE uid = :uid")
    void updateRecognitionStats(int uid, long timestamp);
    
    /**
     * Search persons by name (partial match)
     */
    @Query("SELECT * FROM known_persons WHERE name LIKE '%' || :searchQuery || '%' ORDER BY name ASC")
    List<Person> searchPersonsByName(String searchQuery);
    
    /**
     * Clear all persons (for testing/reset)
     */
    @Query("DELETE FROM known_persons")
    void clearAllPersons();
}
