package com.stemrobo.humanoid.services;

import android.app.Service;
import android.content.Intent;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.util.Log;

import androidx.annotation.Nullable;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.stemrobo.humanoid.communication.ESP32CommunicationManager;
import com.stemrobo.humanoid.models.RobotCommand;
import com.stemrobo.humanoid.models.RobotResponse;

import java.util.HashMap;
import java.util.Map;

/**
 * Service to monitor heart rate sensor data from ESP32
 * Shows heart rate only when sensor is being touched
 */
public class HeartRateSensorService extends Service {
    private static final String TAG = "HeartRateSensorService";
    
    // Broadcast actions
    public static final String ACTION_HEART_RATE_UPDATE = "com.stemrobo.humanoid.HEART_RATE_UPDATE";
    public static final String ACTION_SENSOR_TOUCH_STATE = "com.stemrobo.humanoid.SENSOR_TOUCH_STATE";
    
    // Intent extras
    public static final String EXTRA_HEART_RATE = "heart_rate";
    public static final String EXTRA_IS_TOUCHED = "is_touched";
    
    private ESP32CommunicationManager communicationManager;
    private Handler handler;
    private boolean isMonitoring = false;
    private boolean isSensorTouched = false;
    private int currentHeartRate = 0;
    
    // Monitoring interval
    private static final int MONITORING_INTERVAL_MS = 1000; // 1 second
    
    private final Runnable heartRateMonitoringRunnable = new Runnable() {
        @Override
        public void run() {
            if (isMonitoring) {
                checkHeartRateSensor();
                handler.postDelayed(this, MONITORING_INTERVAL_MS);
            }
        }
    };
    
    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "HeartRateSensorService created");

        handler = new Handler(Looper.getMainLooper());
        communicationManager = ESP32CommunicationManager.getInstance();

        // Set up communication listener to receive sensor data
        communicationManager.setCommunicationListener(new ESP32CommunicationManager.CommunicationListener() {
            @Override
            public void onCommandSent(String controllerId, RobotCommand command) {
                // Command sent successfully
            }

            @Override
            public void onResponseReceived(String controllerId, RobotResponse response) {
                if ("sensor".equals(controllerId) && response.getData() != null) {
                    // Parse heart rate data from sensor response
                    parseHeartRateResponse(response);
                }
            }

            @Override
            public void onConnectionStatusChanged(String controllerId, boolean connected) {
                // Handle connection status changes
            }

            @Override
            public void onError(String controllerId, String error) {
                Log.e(TAG, "ESP32 communication error: " + error);
            }
        });
    }
    
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.d(TAG, "HeartRateSensorService started");
        startMonitoring();
        return START_STICKY; // Restart if killed
    }
    
    private void startMonitoring() {
        if (!isMonitoring) {
            isMonitoring = true;
            handler.post(heartRateMonitoringRunnable);
            Log.d(TAG, "Heart rate monitoring started");
        }
    }
    
    private void stopMonitoring() {
        if (isMonitoring) {
            isMonitoring = false;
            handler.removeCallbacks(heartRateMonitoringRunnable);
            Log.d(TAG, "Heart rate monitoring stopped");
        }
    }
    
    private void checkHeartRateSensor() {
        try {
            // Get real heart rate data from ESP32 sensor controller
            getRealHeartRateData();

        } catch (Exception e) {
            Log.e(TAG, "Error reading heart rate sensor", e);
        }
    }
    
    /**
     * Get real heart rate data from ESP32 sensor controller
     */
    private void getRealHeartRateData() {
        if (communicationManager == null) {
            return;
        }

        // Request heart rate data from ESP32 sensor controller
        Map<String, Object> params = new HashMap<>();
        communicationManager.sendSensorCommand("get_heart_rate", params);

        // The response will be handled by the communication listener
        // which will call parseHeartRateResponse when data is received
    }

    /**
     * Parse heart rate response from ESP32 sensor controller
     */
    private void parseHeartRateResponse(RobotResponse response) {
        try {
            Map<String, Object> data = response.getData();
            if (data != null) {
                // Get heart rate data
                Object heartRateObj = data.get("heartRate");
                Object heartRateValidObj = data.get("heartRateValid");

                if (heartRateValidObj instanceof Boolean && (Boolean) heartRateValidObj) {
                    // Heart rate sensor is being touched and has valid data
                    boolean wasTouched = isSensorTouched;
                    isSensorTouched = true;

                    if (heartRateObj instanceof Number) {
                        currentHeartRate = ((Number) heartRateObj).intValue();
                        broadcastHeartRateUpdate(currentHeartRate);
                    }

                    // Broadcast touch state change if needed
                    if (!wasTouched) {
                        broadcastTouchStateChange(true);
                    }
                } else {
                    // Heart rate sensor is not being touched or has invalid data
                    boolean wasTouched = isSensorTouched;
                    isSensorTouched = false;
                    currentHeartRate = 0;

                    // Broadcast touch state change if needed
                    if (wasTouched) {
                        broadcastTouchStateChange(false);
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error parsing heart rate response", e);
        }
    }
    
    /**
     * Parse actual sensor data from ESP32
     * This method will be used when ESP32 communication is implemented
     */
    private void parseSensorData(String sensorData) {
        try {
            // Expected format: "HEART_RATE:75,TOUCHED:1"
            String[] parts = sensorData.split(",");
            
            for (String part : parts) {
                String[] keyValue = part.split(":");
                if (keyValue.length == 2) {
                    String key = keyValue[0].trim();
                    String value = keyValue[1].trim();
                    
                    switch (key) {
                        case "HEART_RATE":
                            int heartRate = Integer.parseInt(value);
                            if (heartRate > 0 && heartRate < 200) { // Valid range
                                currentHeartRate = heartRate;
                                broadcastHeartRateUpdate(heartRate);
                            }
                            break;
                            
                        case "TOUCHED":
                            boolean wasTouched = isSensorTouched;
                            isSensorTouched = "1".equals(value);
                            
                            if (wasTouched != isSensorTouched) {
                                broadcastTouchStateChange(isSensorTouched);
                            }
                            break;
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error parsing sensor data: " + sensorData, e);
        }
    }
    
    private void broadcastHeartRateUpdate(int heartRate) {
        Intent intent = new Intent(ACTION_HEART_RATE_UPDATE);
        intent.putExtra(EXTRA_HEART_RATE, heartRate);
        LocalBroadcastManager.getInstance(this).sendBroadcast(intent);
        
        Log.d(TAG, "Heart rate update: " + heartRate + " BPM");
    }
    
    private void broadcastTouchStateChange(boolean isTouched) {
        Intent intent = new Intent(ACTION_SENSOR_TOUCH_STATE);
        intent.putExtra(EXTRA_IS_TOUCHED, isTouched);
        LocalBroadcastManager.getInstance(this).sendBroadcast(intent);
        
        Log.d(TAG, "Sensor touch state: " + (isTouched ? "touched" : "not touched"));
    }
    
    @Override
    public void onDestroy() {
        super.onDestroy();
        stopMonitoring();
        Log.d(TAG, "HeartRateSensorService destroyed");
    }
    
    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null; // Not a bound service
    }
    
    /**
     * Get current heart rate
     * @return current heart rate in BPM, 0 if not being measured
     */
    public int getCurrentHeartRate() {
        return isSensorTouched ? currentHeartRate : 0;
    }
    
    /**
     * Check if sensor is currently being touched
     * @return true if sensor is touched, false otherwise
     */
    public boolean isSensorTouched() {
        return isSensorTouched;
    }
}
