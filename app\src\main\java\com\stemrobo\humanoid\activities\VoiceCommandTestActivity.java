package com.stemrobo.humanoid.activities;

import android.content.SharedPreferences;
import android.os.Bundle;
import android.preference.PreferenceManager;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import com.stemrobo.humanoid.R;
import com.stemrobo.humanoid.services.VoiceRecognitionService;

/**
 * Test Activity for Voice Command Duration Settings
 * This activity allows testing voice commands with configurable durations
 */
public class VoiceCommandTestActivity extends AppCompatActivity {
    private static final String TAG = "VoiceCommandTest";
    
    private TextView forwardDurationText;
    private TextView sideDurationText;
    private Button testForwardButton;
    private Button testBackwardButton;
    private Button testLeftButton;
    private Button testRightButton;
    private Button refreshSettingsButton;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_voice_command_test);
        
        initializeViews();
        setupListeners();
        updateDurationDisplay();
    }
    
    private void initializeViews() {
        forwardDurationText = findViewById(R.id.forward_duration_text);
        sideDurationText = findViewById(R.id.side_duration_text);
        testForwardButton = findViewById(R.id.test_forward_button);
        testBackwardButton = findViewById(R.id.test_backward_button);
        testLeftButton = findViewById(R.id.test_left_button);
        testRightButton = findViewById(R.id.test_right_button);
        refreshSettingsButton = findViewById(R.id.refresh_settings_button);
    }
    
    private void setupListeners() {
        testForwardButton.setOnClickListener(v -> testVoiceCommand("forward"));
        testBackwardButton.setOnClickListener(v -> testVoiceCommand("backward"));
        testLeftButton.setOnClickListener(v -> testVoiceCommand("turn_left"));
        testRightButton.setOnClickListener(v -> testVoiceCommand("turn_right"));
        refreshSettingsButton.setOnClickListener(v -> updateDurationDisplay());
    }
    
    private void testVoiceCommand(String command) {
        // Get current duration settings
        SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(this);
        int duration;
        
        if (command.equals("turn_left") || command.equals("turn_right")) {
            duration = prefs.getInt("voice_side_duration", 2000);
        } else {
            duration = prefs.getInt("voice_forward_duration", 3000);
        }
        
        // Show test message
        String message = String.format("Testing %s command for %.1f seconds", 
                                     command, duration / 1000.0f);
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
        
        // Here you would normally call the VoiceRecognitionService
        // For testing purposes, we'll just simulate the command
        simulateVoiceCommand(command, duration);
    }
    
    private void simulateVoiceCommand(String command, int duration) {
        // This simulates what the VoiceRecognitionService would do
        android.util.Log.d(TAG, String.format("Simulating voice command: %s for %d ms", command, duration));
        
        // In a real implementation, this would send the command to ESP32
        // and stop after the specified duration
    }
    
    private void updateDurationDisplay() {
        SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(this);
        
        int forwardDuration = prefs.getInt("voice_forward_duration", 3000);
        int sideDuration = prefs.getInt("voice_side_duration", 2000);
        
        forwardDurationText.setText(String.format("Forward/Backward Duration: %.1f seconds", 
                                                 forwardDuration / 1000.0f));
        sideDurationText.setText(String.format("Left/Right Duration: %.1f seconds", 
                                              sideDuration / 1000.0f));
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        updateDurationDisplay();
    }
}
