package com.stemrobo.humanoid.vision.yolo;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.view.View;

import java.util.LinkedList;
import java.util.List;

/**
 * Overlay view for displaying YOLO object detection results
 */
public class YoloDetectionOverlay extends View {
    private final List<YoloClassifier.Recognition> results = new LinkedList<>();
    private final Paint boxPaint = new Paint();
    private final Paint textBackgroundPaint = new Paint();
    private final Paint textPaint = new Paint();
    private MultiBoxTracker tracker;

    public YoloDetectionOverlay(Context context, AttributeSet attrs) {
        super(context, attrs);
        initPaints();
    }

    private void initPaints() {
        // Box paint
        boxPaint.setColor(Color.RED);
        boxPaint.setStyle(Paint.Style.STROKE);
        boxPaint.setStrokeWidth(3.0f);

        // Text background paint
        textBackgroundPaint.setColor(Color.BLACK);
        textBackgroundPaint.setAlpha(180);

        // Text paint
        textPaint.setColor(Color.WHITE);
        textPaint.setTextSize(40.0f);
    }

    public void setTracker(MultiBoxTracker tracker) {
        this.tracker = tracker;
    }

    public void setResults(final List<YoloClassifier.Recognition> results) {
        synchronized (this.results) {
            this.results.clear();
            this.results.addAll(results);
        }
        postInvalidate();
    }

    @Override
    public void draw(Canvas canvas) {
        super.draw(canvas);

        if (tracker != null) {
            // Use tracker to draw tracked objects
            tracker.draw(canvas);
        } else {
            // Draw detections directly
            synchronized (results) {
                for (final YoloClassifier.Recognition result : results) {
                    final RectF location = result.getLocation();
                    if (location != null && result.getConfidence() >= 0.5f) {
                        // Scale coordinates to view size
                        float scaleX = getWidth() / 416.0f;  // YOLO input size
                        float scaleY = getHeight() / 416.0f;
                        
                        RectF scaledLocation = new RectF(
                            location.left * scaleX,
                            location.top * scaleY,
                            location.right * scaleX,
                            location.bottom * scaleY
                        );

                        // Draw bounding box
                        canvas.drawRect(scaledLocation, boxPaint);

                        // Prepare label text
                        String labelString = String.format("%s %.1f%%",
                            result.getTitle(),
                            result.getConfidence() * 100);

                        // Draw text background
                        float textWidth = textPaint.measureText(labelString);
                        float textHeight = textPaint.getTextSize();
                        canvas.drawRect(
                            scaledLocation.left,
                            scaledLocation.top - textHeight - 4,
                            scaledLocation.left + textWidth + 8,
                            scaledLocation.top,
                            textBackgroundPaint
                        );

                        // Draw label text
                        canvas.drawText(
                            labelString,
                            scaledLocation.left + 4,
                            scaledLocation.top - 4,
                            textPaint
                        );
                    }
                }
            }
        }
    }

    public void clear() {
        synchronized (results) {
            results.clear();
        }
        postInvalidate();
    }
}