package com.stemrobo.humanoid.database;

import android.content.Context;
import androidx.room.Database;
import androidx.room.Room;
import androidx.room.RoomDatabase;
import androidx.room.migration.Migration;
import androidx.sqlite.db.SupportSQLiteDatabase;

/**
 * Room database for storing person face recognition data and chat conversation history
 * Singleton pattern ensures only one database instance
 */
@Database(
    entities = {Person.class, PersonEmbedding.class, ChatConversation.class, ChatHistory.class},
    version = 3,
    exportSchema = false
)
public abstract class PersonDatabase extends RoomDatabase {
    
    private static final String DATABASE_NAME = "person_database";
    private static PersonDatabase INSTANCE;
    
    /**
     * Get the PersonDao for database operations
     */
    public abstract PersonDao personDao();
    
    /**
     * Get the PersonEmbeddingDao for embedding operations
     */
    public abstract PersonEmbeddingDao personEmbeddingDao();

    /**
     * Get the ChatConversationDao for conversation operations
     */
    public abstract ChatConversationDao chatConversationDao();

    /**
     * Get the ChatHistoryDao for chat history operations
     */
    public abstract ChatHistoryDao chatHistoryDao();
    
    /**
     * Get singleton instance of the database
     */
    public static synchronized PersonDatabase getInstance(Context context) {
        if (INSTANCE == null) {
            INSTANCE = Room.databaseBuilder(
                    context.getApplicationContext(),
                    PersonDatabase.class,
                    DATABASE_NAME
                )
                .allowMainThreadQueries() // For simplicity - in production use background threads
                .addMigrations(MIGRATION_1_2, MIGRATION_2_3)
                .fallbackToDestructiveMigration() // For development - handle migrations properly in production
                .build();
        }
        return INSTANCE;
    }
    
    /**
     * Close database instance (for testing)
     */
    public static void closeDatabase() {
        if (INSTANCE != null && INSTANCE.isOpen()) {
            INSTANCE.close();
            INSTANCE = null;
        }
    }
    
    /**
     * Database callback for initialization
     */
    private static final RoomDatabase.Callback DATABASE_CALLBACK = new RoomDatabase.Callback() {
        @Override
        public void onCreate(SupportSQLiteDatabase db) {
            super.onCreate(db);
            // Database created - can add initial data here if needed
            android.util.Log.d("PersonDatabase", "Database created successfully");
        }
        
        @Override
        public void onOpen(SupportSQLiteDatabase db) {
            super.onOpen(db);
            android.util.Log.d("PersonDatabase", "Database opened");
        }
    };
    
    /**
     * Migration from version 1 to 2 - Add PersonEmbedding table
     */
    private static final Migration MIGRATION_1_2 = new Migration(1, 2) {
        @Override
        public void migrate(SupportSQLiteDatabase database) {
            // Create person_embeddings table
            database.execSQL("CREATE TABLE IF NOT EXISTS `person_embeddings` (" +
                    "`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, " +
                    "`person_id` INTEGER NOT NULL, " +
                    "`embedding_json` TEXT, " +
                    "`created_timestamp` INTEGER NOT NULL, " +
                    "`quality_score` REAL NOT NULL, " +
                    "`is_primary` INTEGER NOT NULL, " +
                    "FOREIGN KEY(`person_id`) REFERENCES `known_persons`(`uid`) ON DELETE CASCADE)");

            // Create index for better performance
            database.execSQL("CREATE INDEX IF NOT EXISTS `index_person_embeddings_person_id` ON `person_embeddings` (`person_id`)");
        }
    };

    /**
     * Migration from version 2 to 3 - Add chat conversation and history tables
     */
    private static final Migration MIGRATION_2_3 = new Migration(2, 3) {
        @Override
        public void migrate(SupportSQLiteDatabase database) {
            // Create chat_conversations table
            database.execSQL("CREATE TABLE IF NOT EXISTS `chat_conversations` (" +
                    "`conversation_id` TEXT NOT NULL, " +
                    "`title` TEXT, " +
                    "`created_timestamp` INTEGER NOT NULL, " +
                    "`last_updated_timestamp` INTEGER NOT NULL, " +
                    "`message_count` INTEGER NOT NULL, " +
                    "`is_active` INTEGER NOT NULL, " +
                    "`language_code` TEXT, " +
                    "PRIMARY KEY(`conversation_id`))");

            // Create chat_history table
            database.execSQL("CREATE TABLE IF NOT EXISTS `chat_history` (" +
                    "`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, " +
                    "`conversation_id` TEXT NOT NULL, " +
                    "`message_text` TEXT, " +
                    "`message_type` INTEGER NOT NULL, " +
                    "`timestamp` INTEGER NOT NULL, " +
                    "`sequence_number` INTEGER NOT NULL, " +
                    "`language_code` TEXT, " +
                    "`response_time_ms` INTEGER NOT NULL, " +
                    "`context_used` TEXT, " +
                    "FOREIGN KEY(`conversation_id`) REFERENCES `chat_conversations`(`conversation_id`) ON DELETE CASCADE)");

            // Create indexes for chat_history
            database.execSQL("CREATE INDEX IF NOT EXISTS `index_chat_history_conversation_id` ON `chat_history` (`conversation_id`)");
            database.execSQL("CREATE INDEX IF NOT EXISTS `index_chat_history_timestamp` ON `chat_history` (`timestamp`)");
        }
    };
}
