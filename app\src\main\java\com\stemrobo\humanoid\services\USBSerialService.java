package com.stemrobo.humanoid.services;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.hardware.usb.UsbDevice;
import android.hardware.usb.UsbDeviceConnection;
import android.hardware.usb.UsbManager;
import android.os.Binder;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.util.Log;

import androidx.core.app.NotificationCompat;

import com.hoho.android.usbserial.driver.UsbSerialDriver;
import com.hoho.android.usbserial.driver.UsbSerialPort;
import com.hoho.android.usbserial.driver.UsbSerialProber;
import com.hoho.android.usbserial.util.SerialInputOutputManager;
import com.stemrobo.humanoid.MainActivity;
import com.stemrobo.humanoid.R;

import java.io.IOException;
import java.util.ArrayDeque;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Enhanced USB Serial Service for ESP32 communication
 * Based on reference implementation from serial terminal app
 */
public class USBSerialService extends Service implements SerialInputOutputManager.Listener {
    private static final String TAG = "USBSerialService";
    private static final String CHANNEL_ID = "USB_SERIAL_CHANNEL";
    private static final int NOTIFICATION_ID = 1001;
    private static final String ACTION_USB_PERMISSION = "com.stemrobo.humanoid.USB_PERMISSION";

    // Serial parameters
    private int baudRate = 115200;
    private int dataBits = 8;
    private int stopBits = UsbSerialPort.STOPBITS_1;
    private int parity = UsbSerialPort.PARITY_NONE;

    // USB components
    private UsbManager usbManager;
    private UsbSerialPort serialPort;
    private UsbDeviceConnection connection;
    private UsbDevice connectedDevice;
    private SerialInputOutputManager ioManager;
    private ExecutorService executorService;

    // Service state
    private boolean isConnected = false;
    private boolean isServiceStarted = false;
    private Handler mainHandler;

    // Data queues for buffering
    private final ArrayDeque<String> writeQueue = new ArrayDeque<>();
    private final ArrayDeque<String> readQueue = new ArrayDeque<>();

    // Listener interface
    public interface SerialListener {
        void onSerialConnect();
        void onSerialDisconnect();
        void onSerialRead(String data);
        void onSerialIoError(Exception e);
    }

    private SerialListener listener;

    // Binder for local service binding
    public class SerialBinder extends Binder {
        public USBSerialService getService() {
            return USBSerialService.this;
        }
    }

    private final IBinder binder = new SerialBinder();

    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "USB Serial Service created");

        mainHandler = new Handler(Looper.getMainLooper());
        executorService = Executors.newSingleThreadExecutor();
        usbManager = (UsbManager) getSystemService(Context.USB_SERVICE);

        createNotificationChannel();

        // CRITICAL: Start foreground immediately to prevent ANR
        startForeground(NOTIFICATION_ID, createNotification("USB Serial Service", "Initializing..."));

        registerUSBReceiver();
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.d(TAG, "USB Serial Service started");

        if (!isServiceStarted) {
            // Update notification to show ready status
            updateNotification("USB Serial Service", "Ready for ESP32 connection");
            isServiceStarted = true;

            // Auto-scan for devices on service start
            scanForDevices();
        }

        return START_STICKY; // Restart if killed
    }

    @Override
    public IBinder onBind(Intent intent) {
        return binder;
    }

    @Override
    public void onDestroy() {
        Log.d(TAG, "USB Serial Service destroyed");
        
        try {
            unregisterReceiver(usbReceiver);
        } catch (Exception e) {
            Log.e(TAG, "Error unregistering USB receiver", e);
        }
        
        disconnect();
        
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
        
        super.onDestroy();
    }

    // USB Permission Broadcast Receiver
    private final BroadcastReceiver usbReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            Log.d(TAG, "USB Broadcast received: " + action);
            
            if (ACTION_USB_PERMISSION.equals(action)) {
                synchronized (this) {
                    UsbDevice device = intent.getParcelableExtra(UsbManager.EXTRA_DEVICE);
                    if (intent.getBooleanExtra(UsbManager.EXTRA_PERMISSION_GRANTED, false)) {
                        if (device != null) {
                            Log.d(TAG, "USB permission granted for device: " + device.getDeviceName());
                            connectToDevice(device);
                        }
                    } else {
                        Log.w(TAG, "USB permission denied for device: " + (device != null ? device.getDeviceName() : "null"));
                        updateNotification("Permission denied", "USB access denied for ESP32");
                    }
                }
            } else if (UsbManager.ACTION_USB_DEVICE_ATTACHED.equals(action)) {
                UsbDevice device = intent.getParcelableExtra(UsbManager.EXTRA_DEVICE);
                Log.d(TAG, "USB device attached: " + (device != null ? device.getDeviceName() : "null"));
                if (device != null && !isConnected) {
                    checkAndRequestPermission(device);
                }
            } else if (UsbManager.ACTION_USB_DEVICE_DETACHED.equals(action)) {
                UsbDevice device = intent.getParcelableExtra(UsbManager.EXTRA_DEVICE);
                Log.d(TAG, "USB device detached: " + (device != null ? device.getDeviceName() : "null"));
                if (device != null && device.equals(connectedDevice)) {
                    disconnect();
                }
            }
        }
    };

    private void registerUSBReceiver() {
        IntentFilter filter = new IntentFilter();
        filter.addAction(ACTION_USB_PERMISSION);
        filter.addAction(UsbManager.ACTION_USB_DEVICE_ATTACHED);
        filter.addAction(UsbManager.ACTION_USB_DEVICE_DETACHED);

        // Fix for Android security requirement: specify RECEIVER_NOT_EXPORTED
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            registerReceiver(usbReceiver, filter, Context.RECEIVER_NOT_EXPORTED);
        } else {
            registerReceiver(usbReceiver, filter);
        }
        Log.d(TAG, "USB receiver registered with proper security flags");
    }

    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                CHANNEL_ID,
                "USB Serial Communication",
                NotificationManager.IMPORTANCE_LOW
            );
            channel.setDescription("ESP32 USB Serial Communication Service");
            
            NotificationManager manager = getSystemService(NotificationManager.class);
            manager.createNotificationChannel(channel);
        }
    }

    private Notification createNotification(String title, String content) {
        Intent notificationIntent = new Intent(this, MainActivity.class);
        PendingIntent pendingIntent = PendingIntent.getActivity(
            this, 0, notificationIntent, 
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
        );

        return new NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle(title)
            .setContentText(content)
            .setSmallIcon(R.drawable.ic_usb_24dp)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .build();
    }

    private void updateNotification(String title, String content) {
        NotificationManager manager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
        manager.notify(NOTIFICATION_ID, createNotification(title, content));
    }

    // Public API methods
    public void setSerialListener(SerialListener listener) {
        this.listener = listener;
    }

    public boolean isConnected() {
        return isConnected && serialPort != null;
    }

    public void scanForDevices() {
        Log.d(TAG, "Scanning for ESP32 USB devices...");
        
        executorService.execute(() -> {
            List<UsbSerialDriver> availableDrivers = UsbSerialProber.getDefaultProber().findAllDrivers(usbManager);
            
            if (availableDrivers.isEmpty()) {
                Log.d(TAG, "No USB serial devices found");
                mainHandler.post(() -> updateNotification("No devices", "No USB serial devices found"));
                return;
            }
            
            for (UsbSerialDriver driver : availableDrivers) {
                UsbDevice device = driver.getDevice();
                int vendorId = device.getVendorId();
                int productId = device.getProductId();
                
                Log.d(TAG, "Found USB device - Vendor ID: 0x" + Integer.toHexString(vendorId) + 
                           ", Product ID: 0x" + Integer.toHexString(productId) + 
                           ", Name: " + device.getDeviceName());
                
                // Check for ESP32 compatible devices
                if (isESP32Device(vendorId, productId)) {
                    Log.d(TAG, "ESP32 compatible device found!");
                    mainHandler.post(() -> {
                        updateNotification("ESP32 found", "ESP32 device detected");
                        checkAndRequestPermission(device);
                    });
                    return;
                }
            }
            
            Log.d(TAG, "No ESP32 compatible devices found");
            mainHandler.post(() -> updateNotification("No ESP32", "No ESP32 devices found"));
        });
    }

    private boolean isESP32Device(int vendorId, int productId) {
        // ESP32 USB CDC devices and common USB-to-Serial chips
        return vendorId == 0x303A ||  // Espressif Systems
               vendorId == 0x10C4 ||  // Silicon Labs CP210x
               vendorId == 0x1A86 ||  // QinHeng Electronics CH340
               vendorId == 0x0403 ||  // FTDI
               vendorId == 0x067B ||  // Prolific PL2303
               vendorId == 0x2341;    // Arduino
    }

    private void checkAndRequestPermission(UsbDevice device) {
        if (usbManager.hasPermission(device)) {
            Log.d(TAG, "USB permission already granted");
            connectToDevice(device);
        } else {
            Log.d(TAG, "Requesting USB permission...");
            PendingIntent permissionIntent = PendingIntent.getBroadcast(
                this, 0, new Intent(ACTION_USB_PERMISSION),
                PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
            );
            usbManager.requestPermission(device, permissionIntent);
            updateNotification("Permission request", "Requesting USB access...");
        }
    }

    private void connectToDevice(UsbDevice device) {
        if (device == null) {
            Log.e(TAG, "No device to connect to");
            return;
        }

        executorService.execute(() -> {
            try {
                List<UsbSerialDriver> drivers = UsbSerialProber.getDefaultProber().findAllDrivers(usbManager);
                UsbSerialDriver driver = null;

                for (UsbSerialDriver d : drivers) {
                    if (d.getDevice().equals(device)) {
                        driver = d;
                        break;
                    }
                }

                if (driver == null) {
                    Log.e(TAG, "No driver found for device");
                    mainHandler.post(() -> updateNotification("Driver error", "No driver found for device"));
                    return;
                }

                connection = usbManager.openDevice(device);
                if (connection == null) {
                    Log.e(TAG, "Failed to open USB device connection");
                    mainHandler.post(() -> updateNotification("Connection failed", "Failed to open USB connection"));
                    return;
                }

                // Use first available port
                List<UsbSerialPort> ports = driver.getPorts();
                if (ports.isEmpty()) {
                    Log.e(TAG, "No ports available on device");
                    mainHandler.post(() -> updateNotification("Port error", "No ports available"));
                    return;
                }

                serialPort = ports.get(0);
                serialPort.open(connection);
                serialPort.setParameters(baudRate, dataBits, stopBits, parity);

                // Set DTR and RTS for proper ESP32 communication
                try {
                    serialPort.setDTR(true);
                    serialPort.setRTS(true);
                } catch (UnsupportedOperationException e) {
                    Log.d(TAG, "DTR/RTS not supported", e);
                }

                // Start I/O manager for automatic read/write handling
                ioManager = new SerialInputOutputManager(serialPort, this);
                ioManager.start();

                connectedDevice = device;
                isConnected = true;

                Log.d(TAG, "USB Serial connection established successfully");
                mainHandler.post(() -> {
                    updateNotification("Connected", "ESP32 connected via USB");
                    if (listener != null) {
                        listener.onSerialConnect();
                    }
                });

                // Process any queued write commands
                processWriteQueue();

            } catch (IOException e) {
                Log.e(TAG, "Error connecting to USB device: " + e.getMessage());
                isConnected = false;
                mainHandler.post(() -> {
                    updateNotification("Connection error", "USB connection failed");
                    if (listener != null) {
                        listener.onSerialIoError(e);
                    }
                });
            }
        });
    }

    public void disconnect() {
        executorService.execute(() -> {
            try {
                // Stop I/O manager first
                if (ioManager != null) {
                    ioManager.stop();
                    ioManager = null;
                }

                // Close serial port
                if (serialPort != null) {
                    serialPort.close();
                    serialPort = null;
                }

                // Close USB connection
                if (connection != null) {
                    connection.close();
                    connection = null;
                }

                isConnected = false;
                connectedDevice = null;

                Log.d(TAG, "USB Serial disconnected");
                mainHandler.post(() -> {
                    updateNotification("Disconnected", "ESP32 disconnected");
                    if (listener != null) {
                        listener.onSerialDisconnect();
                    }
                });

            } catch (IOException e) {
                Log.e(TAG, "Error disconnecting USB: " + e.getMessage());
            }
        });
    }

    public void write(String data) {
        if (!isConnected || serialPort == null) {
            Log.w(TAG, "USB not connected, queuing command: " + data);
            synchronized (writeQueue) {
                writeQueue.add(data);
            }
            return;
        }

        executorService.execute(() -> {
            try {
                String commandWithNewline = data + "\n";
                byte[] bytes = commandWithNewline.getBytes();

                Log.d(TAG, "Sending USB command: " + data);
                serialPort.write(bytes, 1000);
                Log.d(TAG, "USB command sent successfully: " + data);

            } catch (IOException e) {
                Log.e(TAG, "Error sending USB command: " + e.getMessage());
                mainHandler.post(() -> {
                    if (listener != null) {
                        listener.onSerialIoError(e);
                    }
                });
            }
        });
    }

    private void processWriteQueue() {
        synchronized (writeQueue) {
            while (!writeQueue.isEmpty() && isConnected) {
                String command = writeQueue.poll();
                if (command != null) {
                    write(command);
                }
            }
        }
    }

    // SerialInputOutputManager.Listener implementation
    @Override
    public void onNewData(byte[] data) {
        String receivedData = new String(data);
        Log.d(TAG, "USB data received: " + receivedData.trim());

        synchronized (readQueue) {
            readQueue.add(receivedData);
        }

        mainHandler.post(() -> {
            if (listener != null) {
                listener.onSerialRead(receivedData);
            }
        });
    }

    public void onRunError(Exception e) {
        Log.e(TAG, "USB I/O error: " + e.getMessage());
        isConnected = false;

        mainHandler.post(() -> {
            updateNotification("I/O Error", "USB communication error");
            if (listener != null) {
                listener.onSerialIoError(e);
                listener.onSerialDisconnect();
            }
        });
    }

    // Configuration methods
    public void setSerialParameters(int baudRate, int dataBits, int stopBits, int parity) {
        this.baudRate = baudRate;
        this.dataBits = dataBits;
        this.stopBits = stopBits;
        this.parity = parity;

        Log.d(TAG, "Serial parameters updated: " + baudRate + " baud, " + dataBits + " data bits");

        // If already connected, update the connection with new parameters
        if (isConnected && serialPort != null) {
            executorService.execute(() -> {
                try {
                    serialPort.setParameters(baudRate, dataBits, stopBits, parity);
                    Log.d(TAG, "Applied new serial parameters to active connection");
                } catch (Exception e) {
                    Log.e(TAG, "Failed to update serial parameters: " + e.getMessage());
                }
            });
        }
    }

    public int getBaudRate() { return baudRate; }
    public int getDataBits() { return dataBits; }
    public int getStopBits() { return stopBits; }
    public int getParity() { return parity; }
}
