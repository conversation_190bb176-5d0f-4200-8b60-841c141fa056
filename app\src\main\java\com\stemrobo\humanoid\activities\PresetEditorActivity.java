package com.stemrobo.humanoid.activities;

import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.MenuItem;
import android.view.View;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.textfield.TextInputEditText;
import com.stemrobo.humanoid.R;
import com.stemrobo.humanoid.adapters.StepEditorAdapter;
import com.stemrobo.humanoid.database.PresetDao;
import com.stemrobo.humanoid.database.PresetDatabase;
import com.stemrobo.humanoid.models.Preset;
import com.stemrobo.humanoid.models.PresetAction;
import com.stemrobo.humanoid.models.PresetStep;
import com.stemrobo.humanoid.services.PresetExecutionService;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Activity for creating and editing action presets.
 * Provides comprehensive preset editing with step-by-step action configuration.
 */
public class PresetEditorActivity extends AppCompatActivity implements StepEditorAdapter.OnStepActionListener {
    
    private static final String TAG = "PresetEditorActivity";
    public static final String EXTRA_PRESET_ID = "preset_id";
    public static final String EXTRA_PRESET_MODE = "preset_mode";
    public static final String MODE_CREATE = "create";
    public static final String MODE_EDIT = "edit";
    public static final String MODE_DUPLICATE = "duplicate";
    
    // UI Components
    private Toolbar toolbar;
    private TextInputEditText editPresetName, editPresetDescription;
    private Spinner spinnerPresetCategory;
    private TextView textTotalDuration;
    private LinearLayout timelineContainer, layoutEmptySteps;
    private RecyclerView recyclerSteps;
    private Button btnAddStep, btnPreviewPreset, btnTestPreset;
    private Button btnCancel, btnSavePreset;
    
    // Data and Adapters
    private StepEditorAdapter stepAdapter;
    private List<PresetStep> steps;
    private PresetDao presetDao;
    private PresetExecutionService executionService;
    
    // Current preset data
    private Preset currentPreset;
    private String editorMode;
    private long presetId = -1;
    
    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_preset_editor);
        
        initializeComponents();
        setupToolbar();
        setupRecyclerView();
        setupListeners();
        setupCategorySpinner();
        loadPresetData();
    }
    
    private void initializeComponents() {
        // Initialize database
        PresetDatabase database = PresetDatabase.getInstance(this);
        presetDao = database.getPresetDao();
        
        // Initialize execution service
        executionService = new PresetExecutionService(this);
        
        // Initialize UI components
        toolbar = findViewById(R.id.toolbar);
        editPresetName = findViewById(R.id.edit_preset_name);
        editPresetDescription = findViewById(R.id.edit_preset_description);
        spinnerPresetCategory = findViewById(R.id.spinner_preset_category);
        textTotalDuration = findViewById(R.id.text_total_duration);
        timelineContainer = findViewById(R.id.timeline_container);
        layoutEmptySteps = findViewById(R.id.layout_empty_steps);
        recyclerSteps = findViewById(R.id.recycler_steps);
        btnAddStep = findViewById(R.id.btn_add_step);
        btnPreviewPreset = findViewById(R.id.btn_preview_preset);
        btnTestPreset = findViewById(R.id.btn_test_preset);
        btnCancel = findViewById(R.id.btn_cancel);
        btnSavePreset = findViewById(R.id.btn_save_preset);
        
        // Initialize data
        steps = new ArrayList<>();
        
        // Get intent data
        Intent intent = getIntent();
        editorMode = intent.getStringExtra(EXTRA_PRESET_MODE);
        presetId = intent.getLongExtra(EXTRA_PRESET_ID, -1);
        
        if (editorMode == null) {
            editorMode = MODE_CREATE;
        }
    }
    
    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
            
            // Set title based on mode
            switch (editorMode) {
                case MODE_CREATE:
                    getSupportActionBar().setTitle("Create Preset");
                    break;
                case MODE_EDIT:
                    getSupportActionBar().setTitle("Edit Preset");
                    break;
                case MODE_DUPLICATE:
                    getSupportActionBar().setTitle("Duplicate Preset");
                    break;
            }
        }
    }
    
    private void setupRecyclerView() {
        stepAdapter = new StepEditorAdapter(steps, this);
        recyclerSteps.setLayoutManager(new LinearLayoutManager(this));
        recyclerSteps.setAdapter(stepAdapter);
    }
    
    private void setupListeners() {
        // Text change listeners for auto-save and validation
        editPresetName.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}
            
            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                validateForm();
            }
            
            @Override
            public void afterTextChanged(Editable s) {}
        });
        
        // Button listeners
        btnAddStep.setOnClickListener(v -> addNewStep());
        btnPreviewPreset.setOnClickListener(v -> previewPreset());
        btnTestPreset.setOnClickListener(v -> testPreset());
        btnCancel.setOnClickListener(v -> confirmCancel());
        btnSavePreset.setOnClickListener(v -> savePreset());
    }
    
    private void setupCategorySpinner() {
        List<String> categories = new ArrayList<>();
        for (Preset.Category category : Preset.Category.values()) {
            categories.add(category.getDisplayName());
        }
        
        ArrayAdapter<String> adapter = new ArrayAdapter<>(this,
                android.R.layout.simple_spinner_item, categories);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerPresetCategory.setAdapter(adapter);
    }
    
    private void loadPresetData() {
        if (editorMode.equals(MODE_CREATE)) {
            // Initialize with default values
            currentPreset = new Preset();
            currentPreset.setName("");
            currentPreset.setDescription("");
            currentPreset.setCategory(Preset.Category.CUSTOM);
            currentPreset.setSteps(new ArrayList<>());
            updateUI();
        } else if (presetId != -1) {
            // Load existing preset
            new Thread(() -> {
                try {
                    Preset preset = presetDao.getPresetById(presetId);
                    if (preset != null) {
                        runOnUiThread(() -> {
                            currentPreset = preset;
                            if (editorMode.equals(MODE_DUPLICATE)) {
                                // Clear ID for duplication
                                currentPreset.setId(0);
                                currentPreset.setName(currentPreset.getName() + " (Copy)");
                            }
                            populateFields();
                            updateUI();
                        });
                    } else {
                        runOnUiThread(() -> {
                            Toast.makeText(this, "Preset not found", Toast.LENGTH_SHORT).show();
                            finish();
                        });
                    }
                } catch (Exception e) {
                    runOnUiThread(() -> {
                        Toast.makeText(this, "Error loading preset: " + e.getMessage(), 
                                     Toast.LENGTH_SHORT).show();
                        finish();
                    });
                }
            }).start();
        }
    }
    
    private void populateFields() {
        if (currentPreset != null) {
            editPresetName.setText(currentPreset.getName());
            editPresetDescription.setText(currentPreset.getDescription());
            
            // Set category
            Preset.Category[] categories = Preset.Category.values();
            for (int i = 0; i < categories.length; i++) {
                if (categories[i] == currentPreset.getCategory()) {
                    spinnerPresetCategory.setSelection(i);
                    break;
                }
            }
            
            // Load steps with deep copy to avoid reference issues
            steps.clear();
            if (currentPreset.getSteps() != null) {
                for (PresetStep originalStep : currentPreset.getSteps()) {
                    steps.add(new PresetStep(originalStep)); // Deep copy
                }
            }
            stepAdapter.notifyDataSetChanged();
        }
    }
    
    private void updateUI() {
        // Update empty state visibility
        boolean hasSteps = !steps.isEmpty();
        layoutEmptySteps.setVisibility(hasSteps ? View.GONE : View.VISIBLE);
        recyclerSteps.setVisibility(hasSteps ? View.VISIBLE : View.GONE);
        
        // Update total duration
        updateTotalDuration();
        
        // Update timeline visualization
        updateTimeline();
        
        // Validate form
        validateForm();
    }
    
    private void updateTotalDuration() {
        float totalDuration = 0f;
        for (PresetStep step : steps) {
            float stepEnd = step.getStartTimeMs() / 1000f + step.getDurationMs() / 1000f;
            if (stepEnd > totalDuration) {
                totalDuration = stepEnd;
            }
        }
        textTotalDuration.setText(String.format("Total: %.1fs", totalDuration));
    }
    
    private void updateTimeline() {
        timelineContainer.removeAllViews();
        
        if (steps.isEmpty()) {
            TextView emptyText = new TextView(this);
            emptyText.setText("Timeline will appear here when you add steps");
            emptyText.setTextColor(getColor(R.color.text_secondary));
            emptyText.setTextSize(12);
            emptyText.setPadding(16, 16, 16, 16);
            timelineContainer.addView(emptyText);
            return;
        }
        
        // Create timeline visualization
        // TODO: Implement visual timeline with step blocks
        TextView timelineText = new TextView(this);
        timelineText.setText(steps.size() + " steps configured");
        timelineText.setTextColor(getColor(R.color.text_primary));
        timelineText.setTextSize(14);
        timelineText.setPadding(16, 16, 16, 16);
        timelineContainer.addView(timelineText);
    }
    
    private void validateForm() {
        String name = editPresetName.getText().toString().trim();
        boolean isValid = !name.isEmpty() && !steps.isEmpty();
        
        btnSavePreset.setEnabled(isValid);
        btnPreviewPreset.setEnabled(!steps.isEmpty());
        btnTestPreset.setEnabled(!steps.isEmpty());
    }
    
    private void addNewStep() {
        PresetStep newStep = new PresetStep();
        newStep.setName("Step " + (steps.size() + 1));
        newStep.setStartTimeMs(steps.size() * 2000); // 2 second intervals
        newStep.setDurationMs(2000); // 2 second duration
        newStep.setActions(new ArrayList<>());
        
        steps.add(newStep);
        stepAdapter.notifyItemInserted(steps.size() - 1);
        updateUI();
    }
    
    private void previewPreset() {
        // TODO: Implement preset preview dialog
        Toast.makeText(this, "Preview functionality coming soon!", Toast.LENGTH_SHORT).show();
    }
    
    private void testPreset() {
        if (steps.isEmpty()) {
            Toast.makeText(this, "Add some steps first", Toast.LENGTH_SHORT).show();
            return;
        }
        
        // Create temporary preset for testing
        Preset testPreset = createPresetFromForm();
        if (testPreset != null) {
            executionService.executePreset(testPreset, new PresetExecutionService.ExecutionCallback() {
                @Override
                public void onExecutionStarted(Preset preset) {
                    runOnUiThread(() -> {
                        Toast.makeText(PresetEditorActivity.this, "Testing preset...", 
                                     Toast.LENGTH_SHORT).show();
                    });
                }
                
                @Override
                public void onExecutionProgress(Preset preset, int currentStep, int totalSteps, int progressPercent) {
                    // Update progress if needed
                }
                
                @Override
                public void onExecutionCompleted(Preset preset) {
                    runOnUiThread(() -> {
                        Toast.makeText(PresetEditorActivity.this, "Test completed!", 
                                     Toast.LENGTH_SHORT).show();
                    });
                }
                
                @Override
                public void onExecutionError(Preset preset, String error) {
                    runOnUiThread(() -> {
                        Toast.makeText(PresetEditorActivity.this, "Test error: " + error, 
                                     Toast.LENGTH_LONG).show();
                    });
                }
            });
        }
    }

    private void confirmCancel() {
        if (hasUnsavedChanges()) {
            new AlertDialog.Builder(this)
                    .setTitle("Unsaved Changes")
                    .setMessage("You have unsaved changes. Are you sure you want to cancel?")
                    .setPositiveButton("Yes, Cancel", (dialog, which) -> finish())
                    .setNegativeButton("Continue Editing", null)
                    .show();
        } else {
            finish();
        }
    }

    private boolean hasUnsavedChanges() {
        // Check if form has been modified
        String currentName = editPresetName.getText().toString().trim();
        String currentDescription = editPresetDescription.getText().toString().trim();

        if (currentPreset == null) {
            return !currentName.isEmpty() || !currentDescription.isEmpty() || !steps.isEmpty();
        }

        return !currentName.equals(currentPreset.getName()) ||
               !currentDescription.equals(currentPreset.getDescription()) ||
               steps.size() != (currentPreset.getSteps() != null ? currentPreset.getSteps().size() : 0);
    }

    private void savePreset() {
        Preset preset = createPresetFromForm();
        if (preset == null) {
            return;
        }

        new Thread(() -> {
            try {
                if (editorMode.equals(MODE_CREATE) || editorMode.equals(MODE_DUPLICATE)) {
                    long id = presetDao.insertPreset(preset);
                    preset.setId(id);
                } else {
                    preset.setId(currentPreset.getId());
                    presetDao.updatePreset(preset);
                }

                runOnUiThread(() -> {
                    Toast.makeText(this, "Preset saved successfully!", Toast.LENGTH_SHORT).show();
                    setResult(RESULT_OK);
                    finish();
                });

            } catch (Exception e) {
                runOnUiThread(() -> {
                    Toast.makeText(this, "Error saving preset: " + e.getMessage(),
                                 Toast.LENGTH_LONG).show();
                });
            }
        }).start();
    }

    private Preset createPresetFromForm() {
        String name = editPresetName.getText().toString().trim();
        String description = editPresetDescription.getText().toString().trim();

        if (name.isEmpty()) {
            Toast.makeText(this, "Please enter a preset name", Toast.LENGTH_SHORT).show();
            return null;
        }

        if (steps.isEmpty()) {
            Toast.makeText(this, "Please add at least one step", Toast.LENGTH_SHORT).show();
            return null;
        }

        Preset preset = new Preset();
        preset.setName(name);
        preset.setDescription(description);

        // Get selected category
        int categoryIndex = spinnerPresetCategory.getSelectedItemPosition();
        Preset.Category[] categories = Preset.Category.values();
        if (categoryIndex >= 0 && categoryIndex < categories.length) {
            preset.setCategory(categories[categoryIndex]);
        } else {
            preset.setCategory(Preset.Category.CUSTOM);
        }

        preset.setSteps(new ArrayList<>(steps));
        preset.setCreatedAt(System.currentTimeMillis());
        preset.setUpdatedAt(System.currentTimeMillis());

        return preset;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            confirmCancel();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onBackPressed() {
        confirmCancel();
    }

    // StepEditorAdapter.OnStepActionListener implementation
    @Override
    public void onStepUpdated(int position, PresetStep step) {
        if (position >= 0 && position < steps.size()) {
            steps.set(position, step);
            updateUI();
        }
    }

    @Override
    public void onStepDeleted(int position) {
        if (position >= 0 && position < steps.size()) {
            new AlertDialog.Builder(this)
                    .setTitle("Delete Step")
                    .setMessage("Are you sure you want to delete this step?")
                    .setPositiveButton("Delete", (dialog, which) -> {
                        steps.remove(position);
                        stepAdapter.notifyItemRemoved(position);
                        stepAdapter.notifyItemRangeChanged(position, steps.size());
                        updateUI();
                    })
                    .setNegativeButton("Cancel", null)
                    .show();
        }
    }

    @Override
    public void onStepMoved(int fromPosition, int toPosition) {
        if (fromPosition >= 0 && fromPosition < steps.size() &&
            toPosition >= 0 && toPosition < steps.size()) {
            PresetStep step = steps.remove(fromPosition);
            steps.add(toPosition, step);
            stepAdapter.notifyItemMoved(fromPosition, toPosition);
            updateUI();
        }
    }

    @Override
    public void onStepDuplicated(int position) {
        if (position >= 0 && position < steps.size()) {
            PresetStep originalStep = steps.get(position);
            PresetStep duplicatedStep = new PresetStep();
            duplicatedStep.setName(originalStep.getName() + " (Copy)");
            duplicatedStep.setStartTimeMs(originalStep.getStartTimeMs() + originalStep.getDurationMs());
            duplicatedStep.setDurationMs(originalStep.getDurationMs());

            // Deep copy actions
            List<PresetAction> duplicatedActions = new ArrayList<>();
            if (originalStep.getActions() != null) {
                for (PresetAction action : originalStep.getActions()) {
                    PresetAction newAction = new PresetAction();
                    newAction.setType(action.getType());
                    newAction.setDescription(action.getDescription());
                    newAction.setParameters(new HashMap<>(action.getParameters()));
                    duplicatedActions.add(newAction);
                }
            }
            duplicatedStep.setActions(duplicatedActions);

            steps.add(position + 1, duplicatedStep);
            stepAdapter.notifyItemInserted(position + 1);
            updateUI();
        }
    }
}
