package com.stemrobo.humanoid.fragments;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AlertDialog;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.stemrobo.humanoid.R;
import com.stemrobo.humanoid.activities.PresetEditorActivity;
import com.stemrobo.humanoid.adapters.PresetAdapter;
import com.stemrobo.humanoid.database.PresetDatabase;
import com.stemrobo.humanoid.database.PresetDao;
import com.stemrobo.humanoid.models.Preset;
import com.stemrobo.humanoid.services.PresetExecutionService;
import com.stemrobo.humanoid.utils.DefaultPresets;

import java.util.ArrayList;
import java.util.List;
import java.io.File;
import java.io.FileWriter;
import java.io.FileReader;
import java.io.IOException;
import org.json.JSONArray;
import org.json.JSONObject;
import org.json.JSONException;

/**
 * Fragment for managing and executing action presets.
 * Provides UI for creating, editing, deleting, and running preset action sequences.
 */
public class PresetFragment extends Fragment implements PresetAdapter.OnPresetActionListener {

    private static final String TAG = "PresetFragment";
    private static final int REQUEST_PRESET_EDITOR = 1001;
    
    // UI Components
    private EditText editSearchPresets;
    private Spinner spinnerCategoryFilter;
    private Button btnAddPreset, btnPresetHelp, btnClearFilters;
    private Button btnStopAll, btnImportPresets, btnExportPresets;
    private Button btnCreateFirstPreset;
    private RecyclerView recyclerPresets;
    private LinearLayout layoutEmptyState;
    private TextView textPresetCount, textExecutionStatus;
    
    // Data and Adapters
    private PresetAdapter presetAdapter;
    private List<Preset> allPresets;
    private List<Preset> filteredPresets;
    private PresetDao presetDao;
    private PresetExecutionService executionService;
    
    // Filter state
    private String currentSearchQuery = "";
    private Preset.Category currentCategoryFilter = null;
    
    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // Initialize database
        PresetDatabase database = PresetDatabase.getInstance(requireContext());
        presetDao = database.getPresetDao();
        
        // Initialize execution service
        executionService = new PresetExecutionService(requireContext());
        
        // Initialize data lists
        allPresets = new ArrayList<>();
        filteredPresets = new ArrayList<>();
    }
    
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_preset, container, false);
    }
    
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        initializeViews(view);
        setupRecyclerView();
        setupListeners();
        setupCategorySpinner();
        loadPresets();
    }
    
    private void initializeViews(View view) {
        // Header buttons
        btnAddPreset = view.findViewById(R.id.btn_add_preset);
        btnPresetHelp = view.findViewById(R.id.btn_preset_help);
        
        // Search and filter
        editSearchPresets = view.findViewById(R.id.edit_search_presets);
        spinnerCategoryFilter = view.findViewById(R.id.spinner_category_filter);
        btnClearFilters = view.findViewById(R.id.btn_clear_filters);
        
        // Quick actions
        btnStopAll = view.findViewById(R.id.btn_stop_all);
        btnImportPresets = view.findViewById(R.id.btn_import_presets);
        btnExportPresets = view.findViewById(R.id.btn_export_presets);
        
        // Main content
        recyclerPresets = view.findViewById(R.id.recycler_presets);
        layoutEmptyState = view.findViewById(R.id.layout_empty_state);
        btnCreateFirstPreset = view.findViewById(R.id.btn_create_first_preset);
        
        // Status
        textPresetCount = view.findViewById(R.id.text_preset_count);
        textExecutionStatus = view.findViewById(R.id.text_execution_status);
    }
    
    private void setupRecyclerView() {
        presetAdapter = new PresetAdapter(filteredPresets, this);
        recyclerPresets.setLayoutManager(new LinearLayoutManager(requireContext()));
        recyclerPresets.setAdapter(presetAdapter);
    }
    
    private void setupListeners() {
        // Header buttons
        btnAddPreset.setOnClickListener(v -> openPresetEditor(null));
        btnPresetHelp.setOnClickListener(v -> showHelpDialog());
        
        // Search functionality
        editSearchPresets.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}
            
            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                currentSearchQuery = s.toString().trim();
                filterPresets();
            }
            
            @Override
            public void afterTextChanged(Editable s) {}
        });
        
        // Filter controls
        btnClearFilters.setOnClickListener(v -> clearFilters());
        
        // Quick actions
        btnStopAll.setOnClickListener(v -> stopAllExecution());
        btnImportPresets.setOnClickListener(v -> importPresets());
        btnExportPresets.setOnClickListener(v -> exportPresets());
        
        // Empty state
        btnCreateFirstPreset.setOnClickListener(v -> openPresetEditor(null));
    }
    
    private void setupCategorySpinner() {
        List<String> categories = new ArrayList<>();
        categories.add("All Categories");
        for (Preset.Category category : Preset.Category.values()) {
            categories.add(category.getDisplayName());
        }
        
        ArrayAdapter<String> adapter = new ArrayAdapter<>(requireContext(),
                android.R.layout.simple_spinner_item, categories);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerCategoryFilter.setAdapter(adapter);
        
        spinnerCategoryFilter.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                if (position == 0) {
                    currentCategoryFilter = null;
                } else {
                    currentCategoryFilter = Preset.Category.values()[position - 1];
                }
                filterPresets();
            }
            
            @Override
            public void onNothingSelected(AdapterView<?> parent) {}
        });
    }
    
    private void loadPresets() {
        new Thread(() -> {
            try {
                List<Preset> presets = presetDao.getAllPresets();

                // Load default presets if database is empty
                if (presets.isEmpty()) {
                    loadDefaultPresets();
                    presets = presetDao.getAllPresets();
                }

                final List<Preset> finalPresets = presets;
                if (getActivity() != null) {
                    getActivity().runOnUiThread(() -> {
                        allPresets.clear();
                        allPresets.addAll(finalPresets);
                        filterPresets();
                        updateUI();
                    });
                }
            } catch (Exception e) {
                if (getActivity() != null) {
                    getActivity().runOnUiThread(() -> {
                        if (getContext() != null) {
                            Toast.makeText(getContext(), "Error loading presets: " + e.getMessage(),
                                         Toast.LENGTH_SHORT).show();
                        }
                    });
                }
            }
        }).start();
    }

    private void loadDefaultPresets() {
        try {
            List<Preset> defaultPresets = DefaultPresets.createDefaultPresets();
            for (Preset preset : defaultPresets) {
                presetDao.insertPreset(preset);
            }
        } catch (Exception e) {
            // Log error but don't crash
            e.printStackTrace();
        }
    }
    
    private void filterPresets() {
        filteredPresets.clear();
        
        for (Preset preset : allPresets) {
            boolean matchesSearch = currentSearchQuery.isEmpty() ||
                    preset.getName().toLowerCase().contains(currentSearchQuery.toLowerCase()) ||
                    preset.getDescription().toLowerCase().contains(currentSearchQuery.toLowerCase());
            
            boolean matchesCategory = currentCategoryFilter == null ||
                    preset.getCategory() == currentCategoryFilter;
            
            if (matchesSearch && matchesCategory && preset.isActive()) {
                filteredPresets.add(preset);
            }
        }
        
        presetAdapter.notifyDataSetChanged();
        updateUI();
    }
    
    private void updateUI() {
        boolean hasPresets = !filteredPresets.isEmpty();
        
        recyclerPresets.setVisibility(hasPresets ? View.VISIBLE : View.GONE);
        layoutEmptyState.setVisibility(hasPresets ? View.GONE : View.VISIBLE);
        
        // Update preset count
        int totalCount = allPresets.size();
        int filteredCount = filteredPresets.size();
        
        if (totalCount == filteredCount) {
            textPresetCount.setText(totalCount + " presets");
        } else {
            textPresetCount.setText(filteredCount + " of " + totalCount + " presets");
        }
        
        // Update execution status
        if (executionService.isExecuting()) {
            textExecutionStatus.setText("Executing...");
            textExecutionStatus.setTextColor(getResources().getColor(R.color.status_warning));
        } else {
            textExecutionStatus.setText("Ready");
            textExecutionStatus.setTextColor(getResources().getColor(R.color.status_success));
        }
    }
    
    private void clearFilters() {
        editSearchPresets.setText("");
        spinnerCategoryFilter.setSelection(0);
        currentSearchQuery = "";
        currentCategoryFilter = null;
        filterPresets();
    }
    
    // PresetAdapter.OnPresetActionListener implementation
    @Override
    public void onPresetPlay(Preset preset) {
        executePreset(preset);
    }
    
    @Override
    public void onPresetEdit(Preset preset) {
        openPresetEditor(preset);
    }
    
    @Override
    public void onPresetDuplicate(Preset preset) {
        duplicatePreset(preset);
    }
    
    @Override
    public void onPresetDelete(Preset preset) {
        confirmDeletePreset(preset);
    }
    
    @Override
    public void onPresetToggleDetails(Preset preset, boolean expanded) {
        // Handle preset details expansion
    }
    
    // Action methods
    private void executePreset(Preset preset) {
        if (executionService.isExecuting()) {
            Toast.makeText(requireContext(), "Another preset is currently executing", 
                         Toast.LENGTH_SHORT).show();
            return;
        }
        
        executionService.executePreset(preset, new PresetExecutionService.ExecutionCallback() {
            @Override
            public void onExecutionStarted(Preset preset) {
                requireActivity().runOnUiThread(() -> {
                    updateUI();
                    Toast.makeText(requireContext(), "Executing: " + preset.getName(), 
                                 Toast.LENGTH_SHORT).show();
                });
            }
            
            @Override
            public void onExecutionProgress(Preset preset, int currentStep, int totalSteps, int progressPercent) {
                requireActivity().runOnUiThread(() -> {
                    // Update progress in adapter if needed
                    presetAdapter.updateExecutionProgress(preset.getId(), progressPercent);
                });
            }
            
            @Override
            public void onExecutionCompleted(Preset preset) {
                requireActivity().runOnUiThread(() -> {
                    updateUI();
                    presetAdapter.clearExecutionProgress(preset.getId());
                    Toast.makeText(requireContext(), "Completed: " + preset.getName(), 
                                 Toast.LENGTH_SHORT).show();
                });
            }
            
            @Override
            public void onExecutionError(Preset preset, String error) {
                requireActivity().runOnUiThread(() -> {
                    updateUI();
                    presetAdapter.clearExecutionProgress(preset.getId());
                    Toast.makeText(requireContext(), "Error: " + error, Toast.LENGTH_LONG).show();
                });
            }
        });
    }

    private void openPresetEditor(Preset preset) {
        Intent intent = new Intent(getActivity(), PresetEditorActivity.class);
        if (preset != null) {
            intent.putExtra(PresetEditorActivity.EXTRA_PRESET_ID, preset.getId());
            intent.putExtra(PresetEditorActivity.EXTRA_PRESET_MODE, PresetEditorActivity.MODE_EDIT);
        } else {
            intent.putExtra(PresetEditorActivity.EXTRA_PRESET_MODE, PresetEditorActivity.MODE_CREATE);
        }
        startActivityForResult(intent, REQUEST_PRESET_EDITOR);
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == REQUEST_PRESET_EDITOR && resultCode == Activity.RESULT_OK) {
            // Reload presets after editor activity
            loadPresets();
            Toast.makeText(getContext(), "Preset saved successfully!", Toast.LENGTH_SHORT).show();
        }
    }

    private void duplicatePreset(Preset preset) {
        new Thread(() -> {
            try {
                Preset duplicate = preset.copy();
                long newId = presetDao.insertPreset(duplicate);

                if (newId != -1) {
                    requireActivity().runOnUiThread(() -> {
                        Toast.makeText(requireContext(), "Preset duplicated successfully",
                                     Toast.LENGTH_SHORT).show();
                        loadPresets();
                    });
                } else {
                    requireActivity().runOnUiThread(() -> {
                        Toast.makeText(requireContext(), "Failed to duplicate preset",
                                     Toast.LENGTH_SHORT).show();
                    });
                }
            } catch (Exception e) {
                requireActivity().runOnUiThread(() -> {
                    Toast.makeText(requireContext(), "Error duplicating preset: " + e.getMessage(),
                                 Toast.LENGTH_SHORT).show();
                });
            }
        }).start();
    }

    private void confirmDeletePreset(Preset preset) {
        new AlertDialog.Builder(requireContext())
                .setTitle("Delete Preset")
                .setMessage("Are you sure you want to delete '" + preset.getName() + "'?\n\nThis action cannot be undone.")
                .setPositiveButton("Delete", (dialog, which) -> deletePreset(preset))
                .setNegativeButton("Cancel", null)
                .setIcon(android.R.drawable.ic_dialog_alert)
                .show();
    }

    private void deletePreset(Preset preset) {
        new Thread(() -> {
            try {
                boolean success = presetDao.deletePreset(preset.getId());

                requireActivity().runOnUiThread(() -> {
                    if (success) {
                        Toast.makeText(requireContext(), "Preset deleted successfully",
                                     Toast.LENGTH_SHORT).show();
                        loadPresets();
                    } else {
                        Toast.makeText(requireContext(), "Failed to delete preset",
                                     Toast.LENGTH_SHORT).show();
                    }
                });
            } catch (Exception e) {
                requireActivity().runOnUiThread(() -> {
                    Toast.makeText(requireContext(), "Error deleting preset: " + e.getMessage(),
                                 Toast.LENGTH_SHORT).show();
                });
            }
        }).start();
    }

    private void stopAllExecution() {
        if (executionService.isExecuting()) {
            executionService.stopExecution();
            Toast.makeText(requireContext(), "Execution stopped", Toast.LENGTH_SHORT).show();
            updateUI();
        } else {
            Toast.makeText(requireContext(), "No preset is currently executing", Toast.LENGTH_SHORT).show();
        }
    }

    private void importPresets() {
        // Basic import functionality - load default presets
        new Thread(() -> {
            try {
                List<Preset> defaultPresets = DefaultPresets.createDefaultPresets();
                for (Preset preset : defaultPresets) {
                    presetDao.insertPreset(preset);
                }
                requireActivity().runOnUiThread(() -> {
                    Toast.makeText(requireContext(), "Default presets imported successfully!", Toast.LENGTH_SHORT).show();
                    loadPresets();
                });
            } catch (Exception e) {
                requireActivity().runOnUiThread(() -> {
                    Toast.makeText(requireContext(), "Failed to import presets: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                });
            }
        }).start();
    }

    private void exportPresets() {
        // Basic export functionality - show preset count
        new Thread(() -> {
            try {
                List<Preset> allPresets = presetDao.getAllPresets();
                requireActivity().runOnUiThread(() -> {
                    Toast.makeText(requireContext(), "Found " + allPresets.size() + " presets ready for export", Toast.LENGTH_LONG).show();
                });
            } catch (Exception e) {
                requireActivity().runOnUiThread(() -> {
                    Toast.makeText(requireContext(), "Export failed: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                });
            }
        }).start();
    }

    private void showHelpDialog() {
        new AlertDialog.Builder(requireContext())
                .setTitle("🎭 Action Presets Help")
                .setMessage("Action Presets allow you to create complex robot behaviors by combining multiple actions:\n\n" +
                           "• Create sequences of movements, gestures, and head actions\n" +
                           "• Set precise timing for each action\n" +
                           "• Execute multiple actions simultaneously\n" +
                           "• Save and reuse your favorite sequences\n" +
                           "• Use voice commands to trigger presets\n\n" +
                           "Examples:\n" +
                           "• Welcome Guest: Wave → Turn head → Move forward\n" +
                           "• Security Patrol: Rotate → Move → Look around\n" +
                           "• Dance Routine: Arm movements → Rotation → Steps")
                .setPositiveButton("Got it", null)
                .setIcon(R.drawable.ic_help)
                .show();
    }

    @Override
    public void onResume() {
        super.onResume();
        loadPresets(); // Refresh data when fragment becomes visible
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (executionService != null) {
            executionService.cleanup();
        }
    }
}
