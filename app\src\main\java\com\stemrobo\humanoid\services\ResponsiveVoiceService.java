package com.stemrobo.humanoid.services;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.webkit.JavascriptInterface;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.webkit.WebChromeClient;
import android.webkit.PermissionRequest;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.media.AudioManager;
import android.media.AudioAttributes;
import android.media.AudioFocusRequest;
import android.os.Build;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.view.WindowManager;
import android.graphics.PixelFormat;
import android.app.Activity;

/**
 * ResponsiveVoice TTS Service for high-quality male voice synthesis
 * Uses ResponsiveVoice API with WebView integration for better voice quality
 */
public class ResponsiveVoiceService {
    private static final String TAG = "ResponsiveVoiceService";
    
    private Context context;
    private WebView webView;
    private Handler mainHandler;
    private boolean isInitialized = false;
    private boolean isReady = false;
    private boolean isSpeaking = false;

    // Audio management
    private AudioManager audioManager;
    private AudioFocusRequest audioFocusRequest;
    private boolean hasAudioFocus = false;

    // WebView overlay for proper audio playback
    private WindowManager windowManager;
    private WindowManager.LayoutParams overlayParams;
    
    // Callback interfaces
    public interface ResponsiveVoiceCallback {
        void onReady();
        void onError(String error);
        void onSpeechStart();
        void onSpeechEnd();
        void onSpeechError(String error);
    }
    
    private ResponsiveVoiceCallback callback;
    
    public ResponsiveVoiceService(Context context) {
        this.context = context;
        this.mainHandler = new Handler(Looper.getMainLooper());
        this.audioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
        this.windowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
    }
    
    /**
     * Initialize ResponsiveVoice service
     */
    public void initialize(ResponsiveVoiceCallback callback) {
        this.callback = callback;
        
        if (!isNetworkAvailable()) {
            Log.w(TAG, "No network connection available for ResponsiveVoice");
            if (callback != null) {
                callback.onError("No internet connection");
            }
            return;
        }
        
        mainHandler.post(() -> {
            try {
                setupWebView();
                loadResponsiveVoiceHTML();
                Log.d(TAG, "ResponsiveVoice initialization started");
            } catch (Exception e) {
                Log.e(TAG, "Error initializing ResponsiveVoice", e);
                if (callback != null) {
                    callback.onError("Initialization failed: " + e.getMessage());
                }
            }
        });
    }
    
    /**
     * Setup WebView for ResponsiveVoice with enhanced audio support
     * CRITICAL: Create overlay WebView for proper audio playback
     */
    private void setupWebView() {
        try {
            // Create WebView
            webView = new WebView(context);
            WebSettings webSettings = webView.getSettings();

            // Enable JavaScript (required for ResponsiveVoice)
            webSettings.setJavaScriptEnabled(true);

            // Enable DOM storage
            webSettings.setDomStorageEnabled(true);

            // Allow mixed content (HTTP/HTTPS)
            webSettings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);

            // Set user agent
            webSettings.setUserAgentString("Mozilla/5.0 (Linux; Android 10) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36");

            // CRITICAL: Enable media playback without user gesture
            webSettings.setMediaPlaybackRequiresUserGesture(false);

            // Additional audio-related settings
            webSettings.setAllowFileAccess(true);
            webSettings.setAllowContentAccess(true);
            webSettings.setAllowUniversalAccessFromFileURLs(true);
            webSettings.setAllowFileAccessFromFileURLs(true);

            // Enable hardware acceleration for better audio performance
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                webView.setLayerType(WebView.LAYER_TYPE_HARDWARE, null);
            }

            // CRITICAL: Create overlay window for WebView to enable audio
            createOverlayWebView();

            // Add JavaScript interface for Android communication
            webView.addJavascriptInterface(new ResponsiveVoiceJSInterface(), "Android");

            // Setup WebView client
            webView.setWebViewClient(new WebViewClient() {
                @Override
                public void onPageFinished(WebView view, String url) {
                    super.onPageFinished(view, url);
                    Log.d(TAG, "ResponsiveVoice HTML loaded successfully");
                    isInitialized = true;

                    // Request audio focus when page is loaded
                    requestAudioFocus();

                    // Simulate user interaction to unlock audio
                    simulateUserInteraction();
                }

                @Override
                public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
                    super.onReceivedError(view, errorCode, description, failingUrl);
                    Log.e(TAG, "WebView error: " + description);
                    if (callback != null) {
                        callback.onError("WebView error: " + description);
                    }
                }
            });

            // Setup WebChromeClient for media permissions
            webView.setWebChromeClient(new WebChromeClient() {
                @Override
                public void onPermissionRequest(PermissionRequest request) {
                    // Grant audio permissions automatically
                    request.grant(request.getResources());
                    Log.d(TAG, "Audio permissions granted to WebView");
                }
            });

            Log.d(TAG, "WebView configured for ResponsiveVoice with enhanced audio support");

        } catch (Exception e) {
            Log.e(TAG, "Error setting up WebView", e);
            if (callback != null) {
                callback.onError("WebView setup failed: " + e.getMessage());
            }
        }
    }

    /**
     * CRITICAL: Create overlay WebView for proper audio playback
     * Hidden WebViews often can't play audio on Android
     */
    private void createOverlayWebView() {
        try {
            if (windowManager != null) {
                // Create overlay parameters - tiny invisible overlay
                overlayParams = new WindowManager.LayoutParams(
                    1, 1, // 1x1 pixel size
                    Build.VERSION.SDK_INT >= Build.VERSION_CODES.O
                        ? WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                        : WindowManager.LayoutParams.TYPE_PHONE,
                    WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE |
                    WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE |
                    WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN,
                    PixelFormat.TRANSLUCENT
                );

                // Position at top-left corner (invisible)
                overlayParams.x = 0;
                overlayParams.y = 0;

                // Add WebView to window manager
                windowManager.addView(webView, overlayParams);
                Log.d(TAG, "WebView overlay created for audio playback");
            }
        } catch (Exception e) {
            Log.w(TAG, "Could not create WebView overlay (will try alternative): " + e.getMessage());
            // Fallback: just set layout params normally
            webView.setLayoutParams(new FrameLayout.LayoutParams(1, 1));
        }
    }

    /**
     * Simulate user interaction to unlock audio playback
     */
    private void simulateUserInteraction() {
        try {
            // Execute JavaScript to simulate user interaction
            webView.evaluateJavascript("document.body.click();", null);
            Log.d(TAG, "User interaction simulated for audio unlock");
        } catch (Exception e) {
            Log.w(TAG, "Could not simulate user interaction: " + e.getMessage());
        }
    }
    
    /**
     * Load ResponsiveVoice HTML template
     */
    private void loadResponsiveVoiceHTML() {
        try {
            webView.loadUrl("file:///android_asset/responsive_voice.html");
            Log.d(TAG, "Loading ResponsiveVoice HTML template");
        } catch (Exception e) {
            Log.e(TAG, "Error loading ResponsiveVoice HTML", e);
            if (callback != null) {
                callback.onError("Failed to load HTML template: " + e.getMessage());
            }
        }
    }
    
    /**
     * Speak text using ResponsiveVoice
     */
    public void speak(String text, String gender, float rate, float pitch) {
        if (!isReady) {
            Log.w(TAG, "ResponsiveVoice not ready, cannot speak");
            if (callback != null) {
                callback.onSpeechError("Service not ready");
            }
            return;
        }
        
        if (!isNetworkAvailable()) {
            Log.w(TAG, "No network connection for ResponsiveVoice speech");
            if (callback != null) {
                callback.onSpeechError("No internet connection");
            }
            return;
        }
        
        mainHandler.post(() -> {
            try {
                // Escape text for JavaScript
                String escapedText = text.replace("'", "\\'").replace("\"", "\\\"").replace("\n", " ");
                
                // Call JavaScript function
                String jsCommand = String.format(
                    "speakText('%s', '%s', %f, %f);",
                    escapedText, gender, rate, pitch
                );
                
                webView.evaluateJavascript(jsCommand, null);
                Log.d(TAG, "ResponsiveVoice speech command sent: " + gender + " voice");
                
            } catch (Exception e) {
                Log.e(TAG, "Error executing ResponsiveVoice speech", e);
                if (callback != null) {
                    callback.onSpeechError("Speech execution failed: " + e.getMessage());
                }
            }
        });
    }
    
    /**
     * Stop current speech
     */
    public void stopSpeech() {
        if (!isReady) return;
        
        mainHandler.post(() -> {
            try {
                webView.evaluateJavascript("stopSpeech();", null);
                Log.d(TAG, "ResponsiveVoice speech stopped");
            } catch (Exception e) {
                Log.e(TAG, "Error stopping ResponsiveVoice speech", e);
            }
        });
    }
    
    /**
     * Check if currently speaking
     */
    public boolean isSpeaking() {
        return isSpeaking;
    }
    
    /**
     * Check if service is ready
     */
    public boolean isReady() {
        return isReady;
    }

    /**
     * Test ResponsiveVoice functionality
     */
    public void testResponsiveVoice() {
        if (!isReady) {
            Log.w(TAG, "ResponsiveVoice not ready for testing");
            return;
        }

        mainHandler.post(() -> {
            try {
                webView.evaluateJavascript("testResponsiveVoice();", null);
                Log.d(TAG, "ResponsiveVoice test function called");
            } catch (Exception e) {
                Log.e(TAG, "Error testing ResponsiveVoice", e);
            }
        });
    }
    
    /**
     * Request audio focus for ResponsiveVoice playback
     */
    private void requestAudioFocus() {
        try {
            if (audioManager != null) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    // Android 8.0+ audio focus request
                    AudioAttributes audioAttributes = new AudioAttributes.Builder()
                            .setUsage(AudioAttributes.USAGE_MEDIA)
                            .setContentType(AudioAttributes.CONTENT_TYPE_SPEECH)
                            .build();

                    audioFocusRequest = new AudioFocusRequest.Builder(AudioManager.AUDIOFOCUS_GAIN_TRANSIENT)
                            .setAudioAttributes(audioAttributes)
                            .setAcceptsDelayedFocusGain(true)
                            .setOnAudioFocusChangeListener(focusChange -> {
                                Log.d(TAG, "Audio focus changed: " + focusChange);
                                hasAudioFocus = (focusChange == AudioManager.AUDIOFOCUS_GAIN ||
                                                focusChange == AudioManager.AUDIOFOCUS_GAIN_TRANSIENT);
                            })
                            .build();

                    int result = audioManager.requestAudioFocus(audioFocusRequest);
                    hasAudioFocus = (result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED);
                } else {
                    // Legacy audio focus request
                    int result = audioManager.requestAudioFocus(
                            focusChange -> {
                                Log.d(TAG, "Audio focus changed: " + focusChange);
                                hasAudioFocus = (focusChange == AudioManager.AUDIOFOCUS_GAIN ||
                                                focusChange == AudioManager.AUDIOFOCUS_GAIN_TRANSIENT);
                            },
                            AudioManager.STREAM_MUSIC,
                            AudioManager.AUDIOFOCUS_GAIN_TRANSIENT
                    );
                    hasAudioFocus = (result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED);
                }

                Log.d(TAG, "Audio focus requested: " + (hasAudioFocus ? "GRANTED" : "DENIED"));
            }
        } catch (Exception e) {
            Log.e(TAG, "Error requesting audio focus", e);
            hasAudioFocus = false;
        }
    }

    /**
     * Release audio focus
     */
    private void releaseAudioFocus() {
        try {
            if (audioManager != null && hasAudioFocus) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O && audioFocusRequest != null) {
                    audioManager.abandonAudioFocusRequest(audioFocusRequest);
                } else {
                    audioManager.abandonAudioFocus(null);
                }
                hasAudioFocus = false;
                Log.d(TAG, "Audio focus released");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error releasing audio focus", e);
        }
    }

    /**
     * Check network availability
     */
    private boolean isNetworkAvailable() {
        try {
            ConnectivityManager connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
            NetworkInfo activeNetworkInfo = connectivityManager.getActiveNetworkInfo();
            return activeNetworkInfo != null && activeNetworkInfo.isConnected();
        } catch (Exception e) {
            Log.e(TAG, "Error checking network availability", e);
            return false;
        }
    }
    
    /**
     * Cleanup resources
     */
    public void destroy() {
        mainHandler.post(() -> {
            try {
                // Release audio focus
                releaseAudioFocus();

                // Remove WebView from overlay
                if (webView != null && windowManager != null) {
                    try {
                        windowManager.removeView(webView);
                        Log.d(TAG, "WebView overlay removed");
                    } catch (Exception e) {
                        Log.w(TAG, "Could not remove WebView overlay: " + e.getMessage());
                    }
                }

                if (webView != null) {
                    webView.destroy();
                    webView = null;
                }

                isInitialized = false;
                isReady = false;
                isSpeaking = false;
                Log.d(TAG, "ResponsiveVoice service destroyed");
            } catch (Exception e) {
                Log.e(TAG, "Error destroying ResponsiveVoice service", e);
            }
        });
    }
    
    /**
     * JavaScript interface for communication between WebView and Android
     */
    private class ResponsiveVoiceJSInterface {
        
        @JavascriptInterface
        public void onResponsiveVoiceReady() {
            Log.d(TAG, "ResponsiveVoice is ready");
            isReady = true;
            if (callback != null) {
                mainHandler.post(() -> callback.onReady());
            }
        }
        
        @JavascriptInterface
        public void onResponsiveVoiceError(String error) {
            Log.e(TAG, "ResponsiveVoice error: " + error);
            isReady = false;
            if (callback != null) {
                mainHandler.post(() -> callback.onError(error));
            }
        }
        
        @JavascriptInterface
        public void onSpeechStart() {
            Log.d(TAG, "ResponsiveVoice speech started");
            isSpeaking = true;
            if (callback != null) {
                mainHandler.post(() -> callback.onSpeechStart());
            }
        }
        
        @JavascriptInterface
        public void onSpeechEnd() {
            Log.d(TAG, "ResponsiveVoice speech ended");
            isSpeaking = false;
            if (callback != null) {
                mainHandler.post(() -> callback.onSpeechEnd());
            }
        }
        
        @JavascriptInterface
        public void onSpeechError(String error) {
            Log.e(TAG, "ResponsiveVoice speech error: " + error);
            isSpeaking = false;
            if (callback != null) {
                mainHandler.post(() -> callback.onSpeechError(error));
            }
        }
    }
}
