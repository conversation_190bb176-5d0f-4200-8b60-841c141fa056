<resources>
    <string name="app_name"><PERSON>ji - STEM Robot</string>
    
    <!-- Navigation -->
    <string name="nav_control">Control</string>
    <string name="nav_chat">AI Chat</string>
    <string name="nav_settings">Settings</string>
    <string name="nav_vision">Vision</string>
    <string name="nav_preset">Presets</string>
    <string name="nav_status">Status</string>
    
    <!-- Control Screen -->
    <string name="control_title">Guruji Control</string>
    <string name="move_forward">Forward</string>
    <string name="move_backward">Backward</string>
    <string name="turn_left">Turn Left</string>
    <string name="turn_right">Turn Right</string>
    <string name="stop_movement">Stop</string>
    
    <!-- Arm Control -->
    <string name="arm_control_title">Arm Control</string>
    <string name="left_arm">Left Arm</string>
    <string name="right_arm">Right Arm</string>
    <string name="shoulder">Shoulder</string>
    <string name="elbow">Elbow</string>
    <string name="wrist">Wrist</string>
    <string name="wave_gesture">Wave</string>
    <string name="point_gesture">Point</string>
    <string name="rest_position">Rest</string>
    
    <!-- Head Control -->
    <string name="head_control_title">Head Control</string>
    <string name="pan_left">Pan Left</string>
    <string name="pan_right">Pan Right</string>
    <string name="tilt_up">Tilt Up</string>
    <string name="tilt_down">Tilt Down</string>
    <string name="center_head">Center</string>
    
    <!-- Vision Screen -->
    <string name="vision_title">Computer Vision</string>
    <string name="camera_preview">Camera Preview</string>
    <string name="person_detection">Person Detection</string>
    <string name="object_detection">Object Detection</string>
    <string name="gesture_recognition">Gesture Recognition</string>
    <string name="follow_person">Follow Person</string>
    <string name="detection_enabled">Detection Enabled</string>
    <string name="detection_disabled">Detection Disabled</string>
    
    <!-- Status Screen -->
    <string name="status_title">System Status</string>
    <string name="connection_status">Connection Status</string>
    <string name="motor_controller">Motor Controller</string>
    <string name="servo_controller">Servo Controller</string>
    <string name="sensor_controller">Sensor Controller</string>
    <string name="connected">Connected</string>
    <string name="disconnected">Disconnected</string>
    <string name="connecting">Connecting...</string>
    
    <!-- Voice Control -->
    <string name="voice_title">Voice Control</string>
    <string name="voice_listening">Listening...</string>
    <string name="voice_processing">Processing...</string>
    <string name="voice_wake_word">Say \"Hey Guruji\" to wake up</string>
    <string name="voice_command_received">Command received</string>

    <!-- Voice Gender Options -->
    <string-array name="voice_gender_options">
        <item>Female Voice</item>
        <item>Male Voice</item>
    </string-array>
    
    <!-- Sensors -->
    <string name="sensor_title">Sensors</string>
    <string name="heart_rate">Heart Rate</string>
    <string name="battery_level">Battery Level</string>
    <string name="temperature">Temperature</string>
    <string name="distance_sensor">Distance</string>
    
    <!-- Error Messages -->
    <string name="error_connection_failed">Connection failed</string>
    <string name="error_command_failed">Command failed</string>
    <string name="error_camera_unavailable">Camera unavailable</string>
    <string name="error_microphone_unavailable">Microphone unavailable</string>
    <string name="error_permission_required">Permission required</string>
    
    <!-- Success Messages -->
    <string name="success_connected">Successfully connected</string>
    <string name="success_command_sent">Command sent</string>
    <string name="success_initialized">System initialized</string>
    
    <!-- General -->
    <string name="ok">OK</string>
    <string name="cancel">Cancel</string>
    <string name="retry">Retry</string>
    <string name="settings">Settings</string>
    <string name="about">About</string>
    <string name="version">Version</string>
</resources>
