package com.stemrobo.humanoid.adapters;

import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.stemrobo.humanoid.R;
import com.stemrobo.humanoid.models.PresetAction;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Adapter for editing individual actions within preset steps.
 * Handles different action types and their specific parameters.
 */
public class ActionEditorAdapter extends RecyclerView.Adapter<ActionEditorAdapter.ActionViewHolder> {
    
    private List<PresetAction> actions;
    private OnActionChangeListener listener;
    
    public interface OnActionChangeListener {
        void onActionUpdated(int position, PresetAction action);
        void onActionDeleted(int position);
    }
    
    public ActionEditorAdapter(List<PresetAction> actions, OnActionChangeListener listener) {
        this.actions = actions;
        this.listener = listener;
    }
    
    @NonNull
    @Override
    public ActionViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_action_editor, parent, false);
        return new ActionViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull ActionViewHolder holder, int position) {
        PresetAction action = actions.get(position);
        holder.bind(action, position);
    }
    
    @Override
    public int getItemCount() {
        return actions.size();
    }
    
    class ActionViewHolder extends RecyclerView.ViewHolder {
        private TextView textActionIcon;
        private Spinner spinnerActionType;
        private Button btnDeleteAction;
        private EditText editActionDescription;
        private LinearLayout layoutParameters;
        
        // Parameter layouts
        private LinearLayout layoutMovementParams, layoutGestureParams, layoutHandParams;
        private LinearLayout layoutHeadParams, layoutSpeechParams, layoutDelayParams, layoutCustomParams;
        
        // Parameter controls
        private Spinner spinnerMovementDirection, spinnerMovementSpeed, spinnerGestureType, spinnerGestureIntensity;
        private Spinner spinnerHandSide, spinnerHandPosition, spinnerHeadAction, spinnerHeadDirection;
        private Spinner spinnerVoiceGender;
        private EditText editMovementDuration, editHandAngle, editHeadAngle, editSpeechText;
        private EditText editVoiceSpeed, editVoicePitch, editDelayDuration, editCustomCommand;
        
        public ActionViewHolder(@NonNull View itemView) {
            super(itemView);
            
            textActionIcon = itemView.findViewById(R.id.text_action_icon);
            spinnerActionType = itemView.findViewById(R.id.spinner_action_type);
            btnDeleteAction = itemView.findViewById(R.id.btn_delete_action);
            editActionDescription = itemView.findViewById(R.id.edit_action_description);
            layoutParameters = itemView.findViewById(R.id.layout_parameters);
            
            // Parameter layouts
            layoutMovementParams = itemView.findViewById(R.id.layout_movement_params);
            layoutGestureParams = itemView.findViewById(R.id.layout_gesture_params);
            layoutHandParams = itemView.findViewById(R.id.layout_hand_params);
            layoutHeadParams = itemView.findViewById(R.id.layout_head_params);
            layoutSpeechParams = itemView.findViewById(R.id.layout_speech_params);
            layoutDelayParams = itemView.findViewById(R.id.layout_delay_params);
            layoutCustomParams = itemView.findViewById(R.id.layout_custom_params);
            
            // Parameter controls
            spinnerMovementDirection = itemView.findViewById(R.id.spinner_movement_direction);
            spinnerMovementSpeed = itemView.findViewById(R.id.spinner_movement_speed);
            editMovementDuration = itemView.findViewById(R.id.edit_movement_duration);

            spinnerGestureType = itemView.findViewById(R.id.spinner_gesture_type);
            spinnerGestureIntensity = itemView.findViewById(R.id.spinner_gesture_intensity);

            spinnerHandSide = itemView.findViewById(R.id.spinner_hand_side);
            spinnerHandPosition = itemView.findViewById(R.id.spinner_hand_position);
            editHandAngle = itemView.findViewById(R.id.edit_hand_angle);

            spinnerHeadAction = itemView.findViewById(R.id.spinner_head_action);
            spinnerHeadDirection = itemView.findViewById(R.id.spinner_head_direction);
            editHeadAngle = itemView.findViewById(R.id.edit_head_angle);

            editSpeechText = itemView.findViewById(R.id.edit_speech_text);
            spinnerVoiceGender = itemView.findViewById(R.id.spinner_voice_gender);
            editVoiceSpeed = itemView.findViewById(R.id.edit_voice_speed);
            editVoicePitch = itemView.findViewById(R.id.edit_voice_pitch);

            editDelayDuration = itemView.findViewById(R.id.edit_delay_duration);
            editCustomCommand = itemView.findViewById(R.id.edit_custom_command);

            // Legacy controls (if needed)
            // spinnerServoType = itemView.findViewById(R.id.spinner_servo_type);
            // editServoAngle = itemView.findViewById(R.id.edit_servo_angle);
            
            setupSpinners();
            setupListeners();
        }
        
        private void setupSpinners() {
            // Action type spinner
            List<String> actionTypes = new ArrayList<>();
            for (PresetAction.ActionType type : PresetAction.ActionType.values()) {
                actionTypes.add(type.getDisplayName());
            }
            ArrayAdapter<String> actionTypeAdapter = new ArrayAdapter<>(itemView.getContext(),
                    android.R.layout.simple_spinner_item, actionTypes);
            actionTypeAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
            spinnerActionType.setAdapter(actionTypeAdapter);
            
            // Movement direction spinner - comprehensive mecanum wheel movements
            List<String> movements = Arrays.asList(
                "Forward", "Backward", "Left", "Right",
                "Slide Left", "Slide Right", "Diagonal Forward Left", "Diagonal Forward Right",
                "Diagonal Backward Left", "Diagonal Backward Right", "Rotate Left", "Rotate Right",
                "Full Turn Left", "Full Turn Right", "Stop"
            );
            ArrayAdapter<String> movementAdapter = new ArrayAdapter<>(itemView.getContext(),
                    android.R.layout.simple_spinner_item, movements);
            movementAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
            spinnerMovementDirection.setAdapter(movementAdapter);

            // Movement speed spinner
            List<String> speeds = Arrays.asList("Slow", "Normal", "Fast", "Custom");
            ArrayAdapter<String> speedAdapter = new ArrayAdapter<>(itemView.getContext(),
                    android.R.layout.simple_spinner_item, speeds);
            speedAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
            spinnerMovementSpeed.setAdapter(speedAdapter);

            // Gesture type spinner
            List<String> gestures = Arrays.asList("Wave", "Handshake", "Point", "Thumbs Up", "Peace Sign", "Salute", "Clap");
            ArrayAdapter<String> gestureAdapter = new ArrayAdapter<>(itemView.getContext(),
                    android.R.layout.simple_spinner_item, gestures);
            gestureAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
            spinnerGestureType.setAdapter(gestureAdapter);

            // Gesture intensity spinner
            List<String> intensities = Arrays.asList("Subtle", "Normal", "Dramatic");
            ArrayAdapter<String> intensityAdapter = new ArrayAdapter<>(itemView.getContext(),
                    android.R.layout.simple_spinner_item, intensities);
            intensityAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
            spinnerGestureIntensity.setAdapter(intensityAdapter);

            // Hand side spinner
            List<String> handSides = Arrays.asList("Left Hand", "Right Hand", "Both Hands");
            ArrayAdapter<String> handSideAdapter = new ArrayAdapter<>(itemView.getContext(),
                    android.R.layout.simple_spinner_item, handSides);
            handSideAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
            spinnerHandSide.setAdapter(handSideAdapter);

            // Hand position spinner
            List<String> handPositions = Arrays.asList("Rest", "Up", "Forward", "Side", "Down", "Custom Angle");
            ArrayAdapter<String> handPositionAdapter = new ArrayAdapter<>(itemView.getContext(),
                    android.R.layout.simple_spinner_item, handPositions);
            handPositionAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
            spinnerHandPosition.setAdapter(handPositionAdapter);

            // Head action spinner
            List<String> headActions = Arrays.asList("Turn", "Tilt", "Look", "Center", "Nod", "Shake");
            ArrayAdapter<String> headActionAdapter = new ArrayAdapter<>(itemView.getContext(),
                    android.R.layout.simple_spinner_item, headActions);
            headActionAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
            spinnerHeadAction.setAdapter(headActionAdapter);

            // Head direction spinner
            List<String> headDirections = Arrays.asList("Left", "Right", "Up", "Down", "Center", "Custom Angle");
            ArrayAdapter<String> headDirectionAdapter = new ArrayAdapter<>(itemView.getContext(),
                    android.R.layout.simple_spinner_item, headDirections);
            headDirectionAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
            spinnerHeadDirection.setAdapter(headDirectionAdapter);

            // Voice gender spinner
            List<String> voiceGenders = Arrays.asList("Female", "Male", "Auto");
            ArrayAdapter<String> voiceGenderAdapter = new ArrayAdapter<>(itemView.getContext(),
                    android.R.layout.simple_spinner_item, voiceGenders);
            voiceGenderAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
            spinnerVoiceGender.setAdapter(voiceGenderAdapter);

            // Legacy servo support removed - use Hand Control and Head Movement instead
        }
        
        private void setupListeners() {
            // Action type change listener
            spinnerActionType.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                    PresetAction.ActionType[] types = PresetAction.ActionType.values();
                    if (position >= 0 && position < types.length) {
                        updateActionType(types[position]);
                        updateAction();
                    }
                }
                
                @Override
                public void onNothingSelected(AdapterView<?> parent) {}
            });
            
            // Delete button
            btnDeleteAction.setOnClickListener(v -> {
                int position = getAdapterPosition();
                if (position != RecyclerView.NO_POSITION && listener != null) {
                    listener.onActionDeleted(position);
                }
            });
            
            // Description change listener
            editActionDescription.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence s, int start, int count, int after) {}
                
                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {}
                
                @Override
                public void afterTextChanged(Editable s) {
                    updateAction();
                }
            });
            
            // Parameter change listeners
            setupParameterListeners();
        }
        
        private void setupParameterListeners() {
            // Movement parameters
            spinnerMovementDirection.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                    updateAction();
                }

                @Override
                public void onNothingSelected(AdapterView<?> parent) {}
            });

            spinnerMovementSpeed.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                    updateAction();
                }

                @Override
                public void onNothingSelected(AdapterView<?> parent) {}
            });

            editMovementDuration.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {}

                @Override
                public void afterTextChanged(Editable s) {
                    updateAction();
                }
            });

            // Hand control parameters
            spinnerHandSide.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                    updateAction();
                }

                @Override
                public void onNothingSelected(AdapterView<?> parent) {}
            });

            spinnerHandPosition.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                    updateAction();
                }

                @Override
                public void onNothingSelected(AdapterView<?> parent) {}
            });

            editHandAngle.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {}

                @Override
                public void afterTextChanged(Editable s) {
                    updateAction();
                }
            });

            // Head movement parameters
            spinnerHeadAction.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                    updateAction();
                }

                @Override
                public void onNothingSelected(AdapterView<?> parent) {}
            });

            spinnerHeadDirection.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                    updateAction();
                }

                @Override
                public void onNothingSelected(AdapterView<?> parent) {}
            });

            editHeadAngle.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {}

                @Override
                public void afterTextChanged(Editable s) {
                    updateAction();
                }
            });

            // Gesture parameters
            spinnerGestureType.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                    updateAction();
                }

                @Override
                public void onNothingSelected(AdapterView<?> parent) {}
            });

            spinnerGestureIntensity.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                    updateAction();
                }

                @Override
                public void onNothingSelected(AdapterView<?> parent) {}
            });
            
            // Speech parameters
            editSpeechText.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {}

                @Override
                public void afterTextChanged(Editable s) {
                    updateAction();
                }
            });

            spinnerVoiceGender.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                    updateAction();
                }

                @Override
                public void onNothingSelected(AdapterView<?> parent) {}
            });

            editVoiceSpeed.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {}

                @Override
                public void afterTextChanged(Editable s) {
                    updateAction();
                }
            });

            editVoicePitch.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {}

                @Override
                public void afterTextChanged(Editable s) {
                    updateAction();
                }
            });
            
            // Delay duration
            editDelayDuration.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence s, int start, int count, int after) {}
                
                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {}
                
                @Override
                public void afterTextChanged(Editable s) {
                    updateAction();
                }
            });
            
            // Custom command
            editCustomCommand.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence s, int start, int count, int after) {}
                
                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {}
                
                @Override
                public void afterTextChanged(Editable s) {
                    updateAction();
                }
            });
        }

        public void bind(PresetAction action, int position) {
            // Set action type
            PresetAction.ActionType[] types = PresetAction.ActionType.values();
            for (int i = 0; i < types.length; i++) {
                if (types[i] == action.getType()) {
                    spinnerActionType.setSelection(i);
                    break;
                }
            }

            // Set description
            editActionDescription.setText(action.getDescription());

            // Update UI for action type
            updateActionType(action.getType());

            // Load parameters
            loadParameters(action);
        }

        private void updateActionType(PresetAction.ActionType type) {
            // Hide all parameter layouts
            layoutMovementParams.setVisibility(View.GONE);
            layoutGestureParams.setVisibility(View.GONE);
            layoutHandParams.setVisibility(View.GONE);
            layoutHeadParams.setVisibility(View.GONE);
            layoutSpeechParams.setVisibility(View.GONE);
            layoutDelayParams.setVisibility(View.GONE);
            layoutCustomParams.setVisibility(View.GONE);

            // Update icon and show relevant parameters
            switch (type) {
                case MOVEMENT:
                    textActionIcon.setText("M");
                    layoutMovementParams.setVisibility(View.VISIBLE);
                    break;
                case HAND_CONTROL:
                    textActionIcon.setText("H");
                    layoutHandParams.setVisibility(View.VISIBLE);
                    break;
                case HEAD_MOVEMENT:
                    textActionIcon.setText("HD");
                    layoutHeadParams.setVisibility(View.VISIBLE);
                    break;
                case GESTURE:
                    textActionIcon.setText("G");
                    layoutGestureParams.setVisibility(View.VISIBLE);
                    break;
                case SPEECH:
                    textActionIcon.setText("S");
                    layoutSpeechParams.setVisibility(View.VISIBLE);
                    break;
                case DELAY:
                    textActionIcon.setText("D");
                    layoutDelayParams.setVisibility(View.VISIBLE);
                    break;
                case STOP:
                    textActionIcon.setText("X");
                    // No parameters needed for stop
                    break;
                case CUSTOM_COMMAND:
                    textActionIcon.setText("C");
                    layoutCustomParams.setVisibility(View.VISIBLE);
                    break;
            }
        }

        private void loadParameters(PresetAction action) {
            Map<String, String> params = action.getParameters();
            if (params == null || params.isEmpty()) {
                return;
            }

            switch (action.getType()) {
                case MOVEMENT:
                    String direction = params.get("direction");
                    if (direction != null) {
                        String[] movements = {
                            "Forward", "Backward", "Left", "Right",
                            "Slide Left", "Slide Right", "Diagonal Forward Left", "Diagonal Forward Right",
                            "Diagonal Backward Left", "Diagonal Backward Right", "Rotate Left", "Rotate Right",
                            "Full Turn Left", "Full Turn Right", "Stop"
                        };
                        // Convert parameter value to display name
                        String displayName = convertParameterToMovementDisplay(direction);
                        for (int i = 0; i < movements.length; i++) {
                            if (movements[i].equals(displayName)) {
                                spinnerMovementDirection.setSelection(i);
                                break;
                            }
                        }
                    }
                    String speed = params.get("speed");
                    if (speed != null) {
                        String[] speeds = {"Slow", "Normal", "Fast", "Custom"};
                        for (int i = 0; i < speeds.length; i++) {
                            if (speeds[i].equals(speed)) {
                                spinnerMovementSpeed.setSelection(i);
                                break;
                            }
                        }
                    }
                    String duration = params.get("duration");
                    if (duration != null) {
                        editMovementDuration.setText(duration);
                    }
                    break;

                case HAND_CONTROL:
                    String handSide = params.get("hand_side");
                    if (handSide != null) {
                        String[] handSides = {"Left Hand", "Right Hand", "Both Hands"};
                        for (int i = 0; i < handSides.length; i++) {
                            if (handSides[i].equals(handSide)) {
                                spinnerHandSide.setSelection(i);
                                break;
                            }
                        }
                    }
                    String handPosition = params.get("hand_position");
                    if (handPosition != null) {
                        String[] handPositions = {"Rest", "Up", "Forward", "Side", "Down", "Custom Angle"};
                        for (int i = 0; i < handPositions.length; i++) {
                            if (handPositions[i].equals(handPosition)) {
                                spinnerHandPosition.setSelection(i);
                                break;
                            }
                        }
                    }
                    String handAngle = params.get("angle");
                    if (handAngle != null) {
                        editHandAngle.setText(handAngle);
                    }
                    break;

                case HEAD_MOVEMENT:
                    String headAction = params.get("head_action");
                    if (headAction != null) {
                        String[] headActions = {"Turn", "Tilt", "Look", "Center", "Nod", "Shake"};
                        for (int i = 0; i < headActions.length; i++) {
                            if (headActions[i].equals(headAction)) {
                                spinnerHeadAction.setSelection(i);
                                break;
                            }
                        }
                    }
                    String headDirection = params.get("head_direction");
                    if (headDirection != null) {
                        String[] headDirections = {"Left", "Right", "Up", "Down", "Center", "Custom Angle"};
                        for (int i = 0; i < headDirections.length; i++) {
                            if (headDirections[i].equals(headDirection)) {
                                spinnerHeadDirection.setSelection(i);
                                break;
                            }
                        }
                    }
                    String headAngle = params.get("head_angle");
                    if (headAngle != null) {
                        editHeadAngle.setText(headAngle);
                    }
                    break;

                case GESTURE:
                    String gesture = params.get("gesture");
                    if (gesture != null) {
                        String[] gestures = {"Wave", "Handshake", "Point", "Thumbs Up", "Peace Sign", "Salute", "Clap"};
                        for (int i = 0; i < gestures.length; i++) {
                            if (gestures[i].equals(gesture)) {
                                spinnerGestureType.setSelection(i);
                                break;
                            }
                        }
                    }
                    String gestureIntensity = params.get("gesture_intensity");
                    if (gestureIntensity != null) {
                        String[] intensities = {"Subtle", "Normal", "Dramatic"};
                        for (int i = 0; i < intensities.length; i++) {
                            if (intensities[i].equals(gestureIntensity)) {
                                spinnerGestureIntensity.setSelection(i);
                                break;
                            }
                        }
                    }
                    break;

                case SPEECH:
                    String text = params.get("text");
                    if (text != null) {
                        editSpeechText.setText(text);
                    }
                    String voiceGender = params.get("voice_gender");
                    if (voiceGender != null) {
                        String[] genders = {"Female", "Male", "Auto"};
                        for (int i = 0; i < genders.length; i++) {
                            if (genders[i].equals(voiceGender)) {
                                spinnerVoiceGender.setSelection(i);
                                break;
                            }
                        }
                    }
                    String voiceSpeed = params.get("voice_speed");
                    if (voiceSpeed != null) {
                        editVoiceSpeed.setText(voiceSpeed);
                    }
                    String voicePitch = params.get("voice_pitch");
                    if (voicePitch != null) {
                        editVoicePitch.setText(voicePitch);
                    }
                    break;

                case DELAY:
                    String delayDuration = params.get("duration");
                    if (delayDuration != null) {
                        editDelayDuration.setText(delayDuration);
                    }
                    break;

                case CUSTOM_COMMAND:
                    String command = params.get("command");
                    if (command != null) {
                        editCustomCommand.setText(command);
                    }
                    break;
            }
        }

        private void updateAction() {
            int position = getAdapterPosition();
            if (position == RecyclerView.NO_POSITION || listener == null) {
                return;
            }

            PresetAction action = actions.get(position);

            // Update type
            int typeIndex = spinnerActionType.getSelectedItemPosition();
            PresetAction.ActionType[] types = PresetAction.ActionType.values();
            if (typeIndex >= 0 && typeIndex < types.length) {
                action.setType(types[typeIndex]);
            }

            // Update description
            action.setDescription(editActionDescription.getText().toString().trim());

            // Update parameters based on type
            Map<String, String> parameters = new HashMap<>();
            switch (action.getType()) {
                case MOVEMENT:
                    String[] movements = {
                        "Forward", "Backward", "Left", "Right",
                        "Slide Left", "Slide Right", "Diagonal Forward Left", "Diagonal Forward Right",
                        "Diagonal Backward Left", "Diagonal Backward Right", "Rotate Left", "Rotate Right",
                        "Full Turn Left", "Full Turn Right", "Stop"
                    };
                    int movementIndex = spinnerMovementDirection.getSelectedItemPosition();
                    if (movementIndex >= 0 && movementIndex < movements.length) {
                        // Convert display name to ESP32 command parameter
                        String direction = convertMovementDisplayToParameter(movements[movementIndex]);
                        parameters.put("direction", direction);
                    }
                    String[] speeds = {"Slow", "Normal", "Fast", "Custom"};
                    int speedIndex = spinnerMovementSpeed.getSelectedItemPosition();
                    if (speedIndex >= 0 && speedIndex < speeds.length) {
                        parameters.put("speed", speeds[speedIndex]);
                    }
                    parameters.put("duration", editMovementDuration.getText().toString().trim());
                    break;

                case HAND_CONTROL:
                    String[] handSides = {"Left Hand", "Right Hand", "Both Hands"};
                    int handSideIndex = spinnerHandSide.getSelectedItemPosition();
                    if (handSideIndex >= 0 && handSideIndex < handSides.length) {
                        parameters.put("hand_side", handSides[handSideIndex]);
                    }
                    String[] handPositions = {"Rest", "Up", "Forward", "Side", "Down", "Custom Angle"};
                    int handPositionIndex = spinnerHandPosition.getSelectedItemPosition();
                    if (handPositionIndex >= 0 && handPositionIndex < handPositions.length) {
                        parameters.put("hand_position", handPositions[handPositionIndex]);
                    }
                    parameters.put("angle", editHandAngle.getText().toString().trim());
                    break;

                case HEAD_MOVEMENT:
                    String[] headActions = {"Turn", "Tilt", "Look", "Center", "Nod", "Shake"};
                    int headActionIndex = spinnerHeadAction.getSelectedItemPosition();
                    if (headActionIndex >= 0 && headActionIndex < headActions.length) {
                        parameters.put("head_action", headActions[headActionIndex]);
                    }
                    String[] headDirections = {"Left", "Right", "Up", "Down", "Center", "Custom Angle"};
                    int headDirectionIndex = spinnerHeadDirection.getSelectedItemPosition();
                    if (headDirectionIndex >= 0 && headDirectionIndex < headDirections.length) {
                        parameters.put("head_direction", headDirections[headDirectionIndex]);
                    }
                    parameters.put("head_angle", editHeadAngle.getText().toString().trim());
                    break;

                case GESTURE:
                    String[] gestures = {"Wave", "Handshake", "Point", "Thumbs Up", "Peace Sign", "Salute", "Clap"};
                    int gestureIndex = spinnerGestureType.getSelectedItemPosition();
                    if (gestureIndex >= 0 && gestureIndex < gestures.length) {
                        parameters.put("gesture", gestures[gestureIndex]);
                    }
                    String[] intensities = {"Subtle", "Normal", "Dramatic"};
                    int intensityIndex = spinnerGestureIntensity.getSelectedItemPosition();
                    if (intensityIndex >= 0 && intensityIndex < intensities.length) {
                        parameters.put("gesture_intensity", intensities[intensityIndex]);
                    }
                    break;

                case SPEECH:
                    parameters.put("text", editSpeechText.getText().toString().trim());
                    String[] genders = {"Female", "Male", "Auto"};
                    int genderIndex = spinnerVoiceGender.getSelectedItemPosition();
                    if (genderIndex >= 0 && genderIndex < genders.length) {
                        parameters.put("voice_gender", genders[genderIndex]);
                    }
                    parameters.put("voice_speed", editVoiceSpeed.getText().toString().trim());
                    parameters.put("voice_pitch", editVoicePitch.getText().toString().trim());
                    break;

                case DELAY:
                    parameters.put("duration", editDelayDuration.getText().toString().trim());
                    break;

                case CUSTOM_COMMAND:
                    parameters.put("command", editCustomCommand.getText().toString().trim());
                    break;
            }

            action.setParameters(parameters);
            listener.onActionUpdated(position, action);
        }

        /**
         * Convert movement display names to ESP32 command parameters
         */
        private String convertMovementDisplayToParameter(String displayName) {
            switch (displayName) {
                case "Forward": return "forward";
                case "Backward": return "backward";
                case "Left": return "turn_left";
                case "Right": return "turn_right";
                case "Slide Left": return "slide_left";
                case "Slide Right": return "slide_right";
                case "Diagonal Forward Left": return "diagonal_front_left";
                case "Diagonal Forward Right": return "diagonal_front_right";
                case "Diagonal Backward Left": return "diagonal_back_left";
                case "Diagonal Backward Right": return "diagonal_back_right";
                case "Rotate Left": return "rotate_left";
                case "Rotate Right": return "rotate_right";
                case "Full Turn Left": return "rotate_left"; // Map to same as rotate left
                case "Full Turn Right": return "rotate_right"; // Map to same as rotate right
                case "Stop": return "stop";
                default: return "stop"; // Default to stop for safety
            }
        }

        /**
         * Convert ESP32 command parameters back to movement display names
         */
        private String convertParameterToMovementDisplay(String parameter) {
            switch (parameter.toLowerCase()) {
                case "forward": return "Forward";
                case "backward": return "Backward";
                case "turn_left": return "Left";
                case "turn_right": return "Right";
                case "slide_left": return "Slide Left";
                case "slide_right": return "Slide Right";
                case "diagonal_front_left": return "Diagonal Forward Left";
                case "diagonal_front_right": return "Diagonal Forward Right";
                case "diagonal_back_left": return "Diagonal Backward Left";
                case "diagonal_back_right": return "Diagonal Backward Right";
                case "rotate_left": return "Rotate Left";
                case "rotate_right": return "Rotate Right";
                case "stop": return "Stop";
                default: return "Stop"; // Default to stop for safety
            }
        }
    }
}
