@echo off
echo ========================================
echo STEM Robot - Setup Verification
echo ========================================

echo.
echo [1/5] Checking Java version...
java -version 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Java not found in PATH
    echo Please install Java 17 or 21
    goto :error
) else (
    echo ✅ Java found
)

echo.
echo [2/5] Checking Android SDK...
if not defined ANDROID_HOME (
    echo ❌ ANDROID_HOME not set
    echo Please set ANDROID_HOME environment variable
    goto :error
) else (
    echo ✅ ANDROID_HOME: %ANDROID_HOME%
)

echo.
echo [3/5] Checking Gradle wrapper...
if exist "gradlew.bat" (
    echo ✅ Gradle wrapper found
) else (
    echo ❌ Gradle wrapper missing
    goto :error
)

echo.
echo [4/5] Testing Gradle version...
call gradlew --version
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Gradle version check failed
    goto :error
) else (
    echo ✅ Gradle working
)

echo.
echo [5/5] Testing project sync...
call gradlew tasks --quiet
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Project sync failed
    echo Check Java/Gradle compatibility
    goto :error
) else (
    echo ✅ Project sync successful
)

echo.
echo ========================================
echo ✅ ALL CHECKS PASSED!
echo ========================================
echo.
echo Your setup is ready for development.
echo Next steps:
echo 1. Open project in Android Studio
echo 2. Sync project with Gradle files
echo 3. Build and run the app
echo.
goto :end

:error
echo.
echo ========================================
echo ❌ SETUP ISSUES FOUND
echo ========================================
echo.
echo Please fix the issues above and try again.
echo See docs/JAVA_GRADLE_COMPATIBILITY.md for help.
echo.

:end
pause
