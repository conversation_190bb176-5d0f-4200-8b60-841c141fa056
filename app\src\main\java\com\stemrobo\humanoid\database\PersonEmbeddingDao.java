package com.stemrobo.humanoid.database;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import java.util.List;

/**
 * Data Access Object for PersonEmbedding operations
 */
@Dao
public interface PersonEmbeddingDao {
    
    @Query("SELECT * FROM person_embeddings")
    List<PersonEmbedding> getAllEmbeddings();
    
    @Query("SELECT * FROM person_embeddings WHERE person_id = :personId")
    List<PersonEmbedding> getEmbeddingsForPerson(int personId);
    
    @Query("SELECT * FROM person_embeddings WHERE person_id = :personId AND is_primary = 1 LIMIT 1")
    PersonEmbedding getPrimaryEmbeddingForPerson(int personId);
    
    @Query("SELECT * FROM person_embeddings WHERE person_id = :personId ORDER BY quality_score DESC")
    List<PersonEmbedding> getEmbeddingsForPersonOrderedByQuality(int personId);
    
    @Insert
    long insertEmbedding(PersonEmbedding embedding);
    
    @Insert
    void insertEmbeddings(List<PersonEmbedding> embeddings);
    
    @Update
    void updateEmbedding(PersonEmbedding embedding);
    
    @Delete
    void deleteEmbedding(PersonEmbedding embedding);
    
    @Query("DELETE FROM person_embeddings WHERE person_id = :personId")
    void deleteEmbeddingsForPerson(int personId);
    
    @Query("DELETE FROM person_embeddings")
    void deleteAllEmbeddings();
    
    @Query("UPDATE person_embeddings SET is_primary = 0 WHERE person_id = :personId")
    void clearPrimaryFlagForPerson(int personId);
    
    @Query("UPDATE person_embeddings SET is_primary = 1 WHERE id = :embeddingId")
    void setPrimaryEmbedding(int embeddingId);
    
    @Query("SELECT COUNT(*) FROM person_embeddings WHERE person_id = :personId")
    int getEmbeddingCountForPerson(int personId);
}