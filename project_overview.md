# Project Architecture Overview

This document provides a high-level overview of the project's architecture.

## System Components

The system is composed of three main components:

1.  **Android App:** The primary user interface and control center for the robot.
2.  **ESP32 Firmware:** The software running on the robot's microcontroller, responsible for hardware control.
3.  **YOLO Object Detection:** A machine learning model used for real-time object detection.

## Architecture Diagram

```mermaid
graph TD
    subgraph Android App
        A[UI] --> B{AI};
        A --> C{Vision};
        A --> D{Communication};
        B --> D;
        C --> B;
        C --> D;
    end

    subgraph ESP32
        E[Firmware]
    end

    subgraph YOLO
        F[Object Detection]
    end

    D -- USB/Bluetooth --> E;
    C -- Camera Feed --> F;