package com.stemrobo.humanoid.adapters;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.stemrobo.humanoid.R;
import com.stemrobo.humanoid.models.Preset;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * RecyclerView adapter for displaying preset action sequences.
 * Handles preset display, execution progress, and user interactions.
 */
public class PresetAdapter extends RecyclerView.Adapter<PresetAdapter.PresetViewHolder> {
    
    private List<Preset> presets;
    private OnPresetActionListener listener;
    private Map<Long, Integer> executionProgress; // preset_id -> progress_percent
    private Map<Long, Boolean> expandedStates; // preset_id -> expanded
    
    public interface OnPresetActionListener {
        void onPresetPlay(Preset preset);
        void onPresetEdit(Preset preset);
        void onPresetDuplicate(Preset preset);
        void onPresetDelete(Preset preset);
        void onPresetToggleDetails(Preset preset, boolean expanded);
    }
    
    public PresetAdapter(List<Preset> presets, OnPresetActionListener listener) {
        this.presets = presets;
        this.listener = listener;
        this.executionProgress = new HashMap<>();
        this.expandedStates = new HashMap<>();
    }
    
    @NonNull
    @Override
    public PresetViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_preset, parent, false);
        return new PresetViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull PresetViewHolder holder, int position) {
        Preset preset = presets.get(position);
        holder.bind(preset);
    }
    
    @Override
    public int getItemCount() {
        return presets.size();
    }
    
    public void updateExecutionProgress(long presetId, int progressPercent) {
        executionProgress.put(presetId, progressPercent);
        notifyDataSetChanged(); // In a real app, you'd use more efficient updates
    }
    
    public void clearExecutionProgress(long presetId) {
        executionProgress.remove(presetId);
        notifyDataSetChanged();
    }
    
    class PresetViewHolder extends RecyclerView.ViewHolder {
        
        // Main content views
        private TextView textPresetName, textPresetCategory, textPresetDescription;
        private TextView textPresetDuration, textPresetSteps, textPresetModified;
        private Button btnPlayPreset, btnPresetMenu;
        
        // Expandable details views
        private LinearLayout layoutPresetDetails;
        private RecyclerView recyclerPresetSteps;
        private Button btnEditPreset, btnDuplicatePreset, btnDeletePreset;
        
        // Execution progress views
        private ProgressBar progressPresetExecution;
        private TextView textExecutionStatus;
        
        public PresetViewHolder(@NonNull View itemView) {
            super(itemView);
            
            // Initialize main content views
            textPresetName = itemView.findViewById(R.id.text_preset_name);
            textPresetCategory = itemView.findViewById(R.id.text_preset_category);
            textPresetDescription = itemView.findViewById(R.id.text_preset_description);
            textPresetDuration = itemView.findViewById(R.id.text_preset_duration);
            textPresetSteps = itemView.findViewById(R.id.text_preset_steps);
            textPresetModified = itemView.findViewById(R.id.text_preset_modified);
            btnPlayPreset = itemView.findViewById(R.id.btn_play_preset);
            btnPresetMenu = itemView.findViewById(R.id.btn_preset_menu);
            
            // Initialize expandable details views
            layoutPresetDetails = itemView.findViewById(R.id.layout_preset_details);
            recyclerPresetSteps = itemView.findViewById(R.id.recycler_preset_steps);
            btnEditPreset = itemView.findViewById(R.id.btn_edit_preset);
            btnDuplicatePreset = itemView.findViewById(R.id.btn_duplicate_preset);
            btnDeletePreset = itemView.findViewById(R.id.btn_delete_preset);
            
            // Initialize execution progress views
            progressPresetExecution = itemView.findViewById(R.id.progress_preset_execution);
            textExecutionStatus = itemView.findViewById(R.id.text_execution_status);
        }
        
        public void bind(Preset preset) {
            // Set basic preset information
            textPresetName.setText(preset.getName());
            textPresetCategory.setText(preset.getCategory().name());
            textPresetDescription.setText(preset.getDescription());
            
            // Set preset statistics
            textPresetDuration.setText("⏱️ " + preset.getFormattedDuration());
            textPresetSteps.setText("📋 " + preset.getStepCount() + " steps");
            textPresetModified.setText("📅 " + formatRelativeTime(preset.getModifiedAt()));
            
            // Set category background color
            setCategoryBackground(preset.getCategory());
            
            // Handle execution progress
            Integer progress = executionProgress.get(preset.getId());
            boolean isExecuting = progress != null;
            
            progressPresetExecution.setVisibility(isExecuting ? View.VISIBLE : View.GONE);
            textExecutionStatus.setVisibility(isExecuting ? View.VISIBLE : View.GONE);
            
            if (isExecuting) {
                progressPresetExecution.setProgress(progress);
                textExecutionStatus.setText("▶️ Executing... " + progress + "%");
            }
            
            // Handle expandable details
            boolean isExpanded = expandedStates.getOrDefault(preset.getId(), false);
            layoutPresetDetails.setVisibility(isExpanded ? View.VISIBLE : View.GONE);
            
            // Set up click listeners
            setupClickListeners(preset);
        }
        
        private void setCategoryBackground(Preset.Category category) {
            int colorRes;
            switch (category) {
                case MOVEMENT:
                    colorRes = R.color.category_movement;
                    break;
                case GESTURE:
                    colorRes = R.color.category_gesture;
                    break;
                case DANCE:
                    colorRes = R.color.category_dance;
                    break;
                case SECURITY:
                    colorRes = R.color.category_security;
                    break;
                case PRESENTATION:
                    colorRes = R.color.category_presentation;
                    break;
                default:
                    colorRes = R.color.robot_accent;
                    break;
            }
            
            textPresetCategory.setBackgroundColor(itemView.getContext().getColor(colorRes));
        }
        
        private void setupClickListeners(Preset preset) {
            // Play button
            btnPlayPreset.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onPresetPlay(preset);
                }
            });
            
            // Menu button - toggle details
            btnPresetMenu.setOnClickListener(v -> toggleDetails(preset));
            
            // Item click - toggle details
            itemView.setOnClickListener(v -> toggleDetails(preset));
            
            // Detail action buttons
            btnEditPreset.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onPresetEdit(preset);
                }
            });
            
            btnDuplicatePreset.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onPresetDuplicate(preset);
                }
            });
            
            btnDeletePreset.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onPresetDelete(preset);
                }
            });
        }
        
        private void toggleDetails(Preset preset) {
            boolean currentlyExpanded = expandedStates.getOrDefault(preset.getId(), false);
            boolean newExpandedState = !currentlyExpanded;
            
            expandedStates.put(preset.getId(), newExpandedState);
            layoutPresetDetails.setVisibility(newExpandedState ? View.VISIBLE : View.GONE);
            
            // Update menu button icon
            btnPresetMenu.setText(newExpandedState ? "▲" : "⋮");
            
            if (listener != null) {
                listener.onPresetToggleDetails(preset, newExpandedState);
            }
        }
        
        private String formatRelativeTime(long timestamp) {
            long now = System.currentTimeMillis();
            long diff = now - timestamp;
            
            long seconds = diff / 1000;
            long minutes = seconds / 60;
            long hours = minutes / 60;
            long days = hours / 24;
            
            if (days > 0) {
                return days + " day" + (days > 1 ? "s" : "") + " ago";
            } else if (hours > 0) {
                return hours + " hour" + (hours > 1 ? "s" : "") + " ago";
            } else if (minutes > 0) {
                return minutes + " min" + (minutes > 1 ? "s" : "") + " ago";
            } else {
                return "Just now";
            }
        }
    }
}
