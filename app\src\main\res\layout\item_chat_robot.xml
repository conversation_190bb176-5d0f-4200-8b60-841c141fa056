<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="4dp"
    android:gravity="start">

    <!-- Robot Avatar -->
    <ImageView
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_marginEnd="8dp"
        android:layout_gravity="top"
        android:src="@drawable/ic_robot"
        android:background="@drawable/robot_avatar_background"
        android:padding="6dp"
        android:contentDescription="Robot" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="48dp"
        android:orientation="vertical"
        android:background="@drawable/robot_message_background"
        android:padding="12dp">

        <TextView
            android:id="@+id/message_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Robot response"
            android:textColor="@color/text_primary"
            android:textSize="14sp"
            android:lineSpacingExtra="2dp" />

        <TextView
            android:id="@+id/time_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="12:34"
            android:textColor="@color/text_secondary"
            android:textSize="10sp"
            android:alpha="0.7" />

    </LinearLayout>

</LinearLayout>
