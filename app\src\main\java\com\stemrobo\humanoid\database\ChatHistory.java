package com.stemrobo.humanoid.database;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.ForeignKey;
import androidx.room.Ignore;
import androidx.room.Index;
import androidx.room.PrimaryKey;

/**
 * Entity for storing individual chat messages with conversation context
 * Links to ChatConversation for conversation memory management
 */
@Entity(tableName = "chat_history",
        foreignKeys = @ForeignKey(entity = ChatConversation.class,
                                 parentColumns = "conversation_id",
                                 childColumns = "conversation_id",
                                 onDelete = ForeignKey.CASCADE),
        indices = {@Index(value = {"conversation_id"}), @Index(value = {"timestamp"})})
public class ChatHistory {
    
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "id")
    public long id;
    
    @NonNull
    @ColumnInfo(name = "conversation_id")
    public String conversationId;
    
    @ColumnInfo(name = "message_text")
    public String messageText;
    
    @ColumnInfo(name = "message_type")
    public int messageType; // 0 = USER, 1 = AI, 2 = SYSTEM
    
    @ColumnInfo(name = "timestamp")
    public long timestamp;
    
    @ColumnInfo(name = "sequence_number")
    public int sequenceNumber; // Order within conversation
    
    @ColumnInfo(name = "language_code")
    public String languageCode;
    
    @ColumnInfo(name = "response_time_ms")
    public long responseTimeMs; // For AI messages, time taken to generate response
    
    @ColumnInfo(name = "context_used")
    public String contextUsed; // Summary of context that was used for this AI response
    
    // Message type constants
    public static final int TYPE_USER = 0;
    public static final int TYPE_AI = 1;
    public static final int TYPE_SYSTEM = 2;
    
    // Default constructor
    public ChatHistory() {
        this.timestamp = System.currentTimeMillis();
        this.responseTimeMs = 0;
    }
    
    // Constructor for user/AI messages
    @Ignore
    public ChatHistory(@NonNull String conversationId, String messageText, int messageType, int sequenceNumber) {
        this();
        this.conversationId = conversationId;
        this.messageText = messageText;
        this.messageType = messageType;
        this.sequenceNumber = sequenceNumber;
    }

    // Constructor with language code
    @Ignore
    public ChatHistory(@NonNull String conversationId, String messageText, int messageType, int sequenceNumber, String languageCode) {
        this(conversationId, messageText, messageType, sequenceNumber);
        this.languageCode = languageCode;
    }
    
    // Getters and setters
    public long getId() {
        return id;
    }
    
    public void setId(long id) {
        this.id = id;
    }
    
    public String getConversationId() {
        return conversationId;
    }
    
    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }
    
    public String getMessageText() {
        return messageText;
    }
    
    public void setMessageText(String messageText) {
        this.messageText = messageText;
    }
    
    public int getMessageType() {
        return messageType;
    }
    
    public void setMessageType(int messageType) {
        this.messageType = messageType;
    }
    
    public long getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }
    
    public int getSequenceNumber() {
        return sequenceNumber;
    }
    
    public void setSequenceNumber(int sequenceNumber) {
        this.sequenceNumber = sequenceNumber;
    }
    
    public String getLanguageCode() {
        return languageCode;
    }
    
    public void setLanguageCode(String languageCode) {
        this.languageCode = languageCode;
    }
    
    public long getResponseTimeMs() {
        return responseTimeMs;
    }
    
    public void setResponseTimeMs(long responseTimeMs) {
        this.responseTimeMs = responseTimeMs;
    }
    
    public String getContextUsed() {
        return contextUsed;
    }
    
    public void setContextUsed(String contextUsed) {
        this.contextUsed = contextUsed;
    }
    
    /**
     * Check if this is a user message
     */
    public boolean isUserMessage() {
        return messageType == TYPE_USER;
    }
    
    /**
     * Check if this is an AI message
     */
    public boolean isAIMessage() {
        return messageType == TYPE_AI;
    }
    
    /**
     * Check if this is a system message
     */
    public boolean isSystemMessage() {
        return messageType == TYPE_SYSTEM;
    }
}
