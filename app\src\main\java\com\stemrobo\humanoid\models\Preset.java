package com.stemrobo.humanoid.models;

import java.util.ArrayList;
import java.util.List;

/**
 * Represents a preset action sequence that can contain multiple simultaneous actions
 * with precise timing and coordination.
 */
public class Preset {
    private long id;
    private String name;
    private String description;
    private long createdAt;
    private long modifiedAt;
    private boolean isActive;
    private int totalDurationMs;
    private List<PresetStep> steps;
    
    // Preset categories for organization
    public enum Category {
        MOVEMENT("Movement"),
        GESTURE("Gesture"),
        DANCE("Dance"),
        SECURITY("Security"),
        PRESENTATION("Presentation"),
        SOCIAL("Social"),
        ENTERTAINMENT("Entertainment"),
        DEMONSTRATION("Demonstration"),
        CUSTOM("Custom");
        
        private final String displayName;
        
        Category(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
    }
    
    private Category category;
    
    public Preset() {
        this.steps = new ArrayList<>();
        this.createdAt = System.currentTimeMillis();
        this.modifiedAt = System.currentTimeMillis();
        this.isActive = true;
        this.category = Category.CUSTOM;
    }
    
    public Preset(String name, String description) {
        this();
        this.name = name;
        this.description = description;
    }
    
    // Getters and Setters
    public long getId() { return id; }
    public void setId(long id) { this.id = id; }
    
    public String getName() { return name; }
    public void setName(String name) { 
        this.name = name;
        this.modifiedAt = System.currentTimeMillis();
    }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { 
        this.description = description;
        this.modifiedAt = System.currentTimeMillis();
    }
    
    public long getCreatedAt() { return createdAt; }
    public void setCreatedAt(long createdAt) { this.createdAt = createdAt; }
    
    public long getModifiedAt() { return modifiedAt; }
    public void setModifiedAt(long modifiedAt) { this.modifiedAt = modifiedAt; }

    public void setUpdatedAt(long updatedAt) { this.modifiedAt = updatedAt; }
    
    public boolean isActive() { return isActive; }
    public void setActive(boolean active) { 
        this.isActive = active;
        this.modifiedAt = System.currentTimeMillis();
    }
    
    public int getTotalDurationMs() { return totalDurationMs; }
    public void setTotalDurationMs(int totalDurationMs) { this.totalDurationMs = totalDurationMs; }
    
    public Category getCategory() { return category; }
    public void setCategory(Category category) { 
        this.category = category;
        this.modifiedAt = System.currentTimeMillis();
    }
    
    public List<PresetStep> getSteps() { return steps; }
    public void setSteps(List<PresetStep> steps) { 
        this.steps = steps;
        calculateTotalDuration();
        this.modifiedAt = System.currentTimeMillis();
    }
    
    // Utility methods
    public void addStep(PresetStep step) {
        this.steps.add(step);
        calculateTotalDuration();
        this.modifiedAt = System.currentTimeMillis();
    }
    
    public void removeStep(int index) {
        if (index >= 0 && index < steps.size()) {
            this.steps.remove(index);
            calculateTotalDuration();
            this.modifiedAt = System.currentTimeMillis();
        }
    }
    
    public void insertStep(int index, PresetStep step) {
        if (index >= 0 && index <= steps.size()) {
            this.steps.add(index, step);
            calculateTotalDuration();
            this.modifiedAt = System.currentTimeMillis();
        }
    }
    
    public void moveStep(int fromIndex, int toIndex) {
        if (fromIndex >= 0 && fromIndex < steps.size() && 
            toIndex >= 0 && toIndex < steps.size()) {
            PresetStep step = steps.remove(fromIndex);
            steps.add(toIndex, step);
            this.modifiedAt = System.currentTimeMillis();
        }
    }
    
    private void calculateTotalDuration() {
        int maxEndTime = 0;
        for (PresetStep step : steps) {
            int stepEndTime = step.getStartTimeMs() + step.getDurationMs();
            if (stepEndTime > maxEndTime) {
                maxEndTime = stepEndTime;
            }
        }
        this.totalDurationMs = maxEndTime;
    }
    
    public boolean isEmpty() {
        return steps == null || steps.isEmpty();
    }
    
    public int getStepCount() {
        return steps != null ? steps.size() : 0;
    }
    
    public PresetStep getStep(int index) {
        if (index >= 0 && index < steps.size()) {
            return steps.get(index);
        }
        return null;
    }
    
    public List<PresetStep> getStepsAtTime(int timeMs) {
        List<PresetStep> activeSteps = new ArrayList<>();
        for (PresetStep step : steps) {
            if (step.isActiveAtTime(timeMs)) {
                activeSteps.add(step);
            }
        }
        return activeSteps;
    }
    
    public String getFormattedDuration() {
        int seconds = totalDurationMs / 1000;
        int minutes = seconds / 60;
        seconds = seconds % 60;
        
        if (minutes > 0) {
            return String.format("%dm %ds", minutes, seconds);
        } else {
            return String.format("%ds", seconds);
        }
    }
    
    public Preset copy() {
        Preset copy = new Preset();
        copy.name = this.name + " (Copy)";
        copy.description = this.description;
        copy.category = this.category;
        copy.isActive = this.isActive;
        
        for (PresetStep step : this.steps) {
            copy.addStep(step.copy());
        }
        
        return copy;
    }
    
    @Override
    public String toString() {
        return "Preset{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", category=" + category +
                ", steps=" + steps.size() +
                ", duration=" + getFormattedDuration() +
                '}';
    }
}
