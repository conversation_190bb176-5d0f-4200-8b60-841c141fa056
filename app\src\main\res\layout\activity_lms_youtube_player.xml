<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black">

    <!-- YouTube WebView -->
    <WebView
        android:id="@+id/youtube_webview"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerInParent="true" />

    <!-- Loading Progress Bar -->
    <ProgressBar
        android:id="@+id/progress_bar"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="match_parent"
        android:layout_height="4dp"
        android:layout_alignParentTop="true"
        android:progressTint="@color/robot_primary"
        android:visibility="gone" />

    <!-- Close Button Overlay -->
    <ImageButton
        android:id="@+id/close_button"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:layout_margin="16dp"
        android:background="@drawable/close_button_background"
        android:contentDescription="Close video"
        android:src="@drawable/ic_close_white"
        android:scaleType="centerInside"
        android:padding="12dp" />

</RelativeLayout>
