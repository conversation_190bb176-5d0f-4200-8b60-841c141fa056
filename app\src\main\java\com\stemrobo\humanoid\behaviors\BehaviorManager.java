package com.stemrobo.humanoid.behaviors;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.stemrobo.humanoid.communication.ESP32CommunicationManager;
import com.stemrobo.humanoid.vision.PersonDetector;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;

public class BehaviorManager {
    private static final String TAG = "BehaviorManager";
    
    private Context context;
    private ESP32CommunicationManager communicationManager;
    private Handler mainHandler;
    private Random random;
    
    // Behavior states
    private boolean isFollowingPerson = false;
    private boolean isIdleBehaviorEnabled = true;
    private boolean isGreetingMode = false;
    private boolean isInteractionMode = false;
    
    // Timing
    private long lastInteractionTime = 0;
    private long lastIdleBehaviorTime = 0;
    private static final long IDLE_BEHAVIOR_INTERVAL = 30000; // 30 seconds
    private static final long INTERACTION_TIMEOUT = 60000; // 1 minute
    
    // Person following
    private PersonDetector.Detection lastPersonDetection;
    private int consecutiveNoDetections = 0;
    private static final int MAX_NO_DETECTIONS = 10;
    
    // Behavior callbacks
    public interface BehaviorCallback {
        void onBehaviorStarted(String behaviorName);
        void onBehaviorCompleted(String behaviorName);
        void onSpeechRequired(String text);
    }
    
    private BehaviorCallback callback;
    
    public BehaviorManager(Context context, ESP32CommunicationManager communicationManager) {
        this.context = context;
        this.communicationManager = communicationManager;
        this.mainHandler = new Handler(Looper.getMainLooper());
        this.random = new Random();
        
        startBehaviorLoop();
    }
    
    public void setBehaviorCallback(BehaviorCallback callback) {
        this.callback = callback;
    }
    
    // Person following behavior
    public void startPersonFollowing() {
        isFollowingPerson = true;
        consecutiveNoDetections = 0;
        
        if (callback != null) {
            callback.onBehaviorStarted("person_following");
            callback.onSpeechRequired("I'll follow you now. Please stay in my view.");
        }
        
        Log.d(TAG, "Person following started");
    }
    
    public void stopPersonFollowing() {
        isFollowingPerson = false;
        
        // Stop any current movement
        sendStopCommand();
        
        if (callback != null) {
            callback.onBehaviorCompleted("person_following");
            callback.onSpeechRequired("I've stopped following. How can I help you?");
        }
        
        Log.d(TAG, "Person following stopped");
    }
    
    public void processPersonDetection(PersonDetector.Detection detection) {
        if (!isFollowingPerson) return;
        
        if (detection != null) {
            lastPersonDetection = detection;
            consecutiveNoDetections = 0;
            
            // Calculate following behavior
            executeFollowingBehavior(detection);
        } else {
            consecutiveNoDetections++;
            
            if (consecutiveNoDetections >= MAX_NO_DETECTIONS) {
                // Lost person, stop following
                stopPersonFollowing();
            }
        }
    }
    
    private void executeFollowingBehavior(PersonDetector.Detection detection) {
        // Get image dimensions (assuming 640x480 for calculation)
        int imageWidth = 640;
        int imageHeight = 480;
        
        // Calculate center offset
        int centerX = imageWidth / 2;
        int centerY = imageHeight / 2;
        float deltaX = detection.centerX - centerX;
        float deltaY = detection.centerY - centerY;
        
        // Tolerance zones
        int horizontalTolerance = imageWidth / 8;
        int verticalTolerance = imageHeight / 8;
        int distanceTolerance = imageWidth / 6;
        
        // Calculate person size (distance estimation)
        // Since we simplified Detection class, use confidence as size indicator
        float normalizedSize = detection.confidence * 0.1f; // Simulate size based on confidence
        
        // Determine action based on person position and size
        if (Math.abs(deltaX) > horizontalTolerance) {
            // Person is off-center horizontally
            if (deltaX > 0) {
                sendTurnCommand("turn_right", 100, 500);
            } else {
                sendTurnCommand("turn_left", 100, 500);
            }
        } else if (normalizedSize < 0.05) {
            // Person is too far, move forward
            sendMovementCommand("forward", 120, 1000);
        } else if (normalizedSize > 0.15) {
            // Person is too close, move backward
            sendMovementCommand("backward", 100, 800);
        } else {
            // Person is in good position, just track with head
            trackPersonWithHead(detection, imageWidth, imageHeight);
        }
    }
    
    private void trackPersonWithHead(PersonDetector.Detection detection, int imageWidth, int imageHeight) {
        // Calculate head movement to track person
        int centerX = imageWidth / 2;
        int centerY = imageHeight / 2;
        
        // Convert pixel offset to servo angles
        float panOffset = (detection.centerX - centerX) / (float) imageWidth * 60; // ±30 degrees
        float tiltOffset = (detection.centerY - centerY) / (float) imageHeight * 40; // ±20 degrees
        
        int panAngle = (int) (90 + panOffset); // Center is 90 degrees
        int tiltAngle = (int) (90 - tiltOffset); // Invert Y axis
        
        // Constrain angles
        panAngle = Math.max(30, Math.min(150, panAngle));
        tiltAngle = Math.max(60, Math.min(120, tiltAngle));
        
        sendHeadMovement(panAngle, tiltAngle);
    }
    
    // Greeting behavior
    public void executeGreetingBehavior() {
        if (isGreetingMode) return;
        
        isGreetingMode = true;
        lastInteractionTime = System.currentTimeMillis();
        
        if (callback != null) {
            callback.onBehaviorStarted("greeting");
        }
        
        // Execute greeting sequence
        mainHandler.post(() -> {
            // Wave gesture
            sendGestureCommand("wave");
            
            // Speak greeting
            if (callback != null) {
                String[] greetings = {
                    "Hello there! I'm STEM Robot. Nice to meet you!",
                    "Hi! Welcome to the world of robotics!",
                    "Greetings! I'm excited to interact with you!",
                    "Hello! Ready to explore some cool robot features?"
                };
                String greeting = greetings[random.nextInt(greetings.length)];
                callback.onSpeechRequired(greeting);
            }
            
            // Head movement
            mainHandler.postDelayed(() -> {
                sendHeadMovement(45, 90); // Look left
                mainHandler.postDelayed(() -> {
                    sendHeadMovement(135, 90); // Look right
                    mainHandler.postDelayed(() -> {
                        sendHeadMovement(90, 90); // Center
                        isGreetingMode = false;
                        
                        if (callback != null) {
                            callback.onBehaviorCompleted("greeting");
                        }
                    }, 1000);
                }, 1000);
            }, 2000);
        });
        
        Log.d(TAG, "Greeting behavior executed");
    }
    
    // Idle behaviors
    private void startBehaviorLoop() {
        mainHandler.post(new Runnable() {
            @Override
            public void run() {
                if (isIdleBehaviorEnabled && !isFollowingPerson && !isGreetingMode) {
                    long currentTime = System.currentTimeMillis();
                    
                    if (currentTime - lastIdleBehaviorTime > IDLE_BEHAVIOR_INTERVAL) {
                        executeRandomIdleBehavior();
                        lastIdleBehaviorTime = currentTime;
                    }
                }
                
                // Schedule next check
                mainHandler.postDelayed(this, 5000); // Check every 5 seconds
            }
        });
    }
    
    private void executeRandomIdleBehavior() {
        String[] idleBehaviors = {
            "head_scan",
            "subtle_gesture",
            "status_check",
            "breathing_simulation"
        };
        
        String behavior = idleBehaviors[random.nextInt(idleBehaviors.length)];
        executeIdleBehavior(behavior);
    }
    
    private void executeIdleBehavior(String behavior) {
        if (callback != null) {
            callback.onBehaviorStarted("idle_" + behavior);
        }
        
        switch (behavior) {
            case "head_scan":
                executeHeadScanBehavior();
                break;
            case "subtle_gesture":
                executeSubtleGesture();
                break;
            case "status_check":
                executeStatusCheck();
                break;
            case "breathing_simulation":
                executeBreathingSimulation();
                break;
        }
        
        Log.d(TAG, "Idle behavior executed: " + behavior);
    }
    
    private void executeHeadScanBehavior() {
        // Slow head scan left to right
        sendHeadMovement(60, 90);
        mainHandler.postDelayed(() -> {
            sendHeadMovement(120, 90);
            mainHandler.postDelayed(() -> {
                sendHeadMovement(90, 90); // Return to center
            }, 2000);
        }, 2000);
    }
    
    private void executeSubtleGesture() {
        // Small arm movement
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("servo", "right_shoulder");
        parameters.put("angle", 75);
        
        communicationManager.sendServoCommand("set_angle", parameters);
        
        mainHandler.postDelayed(() -> {
            parameters.put("angle", 90); // Return to rest
            communicationManager.sendServoCommand("set_angle", parameters);
        }, 1500);
    }
    
    private void executeStatusCheck() {
        if (callback != null) {
            callback.onSpeechRequired("All systems operational. Standing by for commands.");
        }
    }
    
    private void executeBreathingSimulation() {
        // Subtle chest movement simulation (could control LED brightness)
        // This is a placeholder for future LED integration
        Log.d(TAG, "Breathing simulation (placeholder for LED control)");
    }
    
    // Interaction behaviors
    public void startInteractionMode() {
        isInteractionMode = true;
        lastInteractionTime = System.currentTimeMillis();
        
        if (callback != null) {
            callback.onBehaviorStarted("interaction_mode");
            callback.onSpeechRequired("I'm ready to interact! What would you like me to do?");
        }
        
        // Look at user
        sendHeadMovement(90, 75); // Slightly down to look at user
    }
    
    public void endInteractionMode() {
        isInteractionMode = false;
        
        if (callback != null) {
            callback.onBehaviorCompleted("interaction_mode");
            callback.onSpeechRequired("Thank you for the interaction! I'll be here if you need me.");
        }
        
        // Return to neutral position
        sendHeadMovement(90, 90);
        sendGestureCommand("rest");
    }
    
    // Emergency behaviors
    public void executeEmergencyStop() {
        isFollowingPerson = false;
        isGreetingMode = false;
        isInteractionMode = false;
        
        sendStopCommand();
        sendGestureCommand("rest");
        sendHeadMovement(90, 90);
        
        if (callback != null) {
            callback.onSpeechRequired("Emergency stop activated. All movement halted.");
        }
        
        Log.d(TAG, "Emergency stop executed");
    }
    
    // Communication helpers
    private void sendMovementCommand(String direction, int speed, int duration) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("direction", direction);
        parameters.put("speed", speed);
        parameters.put("duration", duration);
        
        communicationManager.sendMotorCommand("move", parameters);
    }
    
    private void sendTurnCommand(String direction, int speed, int duration) {
        sendMovementCommand(direction, speed, duration);
    }
    
    private void sendStopCommand() {
        communicationManager.sendMotorCommand("stop", new HashMap<>());
    }
    
    private void sendGestureCommand(String gesture) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("gesture", gesture);
        
        communicationManager.sendServoCommand("gesture", parameters);
    }
    
    private void sendHeadMovement(int pan, int tilt) {
        Map<String, Object> panParams = new HashMap<>();
        panParams.put("servo", "head_pan");
        panParams.put("angle", pan);
        
        Map<String, Object> tiltParams = new HashMap<>();
        tiltParams.put("servo", "head_tilt");
        tiltParams.put("angle", tilt);
        
        communicationManager.sendServoCommand("set_angle", panParams);
        communicationManager.sendServoCommand("set_angle", tiltParams);
    }
    
    // Configuration
    public void setIdleBehaviorEnabled(boolean enabled) {
        this.isIdleBehaviorEnabled = enabled;
    }
    
    public boolean isFollowingPerson() {
        return isFollowingPerson;
    }
    
    public boolean isInInteractionMode() {
        return isInteractionMode;
    }
    
    public void cleanup() {
        isFollowingPerson = false;
        isIdleBehaviorEnabled = false;
        isGreetingMode = false;
        isInteractionMode = false;
        
        if (mainHandler != null) {
            mainHandler.removeCallbacksAndMessages(null);
        }
    }
}
